#pragma once

namespace calibration_algo::eol::camera {

// enum class CalibrationErrorCode {
//   NONE = 0,
//   SUCCESS = 1,
//   UNKONOWN_ERROR = 2,

//   INPUT_IMAGES_INCOMPLETE = 20000,
//   INPUT_INTRINSIC_INCORRECT = 20001,
//   INPUT_CALIBROOM_POINTS_INCORRECT = 20002,
//   INPUT_CAMERA_STRUCTURE_INCORRECT = 20003,

//   OUTPUT_IO_WRITE_PARAM_ERROR = 30000,
//   OUTPUT_IO_WRITE_LOG_ERROR = 30001,
//   OUTPUT_RESULT_REPOJECT_BEYOND_THRESHLOD_ERROR = 30002,
//   OUTPUT_RESULT_STRUCTURE_DIFF_BEYOND_THRESHLOD_ERROR = 30003,

//   PROCEDURE_TIMECONSUMPTION_BEYOND_THRESHLOD_ERROR = 40000,
//   PROCEDURE_DETECT_ABNORMAL_ERROR = 40001,
// };

/*
// E171 error code
enum class CalibrationErrorCode {
  NO_ERROR = 0,
  INVALID_PARAMETER = 1,
  INSUFFICIENT_ILLUMINATION_CALIBRATION_TARGET_OR_WINDSHIELD_DIRTY = 2, // not used
  NO_CALIBRATION_TARGET_FOUND = 3,  // not used
  ANGLES_OUT_OF_TOLERANCE_RANGE = 4,
  INVALID_VIN = 5,  // not used
  TOO_MANY_TARGETS_FOUND = 6, // not used
  INVALID_IMAGE = 7,
  CALIBRATION_ERROR_OUT_OF_RANGE = 8,
  NO_CALIBRATION = 9,
  DETECT_ERROR = 10,
  TIME_OUT = 11, // not used
  COMMON_INTERNAL_CALIBRATION_ERROR = 12,
};
*/

// P177 error code
enum class CalibrationErrorCode_ori {
  NO_ERROR = 0,
  CONFIGURATION_FILE_ERROR = 1,
  INVALID_PARAMETER = 2,
  INVALID_IMAGE = 3,
  NO_CALIBRATION_TARGET_FOUND = 4,                   // -> need to add
  TOO_MUCH_OR_LITTLE_CALIBRATION_TARGETS_FOUND = 5,  // -> need to add
  PITCH_ANGLE_OUT_OF_RANGE = 6,
  YAW_ANGLE_OUT_OF_RANGE = 7,
  ROLL_ANGLE_OUT_OF_RANGE = 8,
  CAMERA_POSITION_NOT_CORRECT = 9,
  REPROJECTION_OUT_OF_RANGE = 10,
  NO_CALIBRATION = 11,                     // -> need to add
  TIME_OUT = 12,                           // not used
  COMMON_INTERNAL_CALIBRATION_ERROR = 13,  // -> need to add
};

}  // namespace calibration_algo::eol::camera
