#pragma once

#include <yaml-cpp/yaml.h>

#include <Eigen/Dense>
#include <filesystem>
#include <fstream>
#include <opencv2/core/eigen.hpp>

#include "calibration_algo/common/extrinsic.hpp"
#include "calibration_algo/common/intrinsic.hpp"
#include "calibration_algo/common/pose.hpp"
#include "calibration_algo/eol/camera/eol_config.hpp"
#include "glog/logging.h"
#include "opencv2/opencv.hpp"

namespace calibration_algo::eol::camera {

inline void get_cameras_intrinsic_from_yaml(const YAML::Node& node, common::CameraIntrinsicParam& param,
                                            double cy_offset = 0.0) {
  param.fx = node["focal_len_x"].as<double>();
  param.fy = node["focal_len_y"].as<double>();
  param.cx = node["optical_center_x"].as<double>();
  param.cy = node["optical_center_y"].as<double>() - cy_offset;

  //   param.distortion_model = node["distortion_model"].as<std::string>();

  int distortion_type = node["distortion_type"].as<int>();
  if (distortion_type == 3) {
    param.distortion_type = common::CameraDistortionType::PINHOLE;
  } else if (distortion_type == 1) {
    param.distortion_type = common::CameraDistortionType::FISHEYE;
  } else {
    LOG(FATAL) << "Unknown distortion type " << distortion_type << std::endl;
    exit(-1);
  }

  if (param.distortion_type == common::CameraDistortionType::PINHOLE) {
    param.k1 = node["k_1"].as<double>();
    param.k2 = node["k_2"].as<double>();
    param.k3 = node["k_3"].as<double>();
    param.k4 = node["k_4"].as<double>();
    param.k5 = node["k_5"].as<double>();
    param.k6 = node["k_6"].as<double>();
    param.p1 = node["p_1"].as<double>();
    param.p2 = node["p_2"].as<double>();
  } else if (param.distortion_type == common::CameraDistortionType::FISHEYE) {
    param.k1 = node["k_1"].as<double>();
    param.k2 = node["k_2"].as<double>();
    param.k3 = node["k_3"].as<double>();
    param.k4 = node["k_4"].as<double>();
    // param.k5 = node["k_5"].as<double>();
    // param.k6 = node["k_6"].as<double>();
  }
}

inline void get_cameras_intrinsic_from_yaml(const std::string& yaml_path, const EOLParams& eol_config,
                                            std::map<std::string, common::CameraIntrinsicParam>& cameras_intrinsic) {
  YAML::Node soca_cam_intrinsics = YAML::LoadFile(yaml_path);
  common::CameraIntrinsicParam param;
  std::map<std::string, std::string> geely2mach_camera_mappings = eol_config.geely2mach_camera_mappings;

  for (const auto& geely_cam : geely2mach_camera_mappings) {
    if (soca_cam_intrinsics[geely_cam.first] && soca_cam_intrinsics[geely_cam.first].IsDefined()) {
      try {
        // get_cameras_intrinsic_from_yaml(soca_cam_intrinsics[geely_cam.first], param, (geely_cam.first ==
        // "cam_front_120" || geely_cam.first == "cam_front_30") ? 0 : 98);
        get_cameras_intrinsic_from_yaml(soca_cam_intrinsics[geely_cam.first], param, 0);
      } catch (const YAML::InvalidNode& e) {
        LOG(FATAL) << "Invalid node for camera intrinsic " << geely_cam.first << ": " << e.what() << std::endl;
        exit(-1);
      }
      cameras_intrinsic.insert(std::make_pair(geely_cam.second, param));
    } else {
      LOG(FATAL) << "Missing camera intrinsic for " << geely_cam.first << std::endl;
      exit(-1);
    }
  }

  LOG(INFO) << "Successfully loaded camera intrinsic from yaml!" << std::endl;

  // for (const auto& mapping : camera_mappings) {
  //     if (soca_cam_intrinsics[mapping.first] && soca_cam_intrinsics[mapping.first].IsDefined()) {
  //         try {
  //             get_cameras_intrinsic_from_yaml(soca_cam_intrinsics[mapping.first], param, (mapping.first ==
  //             "cam_front_120" || mapping.first == "cam_front_30") ? 0 : 98);

  //         } catch (const YAML::InvalidNode& e) {
  //             LOG(ERROR) << "Invalid node for camera intrinsic " << mapping.first << ": " << e.what() << std::endl;
  //         }
  //         cameras_intrinsic.insert(std::make_pair(mapping.second, param));
  //     } else {
  //         LOG(ERROR) << "Missing camera intrinsic for " << mapping.first << std::endl;
  //     }

  // }

  /*
  // cam_front_120
  get_cameras_intrinsic_from_yaml(soca_cam_intrinsics["cam_front"], param, 0);
  cameras_intrinsic.insert(std::make_pair("cam_front_120", param));
  // cam_front_left_100
  get_cameras_intrinsic_from_yaml(soca_cam_intrinsics["cam_side_left_front"], param, 98);
  cameras_intrinsic.insert(std::make_pair("cam_front_left_100", param));
  // cam_front_right_100
  get_cameras_intrinsic_from_yaml(soca_cam_intrinsics["cam_side_right_front"], param, 98);
  cameras_intrinsic.insert(std::make_pair("cam_front_right_100", param));
  // cam_back_100
  get_cameras_intrinsic_from_yaml(soca_cam_intrinsics["cam_back"], param, 98);
  cameras_intrinsic.insert(std::make_pair("cam_back_100", param));
  // cam_back_left_100
  get_cameras_intrinsic_from_yaml(soca_cam_intrinsics["cam_side_left_back"], param, 98);
  cameras_intrinsic.insert(std::make_pair("cam_back_left_100", param));
  // cam_back_right_100
  get_cameras_intrinsic_from_yaml(soca_cam_intrinsics["cam_side_right_back"], param, 98);
  cameras_intrinsic.insert(std::make_pair("cam_back_right_100", param));
  */
}

inline void get_cameras_intrinsic_from_default(std::map<std::string, common::CameraIntrinsicParam>& cameras_intrinsic) {
  common::CameraIntrinsicParam intrinsic;
  //   common::CameraIntrinsicDistortion D;

  intrinsic.fx = 2438.35;
  intrinsic.fy = 2438.41;
  intrinsic.cx = 1920.75;
  intrinsic.cy = 1083.12;
  intrinsic.k1 = 6.87107;
  intrinsic.k2 = 3.30114;
  intrinsic.k3 = 0.104093;
  intrinsic.k4 = 7.55728;
  intrinsic.k5 = 7.53527;
  intrinsic.k6 = 1.01264;
  intrinsic.p1 = 0.000013;
  intrinsic.p2 = 0.00001;

  intrinsic.distortion_type = common::CameraDistortionType::PINHOLE;
  cameras_intrinsic.insert(std::make_pair("cam_front_120", intrinsic));

  intrinsic.fx = 1051.8480224609375;
  intrinsic.fy = 1049.867919921875;
  intrinsic.cx = 956.8788452148438;
  intrinsic.cy = 542.1884155273438;
  intrinsic.k1 = -0.016109803691506386;
  intrinsic.k2 = -0.00036199306487105787;
  intrinsic.k3 = -0.0002262494817841798;
  intrinsic.k4 = 0.004851216450333595;
  intrinsic.k5 = 0;
  intrinsic.k6 = 0;
  intrinsic.p1 = 0;
  intrinsic.p2 = 0;
  intrinsic.distortion_type = common::CameraDistortionType::FISHEYE;
  cameras_intrinsic.insert(std::make_pair("cam_front_left_100", intrinsic));
  cameras_intrinsic.insert(std::make_pair("cam_front_right_100", intrinsic));
  cameras_intrinsic.insert(std::make_pair("cam_back_100", intrinsic));
  cameras_intrinsic.insert(std::make_pair("cam_back_left_100", intrinsic));
  cameras_intrinsic.insert(std::make_pair("cam_back_right_100", intrinsic));
}

inline void get_cameras_extrinsic_from_yaml(std::map<std::string, common::ExtrinsicPose>& cameras_structure,
                                            const EOLParams& eol_config, const std::string& extrinsic_config_path) {
  YAML::Node config = YAML::LoadFile(extrinsic_config_path);

  YAML::Node car_config = config[eol_config.vehicle_model];

  for (const auto& mapping : eol_config.geely2mach_camera_mappings) {
    common::Point position;
    position.x = car_config[mapping.second]["x"].as<double>();
    position.y = car_config[mapping.second]["y"].as<double>();
    position.z = car_config[mapping.second]["z"].as<double>();
    // common::RotationAngle rot_zyx;
    // rot_zyx.rot_x = car_config[mapping.second]["rot_x"].as<double>();
    // rot_zyx.rot_y = car_config[mapping.second]["rot_y"].as<double>();
    // rot_zyx.rot_z = car_config[mapping.second]["rot_z"].as<double>();

    double RotX = car_config[mapping.second]["RotX"].as<double>();
    double RotY = car_config[mapping.second]["RotY"].as<double>();
    double RotZ = car_config[mapping.second]["RotZ"].as<double>();
    // Intrinsic Rotation
    Eigen::Quaterniond q = Eigen::AngleAxisd(RotZ * M_PI / 180.0, Eigen::Vector3d::UnitZ()) *
                           Eigen::AngleAxisd(RotY * M_PI / 180.0, Eigen::Vector3d::UnitY()) *
                           Eigen::AngleAxisd(RotX * M_PI / 180.0, Eigen::Vector3d::UnitX());

    common::ExtrinsicPose extrinsic_structure;
    extrinsic_structure.position = position;
    // extrinsic_structure.rotation_angle = rot_zyx;
    extrinsic_structure.orientation.x = q.x();
    extrinsic_structure.orientation.y = q.y();
    extrinsic_structure.orientation.z = q.z();
    extrinsic_structure.orientation.w = q.w();
    cameras_structure.insert(std::make_pair(mapping.second, extrinsic_structure));

    // Convert quaternion back to Euler angles (RotZ, RotY, RotX)
    Eigen::Vector3d euler_angles = q.toRotationMatrix().eulerAngles(2, 1, 0);
    double yaw = euler_angles[0] * 180.0 / M_PI;
    double pitch = euler_angles[1] * 180.0 / M_PI;
    double roll = euler_angles[2] * 180.0 / M_PI;

    LOG(INFO) << mapping.second << " StructureParam.orientation -> (RotZ, RotY, RotX): " << yaw << ", " << pitch << ", "
              << roll;
  }

  /*
  YAML::Node car_config = config[car_name];
  Point position;
  position.x = car_config["cam_front"]["x"].as<double>();
  position.y = car_config["cam_front"]["y"].as<double>();
  position.z = car_config["cam_front"]["z"].as<double>();
  RotationAngle rpy;
  rpy.yaw = car_config["cam_front"]["yaw"].as<double>();
  rpy.pitch = car_config["cam_front"]["pitch"].as<double>();
  rpy.roll = car_config["cam_front"]["roll"].as<double>();
  ExtrinsicPose extrinsic_structure;
  extrinsic_structure.position = position;
  extrinsic_structure.rotation_angle = rpy;
  cameras_structure.insert(std::make_pair("cam_front_120", extrinsic_structure));

  position.x = car_config["cam_side_left_front"]["x"].as<double>();
  position.y = car_config["cam_side_left_front"]["y"].as<double>();
  position.z = car_config["cam_side_left_front"]["z"].as<double>();
  rpy.yaw = car_config["cam_side_left_front"]["yaw"].as<double>();
  rpy.pitch = car_config["cam_side_left_front"]["pitch"].as<double>();
  rpy.roll = car_config["cam_side_left_front"]["roll"].as<double>();
  extrinsic_structure.position = position;
  extrinsic_structure.rotation_angle = rpy;
  cameras_structure.insert(std::make_pair("cam_front_left_100", extrinsic_structure));

  position.x = car_config["cam_side_right_front"]["x"].as<double>();
  position.y = car_config["cam_side_right_front"]["y"].as<double>();
  position.z = car_config["cam_side_right_front"]["z"].as<double>();
  rpy.yaw = car_config["cam_side_right_front"]["yaw"].as<double>();
  rpy.pitch = car_config["cam_side_right_front"]["pitch"].as<double>();
  rpy.roll = car_config["cam_side_right_front"]["roll"].as<double>();
  extrinsic_structure.position = position;
  extrinsic_structure.rotation_angle = rpy;
  cameras_structure.insert(std::make_pair("cam_front_right_100", extrinsic_structure));

  position.x = car_config["cam_back"]["x"].as<double>();
  position.y = car_config["cam_back"]["y"].as<double>();
  position.z = car_config["cam_back"]["z"].as<double>();
  rpy.yaw = car_config["cam_back"]["yaw"].as<double>();
  rpy.pitch = car_config["cam_back"]["pitch"].as<double>();
  rpy.roll = car_config["cam_back"]["roll"].as<double>();
  extrinsic_structure.position = position;
  extrinsic_structure.rotation_angle = rpy;
  cameras_structure.insert(std::make_pair("cam_back_100", extrinsic_structure));

  position.x = car_config["cam_side_left_back"]["x"].as<double>();
  position.y = car_config["cam_side_left_back"]["y"].as<double>();
  position.z = car_config["cam_side_left_back"]["z"].as<double>();
  rpy.yaw = car_config["cam_side_left_back"]["yaw"].as<double>();
  rpy.pitch = car_config["cam_side_left_back"]["pitch"].as<double>();
  rpy.roll = car_config["cam_side_left_back"]["roll"].as<double>();
  extrinsic_structure.position = position;
  extrinsic_structure.rotation_angle = rpy;
  cameras_structure.insert(std::make_pair("cam_back_left_100", extrinsic_structure));

  position.x = car_config["cam_side_right_back"]["x"].as<double>();
  position.y = car_config["cam_side_right_back"]["y"].as<double>();
  position.z = car_config["cam_side_right_back"]["z"].as<double>();
  rpy.yaw = car_config["cam_side_right_back"]["yaw"].as<double>();
  rpy.pitch = car_config["cam_side_right_back"]["pitch"].as<double>();
  rpy.roll = car_config["cam_side_right_back"]["roll"].as<double>();
  extrinsic_structure.position = position;
  extrinsic_structure.rotation_angle = rpy;
  cameras_structure.insert(std::make_pair("cam_back_right_100", extrinsic_structure));
  */
}

inline void load_calibroom_points(const std::string& calib_room_txt_path,
                                  std::map<std::string, common::Point>& calibroom_points_map) {
  std::ifstream map_f;
  map_f.open(calib_room_txt_path);
  std::string line;
  while (std::getline(map_f, line)) {
    std::string point_name;
    common::Point point;
    std::istringstream s(line);
    s >> point_name;
    s >> point.x;
    s >> point.y;
    s >> point.z;
    calibroom_points_map.insert(std::make_pair(point_name, point));
  }
}

inline bool overlap_tag(const std::vector<cv::Point2f>& corners0, const std::vector<cv::Point2f>& corners1,
                        const double overlap_tag_threshold = 200) {
  float cx0 = (corners0[0].x + corners0[1].x + corners0[2].x + corners0[3].x) / 4.;
  float cy0 = (corners0[0].y + corners0[1].y + corners0[2].y + corners0[3].y) / 4.;
  float cx1 = (corners1[0].x + corners1[1].x + corners1[2].x + corners1[3].x) / 4.;
  float cy1 = (corners1[0].y + corners1[1].y + corners1[2].y + corners1[3].y) / 4.;
  float distance = sqrt(pow(cx0 - cx1, 2) + pow(cy0 - cy1, 2));
  // LOG(ERROR) << "distance in pixels " << distance << std::endl;
  if (distance > overlap_tag_threshold) {
    LOG(ERROR) << "overlap_tag distance in pixels: " << distance << std::endl;
  }

  return distance < overlap_tag_threshold;
}

inline double polygon_area(const std::vector<cv::Point2f>& corners) {
  double area = 0.0;
  int n = corners.size();
  for (int i = 0; i < n; ++i) {
    int j = (i + 1) % n;
    area += corners[i].x * corners[j].y - corners[j].x * corners[i].y;
  }
  return std::abs(area) / 2.0;
}

inline double polygon_intersection_area(const std::vector<cv::Point2f>& poly1, const std::vector<cv::Point2f>& poly2) {
  std::vector<cv::Point2f> intersection;
  cv::intersectConvexConvex(poly1, poly2, intersection);  // calculate intersection
  return polygon_area(intersection);
}

inline bool overlap_tag_iou(const std::vector<cv::Point2f>& corners0, const std::vector<cv::Point2f>& corners1) {
  double area0 = polygon_area(corners0);
  double area1 = polygon_area(corners1);
  double intersection_area = polygon_intersection_area(corners0, corners1);
  double union_area = area0 + area1 - intersection_area;
  double iou = intersection_area / union_area;
  // LOG(INFO) << "iou: " << iou << std::endl;
  return iou;
}

inline void add_f_D_calibroom_points_offset(std::map<std::string, common::CameraIntrinsicParam>& cameras_intrinsic,
                                            std::map<std::string, common::Point>& calibroom_points_map,
                                            const EOLParams& eol_config) {
  // Add Gaussian noise to focal lengths
  std::random_device rd;
  std::mt19937 gen(rd());

  // print cameras_intrinsic after adding noise
  LOG(INFO) << "\n";
  LOG(INFO) << "cameras_intrinsic after adding noise: " << std::endl;

  for (auto& [camera_name, param] : cameras_intrinsic) {
    if (std::any_of(eol_config.simulation_error_config.f_offset.begin(),
                    eol_config.simulation_error_config.f_offset.end(), [](double offset) { return offset > 1e-9; })) {
      LOG(INFO) << "  Original fx, fy for " << camera_name << ": " << param.fx << ", " << param.fy;
      std::normal_distribution<> fx_dist(param.fx, eol_config.simulation_error_config.f_offset[0] * param.fx / 3);
      std::normal_distribution<> fy_dist(param.fy, eol_config.simulation_error_config.f_offset[1] * param.fy / 3);
      param.fx = fx_dist(gen);
      param.fy = fy_dist(gen);
      LOG(INFO) << "  Updated fx, fy for " << camera_name << " : " << param.fx << ", " << param.fy;
    }

    // Add Gaussian noise to distortion coefficients
    if (std::any_of(eol_config.simulation_error_config.D_offset.begin(),
                    eol_config.simulation_error_config.D_offset.end(), [](double offset) { return offset > 1e-9; })) {
      LOG(INFO) << "  Original distortion coefficients for " << camera_name << ": " << param.k1 << ", " << param.k2
                << ", " << param.k3 << ", " << param.k4 << ", " << param.k5 << ", " << param.k6 << ", " << param.p1
                << ", " << param.p2;

      std::normal_distribution<> k1_dist(param.k1, eol_config.simulation_error_config.D_offset[0] / 3);
      std::normal_distribution<> k2_dist(param.k2, eol_config.simulation_error_config.D_offset[1] / 3);
      std::normal_distribution<> k3_dist(param.k3, eol_config.simulation_error_config.D_offset[2] / 3);
      std::normal_distribution<> k4_dist(param.k4, eol_config.simulation_error_config.D_offset[3] / 3);
      param.k1 = k1_dist(gen);
      param.k2 = k2_dist(gen);
      param.k3 = k3_dist(gen);
      param.k4 = k4_dist(gen);

      if (!std::isnan(param.k5)) {
        std::normal_distribution<> k5_dist(param.k5, eol_config.simulation_error_config.D_offset[4] / 3);
        param.k5 = k5_dist(gen);
      }
      if (!std::isnan(param.k6)) {
        std::normal_distribution<> k6_dist(param.k6, eol_config.simulation_error_config.D_offset[5] / 3);
        param.k6 = k6_dist(gen);
      }
      if (!std::isnan(param.p1)) {
        std::normal_distribution<> p1_dist(param.p1, eol_config.simulation_error_config.D_offset[6] / 3);
        param.p1 = p1_dist(gen);
      }
      if (!std::isnan(param.p2)) {
        std::normal_distribution<> p2_dist(param.p2, eol_config.simulation_error_config.D_offset[7] / 3);
        param.p2 = p2_dist(gen);
      }

      LOG(INFO) << "  Updated distortion coefficients for " << camera_name << " : " << param.k1 << ", " << param.k2
                << ", " << param.k3 << ", " << param.k4 << ", " << param.k5 << ", " << param.k6 << ", " << param.p1
                << ", " << param.p2;
    }
  }

  // Add Gaussian noise to calibroom points
  if (std::any_of(eol_config.simulation_error_config.calibroom_points_offset.begin(),
                  eol_config.simulation_error_config.calibroom_points_offset.end(),
                  [](double offset) { return offset > 1e-9; })) {
    LOG(INFO) << std::endl << std::endl << "calibroom_points_map after adding noise: " << std::endl;
    for (auto& [point_name, point] : calibroom_points_map) {
      std::normal_distribution<> x_dist(point.x, eol_config.simulation_error_config.calibroom_points_offset[0] / 3);
      std::normal_distribution<> y_dist(point.y, eol_config.simulation_error_config.calibroom_points_offset[1] / 3);
      std::normal_distribution<> z_dist(point.z, eol_config.simulation_error_config.calibroom_points_offset[2] / 3);

      double new_x = x_dist(gen);
      double new_y = y_dist(gen);
      double new_z = z_dist(gen);

      LOG(INFO) << "  Original point " << point_name << ": " << point.x << ", " << point.y << ", " << point.z;
      LOG(INFO) << "  Updated point " << point_name << " : " << new_x << ", " << new_y << ", " << new_z;

      point.x = new_x;
      point.y = new_y;
      point.z = new_z;
    }
  }
}

inline calibration_algo::eol::camera::SimulationErrorConfig readSimulationErrorConfig(const YAML::Node& debugNode) {
  calibration_algo::eol::camera::SimulationErrorConfig config;
  try {
    // 读取 test_num
    config.test_num = debugNode["test_num"].as<int>();

    // 读取 f_offset
    config.f_offset = debugNode["f_offset"].as<std::vector<double>>();

    // D_offset
    config.D_offset = debugNode["D_offset"].as<std::vector<double>>();

    // img_detect_points_offset
    config.img_detect_points_offset = debugNode["img_detect_points_offset"].as<std::vector<double>>();

    // calibroom_points_offset
    config.calibroom_points_offset = debugNode["calibroom_points_offset"].as<std::vector<double>>();

    // random_mask_num
    config.random_mask_num = debugNode["random_mask_num"].as<int>();

    // random_mask_AprilTag
    YAML::Node aprilTagNode = debugNode["random_mask_AprilTag"];
    for (YAML::const_iterator it = aprilTagNode.begin(); it != aprilTagNode.end(); ++it) {
      std::string cameraName = it->first.as<std::string>();
      std::vector<int> tagIds = it->second.as<std::vector<int>>();
      config.random_mask_AprilTag[cameraName] = tagIds;
    }

    config.output_simulation_error_name = debugNode["output_simulation_error_name"].as<std::string>();

  } catch (const YAML::Exception& e) {
    std::cerr << "YAML 解析错误: " << e.what() << std::endl;
  }
  return config;
}

inline void get_config_from_yaml(const std::string& eol_config_path, EOLParams& eol_config) {
  if (!std::filesystem::exists(eol_config_path)) {
    LOG(FATAL) << "no eol_config yaml in " << eol_config_path << " !" << std::endl;
    exit(-1);
  }
  YAML::Node conf = YAML::LoadFile(eol_config_path);

  bool is_right = true;
  bool is_print;

  if (conf["is_print_eol_config"]) {
    is_print = conf["is_print_eol_config"].as<bool>();
  } else {
    LOG(ERROR) << "no is_print_eol_config in config yaml! Default is false." << std::endl;
    is_print = false;
  }

  // check vehicle_model
  if (conf["vehicle_model"]) {
    eol_config.vehicle_model = conf["vehicle_model"].as<std::string>();

    // print
    if (is_print) {
      LOG(INFO) << "vehicle_model: " << eol_config.vehicle_model << std::endl;
      LOG(INFO) << std::endl;
    }
  } else {
    LOG(FATAL) << "no vehicle_model in config yaml!" << std::endl;
    is_right = false;
  }

  // is_debug & debug config
  if (conf["is_debug"]) {
    eol_config.is_debug = conf["is_debug"].as<bool>();
    if (is_print) {
      LOG(INFO) << "is_debug: " << (eol_config.is_debug ? "true" : "false") << std::endl;
    }
  } else {
    LOG(ERROR) << "no is_debug in config yaml! Default is false." << std::endl;
    eol_config.is_debug = false;
  }

  if (conf["is_simulation_error"]) {
    eol_config.is_simulation_error = conf["is_simulation_error"].as<bool>();
    if (is_print) {
      LOG(INFO) << "is_simulation_error: " << (eol_config.is_simulation_error ? "true" : "false") << std::endl;
    }
  } else {
    LOG(ERROR) << "no is_simulation_error in config yaml! Default is false." << std::endl;
    eol_config.is_simulation_error = false;
  }
  if (conf["simulation_error_config"]) {
    eol_config.simulation_error_config = readSimulationErrorConfig(conf["simulation_error_config"]);

    if (is_print) {
      eol_config.simulation_error_config.printConfig();
    }
  }

  if (conf["tmp_output_dir"]) {
    eol_config.tmp_output_dir = conf["tmp_output_dir"].as<std::string>();
    // // makedir
    // if (eol_config.is_debug &&
    // !std::filesystem::exists(eol_config.tmp_output_dir)) {
    //   std::filesystem::create_directory(eol_config.tmp_output_dir);
    // }

    // print
    if (is_print) {
      LOG(INFO) << "tmp_output_dir: " << eol_config.tmp_output_dir << std::endl;
      LOG(INFO) << std::endl;
    }

  } else {
    LOG(FATAL) << "no tmp_output_dir in config yaml!" << std::endl;
    is_right = false;
  }

  // check AprilTag_code
  if (conf["AprilTag_code"]) {
    std::string tag_code_str = conf["AprilTag_code"].as<std::string>();

    // print
    if (is_print) {
      LOG(INFO) << "AprilTag_code: " << tag_code_str << std::endl;
      LOG(INFO) << std::endl;
    }

    if (tag_code_str == "16h5") {
      eol_config.m_tagCodes = AprilTags::tagCodes16h5;
    } else if (tag_code_str == "25h7") {
      eol_config.m_tagCodes = AprilTags::tagCodes25h7;
    } else if (tag_code_str == "25h9") {
      eol_config.m_tagCodes = AprilTags::tagCodes25h9;
    } else if (tag_code_str == "36h9") {
      eol_config.m_tagCodes = AprilTags::tagCodes36h9;
    } else if (tag_code_str == "36h11") {
      eol_config.m_tagCodes = AprilTags::tagCodes36h11;
    } else {
      LOG(FATAL) << "Unknown tag code: " << tag_code_str;
      is_right = false;
    }

  } else {
    LOG(FATAL) << "no AprilTag_code in config yaml!" << std::endl;
    is_right = false;
  }

  // check geely2mach_camera_mappings
  if (conf["geely2mach_camera_mappings"]) {
    for (const auto& item : conf["geely2mach_camera_mappings"]) {
      std::string key = item.first.as<std::string>();
      std::string value = item.second.as<std::string>();
      eol_config.geely2mach_camera_mappings[key] = value;
    }

    // print geely2mach_camera_mappings
    if (is_print) {
      LOG(INFO) << "geely2mach_camera_mappings:" << std::endl;
      for (const auto& geely2mach_camera_mapping : eol_config.geely2mach_camera_mappings) {
        LOG(INFO) << "  " << geely2mach_camera_mapping.first << " -> " << geely2mach_camera_mapping.second << std::endl;
      }
      LOG(INFO) << std::endl;
    }

    // camera_names
    for (const auto& mapping : eol_config.geely2mach_camera_mappings) {
      eol_config.cam_names.push_back(mapping.second);
    }
    // print cam_names
    if (is_print) {
      LOG(INFO) << "cam_names:" << std::endl;
      for (const auto& cam_name : eol_config.cam_names) {
        LOG(INFO) << "  " << cam_name << std::endl;
      }
      LOG(INFO) << std::endl;
    }

    // cam_prior_names
    for (const auto& cam_name : eol_config.cam_names) {
      eol_config.cam_prior_names.push_back(cam_name + "_prior");
    }
    // print cam_prior_names
    if (is_print) {
      LOG(INFO) << "cam_prior_names:" << std::endl;
      for (const auto& cam_prior_name : eol_config.cam_prior_names) {
        LOG(INFO) << "  " << cam_prior_name << std::endl;
      }
      LOG(INFO) << std::endl;
    }

  } else {
    LOG(FATAL) << "no geely2mach_camera_mappings in config yaml!" << std::endl;
    is_right = false;
  }

  // check overlap_tag_threshold
  if (conf["overlap_tag_threshold"]) {
    eol_config.overlap_tag_threshold = conf["overlap_tag_threshold"].as<std::map<std::string, double>>();

    // print
    if (is_print) {
      LOG(INFO) << "overlap_tag_threshold:" << std::endl;
      for (const auto& overlap_tag : eol_config.overlap_tag_threshold) {
        LOG(INFO) << "  " << overlap_tag.first << ": " << overlap_tag.second << std::endl;
      }
      LOG(INFO) << std::endl;
    }
  } else {
    LOG(FATAL) << "no overlap_tag_threshold in config yaml!" << std::endl;
    is_right = false;
  }

  // check apriltag_detection_img_scale
  if (conf["apriltag_detection_img_scale"]) {
    eol_config.apriltag_detection_img_scale =
        conf["apriltag_detection_img_scale"].as<std::map<std::string, std::vector<double>>>();

    // print
    if (is_print) {
      LOG(INFO) << "apriltag_detection_img_scale:" << std::endl;
      for (const auto& apriltag_detection_img_scale : eol_config.apriltag_detection_img_scale) {
        std::string output;
        for (const auto& scale : apriltag_detection_img_scale.second) {
          output += std::to_string(scale) + " ";
        }
        LOG(INFO) << "  " << apriltag_detection_img_scale.first << ": [" << output << "]" << std::endl;
      }
      LOG(INFO) << std::endl;
    }
  } else {
    LOG(FATAL) << "no apriltag_detection_img_scale in config yaml!" << std::endl;
    is_right = false;
  }

  if (conf["is_use_SubPix"]) {
    eol_config.is_use_SubPix = conf["is_use_SubPix"].as<std::map<std::string, bool>>();

    // print
    if (is_print) {
      LOG(INFO) << "is_use_SubPix:" << std::endl;
      for (const auto& is_use_SubPix : eol_config.is_use_SubPix) {
        LOG(INFO) << "  " << is_use_SubPix.first << ": " << is_use_SubPix.second;
      }
      LOG(INFO) << std::endl;
    } else {
      LOG(FATAL) << "no is_use_SubPix in config yaml!" << std::endl;
      is_right = false;
    }
  }

  // check ceres_cfg
  std::vector<std::string> ceres_keys = {"structure_translation_prior_weight_x", "structure_translation_prior_weight_y",
                                         "structure_translation_prior_weight_z", "report_angle_diff_threshold",
                                         "report_trans_diff_threshold"};
  if (conf["ceres_cfg"]) {
    for (const auto& key : ceres_keys) {
      if (conf["ceres_cfg"][key]) {
        eol_config.ceres_cfg[key] = conf["ceres_cfg"][key].as<double>();
      } else {
        LOG(FATAL) << "missing " << key << " in ceres_cfg in config yaml!" << std::endl;
        is_right = false;
      }
    }

    // print ceres_cfg
    if (is_print) {
      LOG(INFO) << "ceres_cfg:" << std::endl;
      for (const auto& ceres_cfg : eol_config.ceres_cfg) {
        LOG(INFO) << "  " << ceres_cfg.first << ": " << ceres_cfg.second << std::endl;
      }
      LOG(INFO) << std::endl;
    }

  } else {
    LOG(FATAL) << "no ceres_cfg in config yaml!" << std::endl;
    is_right = false;
  }

  // check reproj_error_threshold
  if (conf["reproj_error_threshold"]) {
    for (const auto& key : eol_config.cam_names) {
      if (conf["reproj_error_threshold"][key]) {
        eol_config.reproj_error_threshold[key] = conf["reproj_error_threshold"][key].as<double>();
      } else {
        LOG(FATAL) << "missing " << key << " in reproj_error_threshold in config yaml!" << std::endl;
        is_right = false;
      }
    }

    // print reproj_error_threshold
    if (is_print) {
      LOG(INFO) << "reproj_error_threshold:" << std::endl;
      for (const auto& dat : eol_config.reproj_error_threshold) {
        LOG(INFO) << "  " << dat.first << ": " << dat.second << std::endl;
      }
      LOG(INFO) << std::endl;
    }

  } else {
    LOG(FATAL) << "no reproj_error_threshold in config yaml!" << std::endl;
    is_right = false;
  }

  // check check_direction_full_ids
  if (conf["check_direction_full_ids"]) {
    for (const auto& item : conf["check_direction_full_ids"]) {
      std::string key = item.first.as<std::string>();
      std::vector<int> values = item.second.as<std::vector<int>>();
      eol_config.check_direction_full_ids[key] = values;
    }

    // print check_direction_full_ids
    if (is_print) {
      LOG(INFO) << "check_direction_full_ids:" << std::endl;
      for (const auto& check_direction_full_id : eol_config.check_direction_full_ids) {
        std::string output;
        for (const auto& value : check_direction_full_id.second) {
          output += std::to_string(value) + " ";
        }
        LOG(INFO) << "  " << check_direction_full_id.first << ": " << output << std::endl;
      }
      LOG(INFO) << std::endl;
    }
  } else {
    LOG(FATAL) << "no check_direction_full_ids in config yaml!" << std::endl;
    is_right = false;
  }

  // check check_direction_necessary_ids
  if (conf["check_direction_necessary_ids"]) {
    for (const auto& item : conf["check_direction_necessary_ids"]) {
      std::string key = item.first.as<std::string>();
      std::vector<std::vector<int>> values = item.second.as<std::vector<std::vector<int>>>();
      eol_config.check_direction_necessary_ids[key] = values;
    }

    // print check_direction_necessary_ids
    if (is_print) {
      LOG(INFO) << "check_direction_necessary_ids:" << std::endl;
      for (const auto& check_direction_necessary_id : eol_config.check_direction_necessary_ids) {
        std::string output;

        for (const auto& value : check_direction_necessary_id.second) {
          output += "[ ";
          for (const auto& v : value) {
            output += std::to_string(v) + " ";
          }
          output += "] ";
        }

        LOG(INFO) << "  " << check_direction_necessary_id.first << ": " << output << std::endl;
      }
      LOG(INFO) << std::endl;
    }

  } else {
    LOG(FATAL) << "no check_direction_necessary_ids in config yaml!" << std::endl;
    is_right = false;
  }

  /*
  if (conf["structure_translation_prior_weight_x"])
      eol_config.structure_translation_prior_weight_x = conf["structure_translation_prior_weight_x"].as<double>();
  else
      LOG(ERROR) << "no structure_translation_prior_weight_x in config yaml!" << std::endl;
  if (conf["structure_translation_prior_weight_y"])
      eol_config.structure_translation_prior_weight_y = conf["structure_translation_prior_weight_y"].as<double>();
  else
      LOG(ERROR) << "no structure_translation_prior_weight_y in config yaml!" << std::endl;
  if (conf["structure_translation_prior_weight_z"])
      eol_config.structure_translation_prior_weight_z = conf["structure_translation_prior_weight_z"].as<double>();
  else
      LOG(ERROR) << "no structure_translation_prior_weight_z in config yaml!" << std::endl;
  */

  // check camera_prior_info, rfu_tf_cam
  if (conf["camera_prior_info"]) {
    Eigen::Quaterniond q;
    Eigen::Vector3d t;
    std::vector<std::string> keys = {"q_w", "q_x", "q_y", "q_z", "t_x", "t_y", "t_z"};

    for (const auto& item : conf["camera_prior_info"]) {
      std::string cam_name = item.first.as<std::string>();
      YAML::Node cam_node = item.second;

      bool complete = true;
      for (const auto& key : keys) {
        if (!cam_node[key]) {
          LOG(FATAL) << cam_name << " in camera_prior_info not complete! missing " << key;
          complete = false;
          break;
        }
      }
      if (complete) {
        // rfu_tf_cam
        q = Eigen::Quaterniond(cam_node["q_w"].as<double>(), cam_node["q_x"].as<double>(), cam_node["q_y"].as<double>(),
                               cam_node["q_z"].as<double>());
        t = Eigen::Vector3d(cam_node["t_x"].as<double>(), cam_node["t_y"].as<double>(), cam_node["t_z"].as<double>());

        // // transform rfu_tf_cam to cam_tf_rfu
        Eigen::Matrix4d rfu_tf_cam = Eigen::Matrix4d::Identity();
        rfu_tf_cam.block<3, 3>(0, 0) = q.toRotationMatrix();
        rfu_tf_cam.block<3, 1>(0, 3) = t;

        Eigen::Matrix4d cam_tf_rfu = rfu_tf_cam.inverse();
        q = Eigen::Quaterniond(cam_tf_rfu.block<3, 3>(0, 0));
        t = cam_tf_rfu.block<3, 1>(0, 3);

        std::string cam_key = cam_name.substr(0, cam_name.find("_prior"));
        eol_config.prior_q.insert(std::make_pair(cam_key, q));
        eol_config.prior_t.insert(std::make_pair(cam_key, t));
      }
    }

    if (is_print) {
      LOG(INFO) << "camera_prior_info:" << std::endl;
      for (const auto& q : eol_config.prior_q) {
        LOG(INFO) << "  " << q.first << " quaternion: " << q.second.coeffs().w() << " " << q.second.coeffs().x() << " "
                  << q.second.coeffs().y() << " " << q.second.coeffs().z() << std::endl;
      }
      for (const auto& t : eol_config.prior_t) {
        LOG(INFO) << "  " << t.first << " translation: " << t.second.x() << ", " << t.second.y() << ", " << t.second.z()
                  << std::endl;
      }
      LOG(INFO) << std::endl;
    }

  } else {
    LOG(FATAL) << "no camera_prior_info in config yaml!" << std::endl;
    is_right = false;
  }

  if (is_right) {
    LOG(INFO) << "config yaml loaded successfully!" << std::endl;
  } else {
    LOG(FATAL) << "eol_config.yaml loaded failed!" << std::endl;
    exit(-1);
  }

  // // print eol_config to test
  // std::cout << "Configuration Parameters:" << std::endl;
  // std::cout << "vehicle_model: " << eol_config.vehicle_model << std::endl;
  // std::cout << "structure_translation_prior_weight_x: " << eol_config.structure_translation_prior_weight_x <<
  // std::endl; std::cout << "structure_translation_prior_weight_y: " << eol_config.structure_translation_prior_weight_y
  // << std::endl; std::cout << "structure_translation_prior_weight_z: " <<
  // eol_config.structure_translation_prior_weight_z << std::endl; for (const auto& q : eol_config.prior_q) {
  //     std::cout << q.first << " quaternion: " << q.second.coeffs().transpose() << std::endl;
  // }
  // for (const auto& t : eol_config.prior_t) {
  //     std::cout << t.first << " translation: " << t.second.transpose() << std::endl;
  // }

  /*
  Eigen::Quaterniond q;
  Eigen::Vector3d t;
  if (conf["cam_front_prior"])
  {
      YAML::Node front_node = conf["cam_front_prior"];
      if (!front_node["q_w"] || !front_node["q_x"] || !front_node["q_y"] || !front_node["q_z"]
            || !front_node["p_x"] || !front_node["p_y"] || !front_node["p_z"])
      {
          LOG(ERROR) << "cam_front_prior in config yaml not complete! missing elements";
      }
      else
      {
          q.w() = front_node["q_w"].as<double>();
          q.x() = front_node["q_x"].as<double>();
          q.y() = front_node["q_y"].as<double>();
          q.z() = front_node["q_z"].as<double>();
          t[0] = front_node["p_x"].as<double>();
          t[1] = front_node["p_y"].as<double>();
          t[2] = front_node["p_z"].as<double>();
          eol_config.prior_q.insert(std::make_pair("cam_front_120", q));
          eol_config.prior_t.insert(std::make_pair("cam_front_120", t));
      }
  }
  else
      LOG(ERROR) << "missing cam_front_prior in config yaml!" << std::endl;

  if (conf["cam_front_left_prior"])
  {
      YAML::Node front_left_node = conf["cam_front_left_prior"];
      if (!front_left_node["q_w"] || !front_left_node["q_x"] || !front_left_node["q_y"] || !front_left_node["q_z"]
            || !front_left_node["p_x"] || !front_left_node["p_y"] || !front_left_node["p_z"])
      {
          LOG(ERROR) << "cam_front_left_prior in config yaml not complete! missing elements";
      }
      else
      {
          q.w() = front_left_node["q_w"].as<double>();
          q.x() = front_left_node["q_x"].as<double>();
          q.y() = front_left_node["q_y"].as<double>();
          q.z() = front_left_node["q_z"].as<double>();
          t[0] = front_left_node["p_x"].as<double>();
          t[1] = front_left_node["p_y"].as<double>();
          t[2] = front_left_node["p_z"].as<double>();
          eol_config.prior_q.insert(std::make_pair("cam_front_left_100", q));
          eol_config.prior_t.insert(std::make_pair("cam_front_left_100", t));
      }
  }
  else
      LOG(ERROR) << "missing cam_front_left_prior in config yaml!" << std::endl;

  if (conf["cam_front_right_prior"])
  {
      YAML::Node front_right_node = conf["cam_front_right_prior"];
      if (!front_right_node["q_w"] || !front_right_node["q_x"] || !front_right_node["q_y"] || !front_right_node["q_z"]
            || !front_right_node["p_x"] || !front_right_node["p_y"] || !front_right_node["p_z"])
      {
          LOG(ERROR) << "cam_front_right_prior in config yaml not complete! missing elements";
      }
      else
      {
          q.w() = front_right_node["q_w"].as<double>();
          q.x() = front_right_node["q_x"].as<double>();
          q.y() = front_right_node["q_y"].as<double>();
          q.z() = front_right_node["q_z"].as<double>();
          t[0] = front_right_node["p_x"].as<double>();
          t[1] = front_right_node["p_y"].as<double>();
          t[2] = front_right_node["p_z"].as<double>();
          eol_config.prior_q.insert(std::make_pair("cam_front_right_100", q));
          eol_config.prior_t.insert(std::make_pair("cam_front_right_100", t));
      }
  }
  else
      LOG(ERROR) << "missing cam_front_right_prior in config yaml!" << std::endl;

  if (conf["cam_back_left_prior"])
  {
      YAML::Node back_left_node = conf["cam_back_left_prior"];
      if (!back_left_node["q_w"] || !back_left_node["q_x"] || !back_left_node["q_y"] || !back_left_node["q_z"]
            || !back_left_node["p_x"] || !back_left_node["p_y"] || !back_left_node["p_z"])
      {
          LOG(ERROR) << "cam_back_left_prior in config yaml not complete! missing elements";
      }
      else
      {
          q.w() = back_left_node["q_w"].as<double>();
          q.x() = back_left_node["q_x"].as<double>();
          q.y() = back_left_node["q_y"].as<double>();
          q.z() = back_left_node["q_z"].as<double>();
          t[0] = back_left_node["p_x"].as<double>();
          t[1] = back_left_node["p_y"].as<double>();
          t[2] = back_left_node["p_z"].as<double>();
          eol_config.prior_q.insert(std::make_pair("cam_back_left_100", q));
          eol_config.prior_t.insert(std::make_pair("cam_back_left_100", t));
      }
  }
  else
      LOG(ERROR) << "missing cam_back_left_prior in config yaml!" << std::endl;

  if (conf["cam_back_right_prior"])
  {
      YAML::Node back_right_node = conf["cam_back_right_prior"];
      if (!back_right_node["q_w"] || !back_right_node["q_x"] || !back_right_node["q_y"] || !back_right_node["q_z"]
            || !back_right_node["p_x"] || !back_right_node["p_y"] || !back_right_node["p_z"])
      {
          LOG(ERROR) << "cam_back_right_prior in config yaml not complete! missing elements";
      }
      else
      {
          q.w() = back_right_node["q_w"].as<double>();
          q.x() = back_right_node["q_x"].as<double>();
          q.y() = back_right_node["q_y"].as<double>();
          q.z() = back_right_node["q_z"].as<double>();
          t[0] = back_right_node["p_x"].as<double>();
          t[1] = back_right_node["p_y"].as<double>();
          t[2] = back_right_node["p_z"].as<double>();
          eol_config.prior_q.insert(std::make_pair("cam_back_right_100", q));
          eol_config.prior_t.insert(std::make_pair("cam_back_right_100", t));
      }
  }
  else
      LOG(ERROR) << "missing cam_back_right_prior in config yaml!" << std::endl;

  if (conf["cam_back_prior"])
  {
      YAML::Node back_node = conf["cam_back_prior"];
      if (!back_node["q_w"] || !back_node["q_x"] || !back_node["q_y"] || !back_node["q_z"]
            || !back_node["p_x"] || !back_node["p_y"] || !back_node["p_z"])
      {
          LOG(ERROR) << "cam_back_prior in config yaml not complete! missing elements";
      }
      else
      {
          q.w() = back_node["q_w"].as<double>();
          q.x() = back_node["q_x"].as<double>();
          q.y() = back_node["q_y"].as<double>();
          q.z() = back_node["q_z"].as<double>();
          t[0] = back_node["p_x"].as<double>();
          t[1] = back_node["p_y"].as<double>();
          t[2] = back_node["p_z"].as<double>();
          eol_config.prior_q.insert(std::make_pair("cam_back_100", q));
          eol_config.prior_t.insert(std::make_pair("cam_back_100", t));
      }
  }
  else
      LOG(ERROR) << "missing cam_back_prior in config yaml!" << std::endl;

}

*/
}

inline void get_image_files_from_dir(const std::string& img_dir, const EOLParams& eol_config,
                                     std::vector<std::pair<std::string, std::string>>& image_files) {
  std::string img_path;
  for (const auto& mapping : eol_config.geely2mach_camera_mappings) {
    img_path = img_dir + "/" + mapping.second + ".jpg";
    if (!std::filesystem::exists(img_path)) {
      img_path = img_dir + "/" + mapping.second + ".png";
    }

    if (!std::filesystem::exists(img_path)) {
      LOG(FATAL) << "Image file does not exist: " << img_path << std::endl;
      exit(-1);
    }

    image_files.push_back(std::make_pair(mapping.second, img_path));
  }

  for (const auto& image_file : image_files) {
    LOG(INFO) << image_file.first << " image path: " << image_file.second;
  }
  LOG(INFO) << "image files loaded successfully!" << std::endl;
}

}  // namespace calibration_algo::eol::camera