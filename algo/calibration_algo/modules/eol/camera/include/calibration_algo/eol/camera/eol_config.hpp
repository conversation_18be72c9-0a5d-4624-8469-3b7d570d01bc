#pragma once

#include <glog/logging.h>

#include <Eigen/Eigen>
#include <map>
#include <numeric>
#include <string>
#include <vector>

#include "AprilTags/TagDetector.h"

namespace calibration_algo::eol::camera {

// Define the SimulationErrorConfig struct
struct SimulationErrorConfig {
  int test_num;
  std::vector<double> f_offset;
  std::vector<double> D_offset;
  std::vector<double> img_detect_points_offset;
  std::vector<double> calibroom_points_offset;
  int random_mask_num;
  std::map<std::string, std::vector<int>> random_mask_AprilTag;
  std::string output_simulation_error_name = "simulation_error.yaml";

  // Constructor to initialize struct members
  SimulationErrorConfig() : test_num(0), random_mask_num(0) {}

  void printConfig() const {
    LOG(INFO) << "SimulationErrorConfig: ";
    LOG(INFO) << "  test_num: " << test_num;
    LOG(INFO) << "  f_offset: ["
              << std::accumulate(f_offset.begin(), f_offset.end(), std::string(),
                                 [](const std::string& a, double b) {
                                   return a.empty() ? std::to_string(b) : a + " " + std::to_string(b);
                                 })
              << "]";

    LOG(INFO) << "  D_offset: ["
              << std::accumulate(D_offset.begin(), D_offset.end(), std::string(),
                                 [](const std::string& a, double b) {
                                   return a.empty() ? std::to_string(b) : a + " " + std::to_string(b);
                                 })
              << "]";

    LOG(INFO) << "  img_detect_points_offset: ["
              << std::accumulate(img_detect_points_offset.begin(), img_detect_points_offset.end(), std::string(),
                                 [](const std::string& a, double b) {
                                   return a.empty() ? std::to_string(b) : a + " " + std::to_string(b);
                                 })
              << "]";

    LOG(INFO) << "  calibroom_points_offset: ["
              << std::accumulate(calibroom_points_offset.begin(), calibroom_points_offset.end(), std::string(),
                                 [](const std::string& a, double b) {
                                   return a.empty() ? std::to_string(b) : a + " " + std::to_string(b);
                                 })
              << "]";

    LOG(INFO) << "  random_mask_num: " << random_mask_num;

    LOG(INFO) << "  random_mask_AprilTag: ";
    for (const auto& pair : random_mask_AprilTag) {
      LOG(INFO) << "    " << pair.first << ": ["
                << std::accumulate(pair.second.begin(), pair.second.end(), std::string(),
                                   [](const std::string& a, int b) {
                                     return a.empty() ? std::to_string(b) : a + " " + std::to_string(b);
                                   })
                << "]";
    }

    LOG(INFO) << "  output_simulation_error_name: " << output_simulation_error_name;
  }
};

struct EOLParams {
  std::string vehicle_model;  // Z10 or P177, determine whose structure params were to load

  std::string tmp_output_dir;  // tmp output dir for debug

  bool is_debug = false;
  bool is_simulation_error = false;
  calibration_algo::eol::camera::SimulationErrorConfig simulation_error_config;

  // double structure_translation_prior_weight_x = 0.5;
  // double structure_translation_prior_weight_y = 0.5;
  // double structure_translation_prior_weight_z = 0.3;

  // AprilTag_code
  AprilTags::TagCodes m_tagCodes{AprilTags::tagCodes36h11};  // or any other valid TagCodes enum value

  // overlap_tag_threshold for filter: pixel distance
  std::map<std::string, double> overlap_tag_threshold;

  std::map<std::string, std::vector<double>> apriltag_detection_img_scale;

  // is_use_SubPix
  std::map<std::string, bool> is_use_SubPix;

  // ceres config
  std::map<std::string, double> ceres_cfg;
  std::map<std::string, double> reproj_error_threshold;

  // check_direction_necessary_ids, check_direction_full_ids
  std::map<std::string, std::vector<int>> check_direction_full_ids;
  std::map<std::string, std::vector<std::vector<int>>> check_direction_necessary_ids;

  // geely2mach_camera_mappings
  std::map<std::string, std::string> geely2mach_camera_mappings;

  std::vector<std::string> cam_names;
  std::vector<std::string> cam_prior_names;

  // prior rotation & translation for each camera
  std::map<std::string, Eigen::Quaterniond> prior_q;
  std::map<std::string, Eigen::Vector3d> prior_t;
};

}  // namespace calibration_algo::eol::camera
