#pragma once

#include "calibration_algo/eol/camera/extrinsic_calib/extrinsic_estimator.h"

namespace calibration_algo::eol::camera {

struct ImageCostFunctor {
  cv::Point3f object_point_;
  cv::Point3f ray_point_;
  ImageCostFunctor(const cv::Point3f objpoint, const cv::Point3f raypoint)
      : object_point_(objpoint), ray_point_(raypoint) {}

  template <typename T>
  bool operator()(const T *const r_vec, const T *const t_vec, T *residual) const {
    T w_pt[3];
    T c_pt[3];
    w_pt[0] = T(object_point_.x);
    w_pt[1] = T(object_point_.y);
    w_pt[2] = T(object_point_.z);

    // world -> camera
    ceres::AngleAxisRotatePoint(r_vec, w_pt, c_pt);
    c_pt[0] += t_vec[0];
    c_pt[1] += t_vec[1];
    c_pt[2] += t_vec[2];

    // normalize
    T c_pt_normalized[3], ray_pt_normalized[3];

    T c_pt_norm = ceres::sqrt(ceres::pow(c_pt[0], 2) + ceres::pow(c_pt[1], 2) + ceres::pow(c_pt[2], 2));

    c_pt_normalized[0] = c_pt[0] / c_pt_norm;
    c_pt_normalized[1] = c_pt[1] / c_pt_norm;
    c_pt_normalized[2] = c_pt[2] / c_pt_norm;

    T ray_pt_norm =
        ceres::sqrt(ceres::pow(T(ray_point_.x), 2) + ceres::pow(T(ray_point_.y), 2) + ceres::pow(T(ray_point_.z), 2));
    ray_pt_normalized[0] = T(ray_point_.x) / ray_pt_norm;
    ray_pt_normalized[1] = T(ray_point_.y) / ray_pt_norm;
    ray_pt_normalized[2] = T(ray_point_.z) / ray_pt_norm;

    residual[0] = c_pt_normalized[0] - ray_pt_normalized[0];
    residual[1] = c_pt_normalized[1] - ray_pt_normalized[1];
    residual[2] = c_pt_normalized[2] - ray_pt_normalized[2];

    return true;
  }

  static ceres::CostFunction *create(const cv::Point3f objpoint, const cv::Point3f raypoint) {
    return new ceres::AutoDiffCostFunction<ImageCostFunctor, 3, 3, 3>(new ImageCostFunctor(objpoint, raypoint));
  }
};

struct TranslationPriorFunctor {
  Eigen::Vector3d rfu_tf_cam_structure_t_;
  double weight_x_, weight_y_, weight_z_;
  TranslationPriorFunctor(const Eigen::Vector3d &rfu_tf_cam_structure_t, const double weight_x, const double weight_y,
                          const double weight_z)
      : rfu_tf_cam_structure_t_(rfu_tf_cam_structure_t),
        weight_x_(weight_x),
        weight_y_(weight_y),
        weight_z_(weight_z) {}

  template <typename T>
  bool operator()(const T *const r_vec, const T *const t_vec, T *residual) const {
    // cam_tf_rfu: r_vec, t_vec

    // transform cam_tf_rfu to rfu_tf_cam
    T rfu_tf_cam_r_vec[3], rfu_tf_cam_t_vec[3];
    rfu_tf_cam_r_vec[0] = -r_vec[0];
    rfu_tf_cam_r_vec[1] = -r_vec[1];
    rfu_tf_cam_r_vec[2] = -r_vec[2];

    T negative_t_vec[3];
    negative_t_vec[0] = -t_vec[0];
    negative_t_vec[1] = -t_vec[1];
    negative_t_vec[2] = -t_vec[2];
    ceres::AngleAxisRotatePoint(rfu_tf_cam_r_vec, negative_t_vec, rfu_tf_cam_t_vec);

    residual[0] = T(rfu_tf_cam_structure_t_[0]) - rfu_tf_cam_t_vec[0];
    residual[1] = T(rfu_tf_cam_structure_t_[1]) - rfu_tf_cam_t_vec[1];
    residual[2] = T(rfu_tf_cam_structure_t_[2]) - rfu_tf_cam_t_vec[2];

    residual[0] *= T(weight_x_);
    residual[1] *= T(weight_y_);
    residual[2] *= T(weight_z_);

    return true;
  }

  static ceres::CostFunction *create(const Eigen::Vector3d &rfu_tf_cam_structure_t, const double weight_x,
                                     const double weight_y, const double weight_z) {
    return new ceres::AutoDiffCostFunction<TranslationPriorFunctor, 3, 3, 3>(
        new TranslationPriorFunctor(rfu_tf_cam_structure_t, weight_x, weight_y, weight_z));
  }
};

}  // namespace calibration_algo::eol::camera
