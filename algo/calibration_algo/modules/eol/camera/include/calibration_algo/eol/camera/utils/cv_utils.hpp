#pragma once

#include <Eigen/Dense>
#include <opencv2/core/eigen.hpp>

#include "opencv2/opencv.hpp"

namespace calibration_algo::eol::camera {

inline cv::Mat SkewSymmetric(const cv::Mat& v) {
  // 判断输入向量是否合法（必须是3x1的列向量）
  CV_Assert(v.rows == 3 && v.cols == 1);

  // 生成反对称矩阵
  return (cv::Mat_<double>(3, 3) << 0, -v.at<double>(2), v.at<double>(1), v.at<double>(2), 0, -v.at<double>(0),
          -v.at<double>(1), v.at<double>(0), 0);
}

inline cv::Mat SE3d_SkewSymmetric(const Eigen::Quaterniond& q, const Eigen::Vector3d& t) {
  cv::Mat matrix, trans;
  cv::eigen2cv(q.toRotationMatrix(), matrix);
  cv::eigen2cv(t, trans);

  return SkewSymmetric(trans) * matrix;
}

inline cv::Mat quat2axisangle(const double& x, const double& y, const double& z, const double& w) {
  cv::Mat axisangle;
  axisangle.create(3, 1, CV_64F);
  Eigen::Quaterniond q(w, x, y, z);
  Eigen::AngleAxisd newAngleAxis(q);
  axisangle.at<double>(0, 0) = newAngleAxis.axis()[0] * newAngleAxis.angle();
  axisangle.at<double>(1, 0) = newAngleAxis.axis()[1] * newAngleAxis.angle();
  axisangle.at<double>(2, 0) = newAngleAxis.axis()[2] * newAngleAxis.angle();
  return axisangle;
}

}  // namespace calibration_algo::eol::camera