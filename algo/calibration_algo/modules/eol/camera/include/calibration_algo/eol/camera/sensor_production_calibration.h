#pragma once

#include <map>
#include <memory>
#include <opencv2/core/core.hpp>

#include "calibration_algo/common/extrinsic.hpp"
#include "calibration_algo/common/intrinsic.hpp"
#include "calibration_algo/common/result.hpp"
#include "opencv2/opencv.hpp"

namespace calibration_algo::eol::camera {

class SensorProductionCalibration {
 public:
  enum CalibrationType {
    CAMERA_EOL_CALIBRATION,
    CAMERA_AFTERSALES_CALIBRATION,
    CAMERA_DYNAMIC_CALIBRATION,
    DEFAULT_CALIBRATION
  };

  class CalibrationParam {
   public:
    CalibrationParam() {}
    CalibrationParam(CalibrationType calibration_type, const std::string& log_output_path,
                     const std::map<std::string, common::CameraIntrinsicParam>& cameras_intrinsic,
                     const std::map<std::string, common::ExtrinsicPose>& cameras_structure);

   protected:
    std::string log_output_path_;
    CalibrationType calibration_type_;
    std::map<std::string, common::CameraIntrinsicParam> cameras_intrinsic_;
    std::map<std::string, common::ExtrinsicPose> cameras_structure_;
  };

  class SensorInput {
   public:
    SensorInput(CalibrationType calibration_type, const uint64_t timestamp);

   protected:
    CalibrationType calibration_type_;
    uint64_t timestamp_;
  };

  class CalibrationOutput {
   public:
    CalibrationOutput(CalibrationType calibration_type);
    std::map<std::string, common::CalibrationResult> sensors_calibration_result;
    uint32_t calibration_process;
    uint32_t calibration_status;  //
    uint32_t calibration_mode;
    uint32_t calibration_error_code;
    uint64_t calibration_timestamp;
    uint32_t reserved[7];

   protected:
    CalibrationType calibration_type_;
  };

  SensorProductionCalibration();
  ~SensorProductionCalibration();
};

typedef std::shared_ptr<SensorProductionCalibration> SensorProductionCalibrationPtr;
}  // namespace calibration_algo::eol::camera
