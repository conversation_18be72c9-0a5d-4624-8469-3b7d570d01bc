#pragma once

#include "calibration_algo/eol/camera/eol_config.hpp"
#include "calibration_algo/eol/camera/extrinsic_calib/extrinsic_estimator.h"
#include "calibration_algo/eol/camera/sensor_production_calibration.h"

namespace calibration_algo::eol::camera {

class CameraEOLCalibration : public SensorProductionCalibration {
 public:
  class CalibrationParam : public SensorProductionCalibration::CalibrationParam {
   public:
    CalibrationParam() {}
    CalibrationParam(CalibrationType calibraiton_type, const std::string& log_output_path,
                     const std::map<std::string, common::CameraIntrinsicParam>& cameras_intrinsic,
                     const std::map<std::string, common::ExtrinsicPose>& cameras_structure, const EOLParams& eol_config,
                     const std::map<std::string, common::Point>& calibroom_points_map);
    std::map<std::string, common::Point> GetCalibroomPointsMap() { return calibroom_points_map_; }
    std::map<std::string, common::CameraIntrinsicParam> GetCamerasIntrinsic() { return cameras_intrinsic_; }
    std::map<std::string, common::ExtrinsicPose> GetCameraStructureParameters() { return cameras_structure_; }
    EOLParams GetEOLParam() { return eol_config_; }
    std::string GetLogOutputPath() { return log_output_path_; }
    CalibrationType GetCalibrationType() { return calibration_type_; }

   protected:
    std::map<std::string, common::Point> calibroom_points_map_;
    EOLParams eol_config_;
  };

  class SensorInput : public SensorProductionCalibration::SensorInput {
   public:
    SensorInput(CalibrationType calibraiton_type, const uint64_t timestamp, const cv::Mat& image);
    cv::Mat GetImage() { return image_.clone(); }

   protected:
    cv::Mat image_;
  };

  CameraEOLCalibration();
  ~CameraEOLCalibration();
  int Inita(CalibrationParam a) { return -1; }

  int Init(const CalibrationParam& calibration_param);

  int RunCalibration(const std::map<std::string, SensorInput>& sensors_input,
                     std::map<std::string, common::CalibrationResult>& calibration_results);

  void DrawReprojImages(const std::string& output_dir);

  ExtrinsicEstimatorPtr GetExtrinsicEstimator() { return extrinsic_estimator_; }

 private:
  int CameraEOLExtrinsicCalibration(const std::map<std::string, SensorInput>& sensors_input,
                                    std::map<std::string, common::CalibrationResult>& calibration_results);

  CalibrationParam calibration_param_;
  ExtrinsicEstimatorPtr extrinsic_estimator_;
};

typedef std::shared_ptr<CameraEOLCalibration> CameraEOLCalibrationPtr;
}  // namespace calibration_algo::eol::camera
