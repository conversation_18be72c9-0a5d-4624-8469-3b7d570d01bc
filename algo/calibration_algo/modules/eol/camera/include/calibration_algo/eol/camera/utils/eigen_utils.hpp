#pragma once

#include <Eigen/Dense>
#include <cmath>

namespace calibration_algo::eol::camera {

inline Eigen::Vector3d R2ypr(const Eigen::Matrix3d& R) {
  Eigen::Vector3d n = R.col(0);
  Eigen::Vector3d o = R.col(1);
  Eigen::Vector3d a = R.col(2);

  Eigen::Vector3d ypr(3);
  double y = atan2(n(1), n(0));
  double p = atan2(-n(2), n(0) * std::cos(y) + n(1) * std::sin(y));
  double r = atan2(a(0) * std::sin(y) - a(1) * std::cos(y), -o(0) * std::sin(y) + o(1) * std::cos(y));
  ypr(0) = y;
  ypr(1) = p;
  ypr(2) = r;

  return ypr / M_PI * 180.0;
}

inline Eigen::Vector3d R2ypr(const Eigen::Quaterniond& q) {
  Eigen::Matrix3d R = q.toRotationMatrix();
  return R2ypr(R);
}

}  // namespace calibration_algo::eol::camera