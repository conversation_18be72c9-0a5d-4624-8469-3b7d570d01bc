#pragma once

#include <ceres/ceres.h>
#include <ceres/rotation.h>
#include <omp.h>

#include <Eigen/Dense>
#include <chrono>
#include <fstream>
#include <iostream>
#include <numeric>
#include <opencv2/core/eigen.hpp>
#include <string>
#include <unordered_set>
#include <vector>

#include "AprilTags/TagDetector.h"
#include "calibration_algo/eol/camera/extrinsic_calib/factor.h"
#include "calibration_algo/eol/camera/sensor_production_calibration.h"
#include "calibration_algo/eol/camera/utils/common_utils.hpp"
#include "calibration_algo/eol/camera/utils/cv_utils.hpp"
#include "calibration_algo/eol/camera/utils/eigen_utils.hpp"
#include "camodocal/EquidistantCamera.h"
#include "camodocal/PinholeCamera.h"
#include "camodocal/PinholeFullCamera.h"
#include "glog/logging.h"
#include "opencv2/opencv.hpp"
// #include "calibration_algo/common/pose.hpp"

namespace calibration_algo::eol::camera {

class ExtrinsicEstimator {
 public:
  struct AxisAngle {
    double r_vec[3];
    double t_vec[3];
  };
  ExtrinsicEstimator() {}
  bool Init(const EOLParams& eol_config, const std::map<std::string, common::Point>& calibroom_points_map,
            const SensorProductionCalibration::CalibrationType& calib_type);
  void reset();
  void feed_img(const cv::Mat& img, const std::string& cam_name, const common::CameraIntrinsicParam& IntrinsicParam,
                const common::ExtrinsicPose& StructureParam);
  void feed_img(const cv::Mat& img, const std::string& cam_name, const common::CameraIntrinsicParam& IntrinsicParam,
                common::CalibrationResult& sensor_calibration_result);
  bool Solve();

  void save_result(std::map<std::string, common::CalibrationResult>& calibration_results);
  Eigen::Quaterniond get_c_tf_w_q(const std::string& cam_name);
  Eigen::Vector3d get_c_tf_w_t(const std::string& cam_name);
  Eigen::Quaterniond get_w_tf_c_q(const std::string& cam_name);
  Eigen::Vector3d get_w_tf_c_t(const std::string& cam_name);
  void printf_result();
  void draw_reproj_error(const std::string& output_dir);
  double cal_reproj_error(const std::string& cam_name);
  void print_epipolar_error();
  double cal_epipolar_error(const std::string& cam0_name, const std::string& cam1_name);
  common::CalibrationErrorCode get_calib_error_code(const std::string& cam_name);
  std::vector<AprilTags::TagDetection> detect_apriltags(const std::string& cam_name, const cv::Mat& img);

  void refineDetectionsSubPixAdvanced(std::vector<AprilTags::TagDetection>& detections, const cv::Mat& gray_image,
                                      const cv::Size& winSize = cv::Size(5, 5), int maxIterations = 40,
                                      double epsilon = 0.001);

  void add_AprilTagDetections_offset(std::vector<AprilTags::TagDetection>& detections,
                                     const std::vector<double>& offset);
  void filter_tags_with_prior(const std::string& cam_name, const cv::Mat& img,
                              std::vector<std::vector<cv::Point3f>>& w_pts,
                              std::vector<std::vector<cv::Point2f>>& img_pts,
                              std::vector<std::vector<std::string>>& json_key_names,
                              std::vector<AprilTags::TagDetection>& detections);
  bool get_w_pts(const std::string& cam_name, const cv::Mat& img, std::vector<std::vector<cv::Point3f>>& w_pts,
                 std::vector<std::vector<cv::Point2f>>& img_pts, std::vector<std::vector<std::string>>& json_key_names,
                 const std::vector<AprilTags::TagDetection>& detections);
  bool check_tags_integrity(const std::string& cam_name, const cv::Mat& img,
                            const std::vector<AprilTags::TagDetection>& detections);
  bool solve_pnp(const std::string& cam_name, const std::vector<std::vector<cv::Point3f>>& w_pts,
                 const std::vector<std::vector<cv::Point2f>>& img_pts, std::vector<std::vector<cv::Point3f>>& ray_pts,
                 AxisAngle& ci_tf_w);
  void save_BA(const std::string& cam_name, const std::vector<std::vector<cv::Point3f>>& ray_pts,
               const std::vector<std::vector<cv::Point2f>>& img_pts, const std::vector<std::vector<cv::Point3f>>& w_pts,
               const cv::Mat& img, const AxisAngle& ci_tf_w,
               const std::vector<std::vector<std::string>>& json_key_names);

  camodocal::CameraPtr get_cam(const std::string& cam_name) { return cam_.at(cam_name); }

 private:
  bool set_camera_model(const std::string& cam_name, const common::CameraIntrinsicParam& IntrinsicParam,
                        const int& image_width, const int& image_height);
  bool set_camera_model(const std::string& cam_name, const cv::Mat& K, const cv::Mat& D, const int& Iw, const int& Ih,
                        const common::CameraDistortionType& cam_type);
  std::vector<cv::Point2f> undistort_points(const std::string& cam_name, const std::vector<cv::Point2f>& imgPts);
  cv::Point2f undistort_point(const cv::Point2f& imgPt, const camodocal::CameraPtr& cam);

  void init_direction_map(const std::map<std::string, std::vector<std::vector<int>>>& necessary_ids,
                          const std::map<std::string, std::vector<int>>& full_ids);
  void init_prior_parameters(const std::vector<std::string>& cam_names);

  void draw_debug_img(const cv::Mat& img, const std::string& cam_name,
                      const std::vector<AprilTags::TagDetection>& detections);
  double calEpipolarError(const cv::Mat& F, const cv::Point2f& pt1, const cv::Point2f& pt2);
  bool acquire_w_pts(const cv::Mat& img, const std::string& cam_direction, const int& tag_id,
                     const std::string& location, std::string& json_key_name, const cv::Point2f& img_pt,
                     cv::Point3f& w_pt, common::CalibrationResult& sensor_calibration_result);
  bool acquire_w_pts(const std::string& json_key_name, cv::Point3f& w_pt,
                     common::CalibrationResult& sensor_calibration_result);
  // tranform
  Eigen::Quaterniond ego_tf_rfu_;

  // came name to direction
  // std::string name2direction(const std::string& cam_name);
  // std::set<std::string> supported_cam_directions_;

  std::shared_ptr<AprilTags::TagDetector> m_tagDetector_;

  std::shared_ptr<ceres::Problem> problem_;

  // camera model
  std::map<std::string, camodocal::CameraPtr> cam_;

  // pnp result
  std::map<std::string, AxisAngle> Ci_tf_W_;

  // structure parameters rfu_tf_cam
  std::map<std::string, Eigen::Vector3d> structure_param_map_ypr_;  // Order: ZYX
  std::map<std::string, Eigen::Vector3d> structure_param_map_t_;

  // prior parameters cam_tf_rfu
  std::map<std::string, Eigen::Quaterniond> prior_q_;
  std::map<std::string, Eigen::Vector3d> prior_t_;

  // measurements for BA
  std::map<std::string, std::vector<std::vector<cv::Point3f>>> wPts_;
  std::map<std::string, std::vector<std::vector<cv::Point3f>>> rayPts_;

  // visualization
  std::map<std::string, cv::Mat> imgs_;
  std::set<std::string> camNames_;
  std::map<std::string, std::vector<std::vector<cv::Point2f>>> imgPts_;

  std::map<std::string, std::vector<std::vector<std::string>>> json_key_names_;

  std::map<std::string, Eigen::Vector3d> calibroom_points_map_;
  std::map<int, std::string> int2location_map_;
  std::map<std::string, std::vector<int>> direction_to_full_ids_map_;
  std::map<std::string, std::vector<std::vector<int>>> direction_to_necessary_ids_map_;

  SensorProductionCalibration::CalibrationType calib_type_;
  // internal result container
  std::map<std::string, common::CalibrationResult> sensors_calibration_result_;

  // eol config
  EOLParams eol_config_;
};

typedef std::shared_ptr<ExtrinsicEstimator> ExtrinsicEstimatorPtr;

}  // namespace calibration_algo::eol::camera