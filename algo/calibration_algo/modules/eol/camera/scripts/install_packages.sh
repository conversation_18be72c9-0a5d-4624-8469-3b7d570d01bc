
sudo apt update

# ========================== install C++ ============================
sudo apt install build-essential gdb
sudo apt install cmake

# test 
gcc --version
g++ --version
gdb --version
cmake --version
# ========================== finsh install C++ ============================



# ============================= yaml-cpp ==================================
sudo apt-get install -y libyaml-cpp-dev
ls /usr/include/yaml-cpp
# ============================= finsh install yaml-cpp ==================================

# ============================== PkgConfig ==================================
sudo apt install pkg-config
pkg-config --version
# ============================== finsh install PkgConfig ==================================


# ========================== install opencv 4.5.5 ==========================
sudo apt install -y g++
sudo apt install -y cmake
sudo apt install -y make
sudo apt install -y wget unzip

mkdir calibration_packages
cd calibration_packages

git clone -b 4.5.5 https://githubfast.com/opencv/opencv.git
git clone -b 4.5.5 https://githubfast.com/opencv/opencv_contrib.git

cd opencv
mkdir build
cd build

cmake -D CMAKE_BUILD_TYPE=RELEASE \
      -D CMAKE_INSTALL_PREFIX=/usr/local \
      -D INSTALL_C_EXAMPLES=ON \
      -D INSTALL_PYTHON_EXAMPLES=ON \
      -D OPENCV_GENERATE_PKGCONFIG=ON \
      -D OPENCV_EXTRA_MODULES_PATH=../../opencv_contrib/modules \
      -D BUILD_EXAMPLES=ON ..

# make
make -j$(nproc)
# install 
sudo make install
# environment setting
sudo ldconfig

# test opencv 4.5.5
cd ../../
pkg-config --modversion opencv4
touch test_opencv.cpp

# #include <opencv2/opencv.hpp>
# #include <iostream>
# int main() {
#     std::cout << "OpenCV version: " << CV_VERSION << std::endl;
#     cv::Mat image = cv::Mat::zeros(300, 300, CV_8UC3);
#     std::string filename = "test_image.jpg";
#     bool saved = cv::imwrite(filename, image);
#     if (saved) {
#         std::cout << "The image has been successfully saved as" << filename << std::endl;
#     } else {
#         std::cerr << "Error saving the image" << std::endl;
#     }
#     return 0;
# }

echo "#include <opencv2/opencv.hpp>" >> test_opencv.cpp
echo "#include <iostream>" >> test_opencv.cpp
echo "int main() {" >> test_opencv.cpp
echo "    std::cout << \"OpenCV version: \" << CV_VERSION << std::endl;" >> test_opencv.cpp
echo "    cv::Mat image = cv::Mat::zeros(300, 300, CV_8UC3);" >> test_opencv.cpp
echo "    std::string filename = \"test_image.jpg\";" >> test_opencv.cpp
echo "    bool saved = cv::imwrite(filename, image);" >> test_opencv.cpp
echo "    if (saved) {" >> test_opencv.cpp
echo "        std::cout << \"The image has been successfully saved as\" << filename << std::endl;" >> test_opencv.cpp
echo "    } else {" >> test_opencv.cpp
echo "        std::cerr << \"Error saving the image\" << std::endl;" >> test_opencv.cpp
echo "    }" >> test_opencv.cpp
echo "    return 0;" >> test_opencv.cpp
echo "}" >> test_opencv.cpp


# compile and run
g++ test_opencv.cpp -o test_opencv `pkg-config --cflags --libs opencv4`
./test_opencv

# ============================= finsh install opencv 4.5.5 =============================


# ============================= install Ceres Solver 2.1 =================================
sudo apt-get install -y liblapack-dev libsuitesparse-dev libcxsparse3 libgflags-dev libgoogle-glog-dev libgtest-dev libeigen3-dev 

echo 'export CPLUS_INCLUDE_PATH=/usr/include/eigen3:$CPLUS_INCLUDE_PATH' >> ~/.zshrc
source ~/.zshrc
echo 'export CPLUS_INCLUDE_PATH=/usr/include/eigen3:$CPLUS_INCLUDE_PATH' >> ~/.bashrc
source ~/.bashrc

echo 'export LIBRARY_PATH=/usr/local/lib:$LIBRARY_PATH' >> ~/.zshrc
source ~/.zshrc
echo 'export LIBRARY_PATH=/usr/local/lib:$LIBRARY_PATH' >> ~/.bashrc
source ~/.bashrc



git clone https://githubfast.com/ceres-solver/ceres-solver.git
cd ceres-solver
git checkout 2.1.0

mkdir build && cd build

cmake -DCMAKE_BUILD_TYPE=Release -DBUILD_SHARED_LIBS=ON -DBUILD_TESTING=OFF -DBUILD_EXAMPLES=OFF ..

sudo make -j$(nproc)
sudo make install

# #include <iostream>
# #include "ceres/ceres.h"

# // 定义一个简单的代价函数
# struct CostFunctor {
#     template <typename T>
#     bool operator()(const T* const x, T* residual) const {
#         residual[0] = 10.0 - x[0];
#         return true;
#     }
# };

# int main(int argc, char** argv) {
#     google::InitGoogleLogging(argv[0]);

#     // 初始值
#     double x = 0.5;
#     const double initial_x = x;

#     // 构建问题
#     ceres::Problem problem;

#     // 定义代价函数
#     ceres::CostFunction* cost_function =
#         new ceres::AutoDiffCostFunction<CostFunctor, 1, 1>(new CostFunctor);
#     problem.AddResidualBlock(cost_function, nullptr, &x);

#     // 配置求解器
#     ceres::Solver::Options options;
#     options.linear_solver_type = ceres::DENSE_QR;
#     options.minimizer_progress_to_stdout = true;

#     // 求解结果
#     ceres::Solver::Summary summary;
#     ceres::Solve(options, &problem, &summary);

#     // 输出结果
#     std::cout << summary.BriefReport() << "\n";
#     std::cout << "Initial x: " << initial_x << "\n";
#     std::cout << "Final x: " << x << "\n";

#     return 0;
# }

cd ../../

# rm test_ceres.cpp
touch test_ceres.cpp

echo "#include <iostream>" >> test_ceres.cpp
echo "#include \"ceres/ceres.h\"" >> test_ceres.cpp
echo "struct CostFunctor {" >> test_ceres.cpp
echo "    template <typename T>" >> test_ceres.cpp
echo "    bool operator()(const T* const x, T* residual) const {" >> test_ceres.cpp
echo "        residual[0] = 10.0 - x[0];" >> test_ceres.cpp
echo "        return true;" >> test_ceres.cpp
echo "    }" >> test_ceres.cpp
echo "};" >> test_ceres.cpp
echo "int main(int argc, char** argv) {" >> test_ceres.cpp
echo "    google::InitGoogleLogging(argv[0]);" >> test_ceres.cpp
echo "    double x = 0.5;" >> test_ceres.cpp
echo "    const double initial_x = x;" >> test_ceres.cpp
echo "    ceres::Problem problem;" >> test_ceres.cpp
echo "    ceres::CostFunction* cost_function =" >> test_ceres.cpp
echo "        new ceres::AutoDiffCostFunction<CostFunctor, 1, 1>(new CostFunctor);" >> test_ceres.cpp
echo "    problem.AddResidualBlock(cost_function, nullptr, &x);" >> test_ceres.cpp
echo "    ceres::Solver::Options options;" >> test_ceres.cpp
echo "    options.linear_solver_type = ceres::DENSE_QR;" >> test_ceres.cpp
echo "    options.minimizer_progress_to_stdout = true;" >> test_ceres.cpp
echo "    ceres::Solver::Summary summary;" >> test_ceres.cpp
echo "    ceres::Solve(options, &problem, &summary);" >> test_ceres.cpp
echo "    std::cout << summary.BriefReport();" >> test_ceres.cpp
echo "    std::cout << \"Initial x: \" << initial_x;" >> test_ceres.cpp
echo "    std::cout << \"Final x: \" << x;" >> test_ceres.cpp
echo "    return 0;" >> test_ceres.cpp
echo "}" >> test_ceres.cpp

g++ -o test_ceres test_ceres.cpp -I/usr/include/eigen3 -L/usr/local/lib -lceres -lglog -lgflags -llapack -lblas -lpthread -lcholmod -lcolamd -lamd
./test_ceres


# ============================= finsh install Ceres Solver 2.1 =================================

