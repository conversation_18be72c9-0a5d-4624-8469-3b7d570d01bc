[toc]

# check

## check_bev

### Usage

- Before running the script, ensure you have modified `cam_calib_result_path`, `camera_intrisic_path`, `cam_name2cam_path` to your own paths in the script.

- Run the script to check the BEV camera calibration results:

```bash
python check/check_bev.py
```

## check_lidar_tf_camera

### Usage

- Before running the script, ensure you have modified `cam_id2cam_path`, `lidar_id2lidar_path`, `cam_calib_result_path`, `lidar_calib_result_path`, `camera_intrisic_path` to your own paths in the script.
- Necessary camera_names to check:
  - `cam_front_30`
  - `cam_front_120`
- Necessary lidar_names to check:
  - `front_2_lidar`
- Run the script to check the LiDAR to camera transformation results:

```bash
python check/check_lidar_tf_camera.py
```

## check_cam2ego

- Before running the script, ensure you have modified `cam_name2img_path`, `cam_calib_result_path`, `camera_intrisic_path` to your own paths in the script.
- Necessary camera_names to check:
  - `cam_front_120`
  - `cam_back_100`

- Run the script to check the camera to ego transformation results:

```bash
python check/check_cam2ego.py
```

## check_epipolar

- Only algorithm testing is required