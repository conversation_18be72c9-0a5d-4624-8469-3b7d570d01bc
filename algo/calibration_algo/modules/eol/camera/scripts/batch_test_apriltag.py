import os, sys
import numpy as np
import cv2
import matplotlib.pyplot as plt
from tqdm import tqdm
import yaml
import concurrent.futures
from sklearn.ensemble import IsolationForest
import subprocess
import shutil
import threading
import json


def apriltag_detection_test(
    statistics_json_path,
    executable_path,
    output_dir,
    eol_config_yaml_path,
    max_threads=8,
):
    if os.path.exists(output_dir):
        os.system("rm -rf {}".format(output_dir))
    os.makedirs(output_dir, exist_ok=True)

    if not os.path.exists(executable_path):
        assert False, "Executable path does not exist: {}".format(executable_path)

    if not os.path.exists(statistics_json_path):
        assert False, "Statistics json path does not exist: {}".format(
            statistics_json_path
        )

    tmp_config_yaml_path = os.path.join(output_dir, "tmp_eol_config.yaml")
    with open(eol_config_yaml_path, "r") as file:
        eol_config = yaml.safe_load(file)
        mach_camera_name_lst = [
            v for v in eol_config["geely2mach_camera_mappings"].values()
        ]

        eol_config["is_debug"] = False
        eol_config["is_simulation_error"] = False
        eol_config["is_print_eol_config"] = False

        with open(tmp_config_yaml_path, "w") as f:
            yaml.dump(eol_config, f, sort_keys=False)

    statistics_data = {}
    with open(statistics_json_path, "r") as f:
        statistics_data = json.load(f)

    with concurrent.futures.ThreadPoolExecutor(max_threads) as executor:
        futures = []
        for cam_name, cam_data in statistics_data.items():
            print("Camera: ", cam_name)

            for error_code, error_data in cam_data.items():
                if "_img" not in error_code:
                    continue
                error_code = error_code.replace("_img", "")
                if error_code != "5" and error_code != "4":
                    continue

                print("  Error Code: ", error_code)

                for img_path in tqdm(
                    error_data, position=0, colour="green", desc="Processing Images: "
                ):
                    tmp_output_dir = os.path.join(output_dir, cam_name, error_code)
                    cmd = "cd {} && {} {} {} {} {}".format(
                        os.path.dirname(executable_path),
                        "./" + os.path.basename(executable_path),
                        cam_name,
                        img_path,
                        tmp_output_dir.replace(
                            os.path.dirname(executable_path) + os.sep, " "
                        ),
                        tmp_config_yaml_path.replace(
                            os.path.dirname(executable_path) + os.sep, " "
                        ),
                    )
                    futures.append(executor.submit(os.system, cmd))

        for future in concurrent.futures.as_completed(futures):
            try:
                future.result()
            except Exception as e:
                print(f"Error occurred: {e}")

    print("Done!")

    statistics_error_data(output_dir, tmp_config_yaml_path)


def statistics_error_data(error_data_dir, eol_config_path):
    eol_config = {}
    with open(eol_config_path, "r") as f:
        eol_config = yaml.safe_load(f)

    missing_data_count = {}

    cam_name_lst = [x for x in eol_config["geely2mach_camera_mappings"].values()]

    for cam_name in cam_name_lst:
        print("Camera: ", cam_name)
        missing_data_count[cam_name] = {}

        error_detection_dir = os.path.join(error_data_dir, cam_name)
        if not os.path.exists(error_detection_dir):
            continue

        necessary_ids = [
            x for y in eol_config["check_direction_necessary_ids"][cam_name] for x in y
        ]

        detections_dir_lst = []
        detections_dir_lst = [
            os.path.join(error_detection_dir, d)
            for d in os.listdir(error_detection_dir)
            if os.path.isdir(os.path.join(error_detection_dir, d))
        ]

        for detections_dir in detections_dir_lst:
            error_code = os.path.basename(detections_dir)
            if error_code != "5" and error_code != "4":
                continue

            print("  Error Code: ", error_code)

            detections_dir = os.path.join(error_detection_dir, error_code, "detections")
            detection_txt_path_lst = [
                os.path.join(detections_dir, d)
                for d in os.listdir(detections_dir)
                if os.path.isfile(os.path.join(detections_dir, d))
                and d.endswith(".txt")
            ]

            for detection_txt_path in tqdm(
                detection_txt_path_lst,
                position=0,
                colour="green",
                desc="Processing Error Data: ",
            ):
                with open(detection_txt_path, "r") as f:
                    detection_data = f.readlines()
                    detection_data = [int(x.strip()) for x in detection_data]

                missing_ids = list(set(necessary_ids) - set(detection_data))

                for id in missing_ids:
                    if id not in missing_data_count[cam_name].keys():
                        missing_data_count[cam_name][id] = 0
                    missing_data_count[cam_name][id] += 1

    # print
    print("===================== Missing Data Count =====================")
    for cam_name, cam_data in missing_data_count.items():
        print("Camera: ", cam_name)
        for id, count in cam_data.items():
            print("  ID: {}, Count: {}".format(id, count))

    output_missing_data_path = os.path.join(
        os.path.dirname(error_data_dir), "missing_data_statistics.yaml"
    )
    print("\n\noutput_missing_data_path: ", output_missing_data_path)
    with open(output_missing_data_path, "w") as f:
        yaml.dump(missing_data_count, f, allow_unicode=True, indent=4)

    output_missing_data_path = os.path.join(
        os.path.dirname(error_data_dir), "missing_data_statistics.json"
    )
    with open(output_missing_data_path, "w") as f:
        json.dump(missing_data_count, f, indent=4)


if __name__ == "__main__":
    # # 2025.04.17: P177 data
    # statistics_yaml_path = (
    #     "./build/output_batch_test/multi-threaded_test_2025.04.17/error_data_statistics.json"
    # )

    # # 2025.04.24: P177 data
    # statistics_yaml_path = "build/output_batch_test/multi-threaded_test_2025.04.24/error_data_statistics_1.json"

    # 2025.04.27: P177 data
    statistics_yaml_path = "./build/output_batch_test/multi-threaded_test_2025.04.27/error_data_statistics.json"

    output_dir = "./build/output_batch_test/apriltag_detection_test"
    executable_path = "./build/batch_test_apriltag"

    eol_config_yaml_path = "./config/P177/eol_config.yaml"

    max_threads = 12

    apriltag_detection_test(
        statistics_yaml_path,
        executable_path,
        output_dir,
        eol_config_yaml_path,
        max_threads,
    )

    # statistics_error_data(output_dir, eol_config_yaml_path)
