import os, sys
import numpy as np
import cv2
import matplotlib.pyplot as plt
from tqdm import tqdm
import yaml
import concurrent.futures
from sklearn.ensemble import IsolationForest
from scipy.spatial.transform import Rotation, RotationSpline


def error_simulation(
    eol_config_yaml_path,
    executable_path,
    simulation_config_lst,
    test_num=500,
    output_dir="./output",
    max_threads=8,
):
    os.makedirs(output_dir, exist_ok=True)

    with open(eol_config_yaml_path, "r") as file:
        eol_config = yaml.safe_load(file)

    eol_config["simulation_error_config"] = {
        "test_num": 0,
        "f_offset": [0.0, 0.0],
        "D_offset": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        "img_detect_points_offset": [0.0, 0.0],
        "calibroom_points_offset": [0.0, 0.0, 0.0],
        "random_mask_num": 0,
        "random_mask_AprilTag": {
            mach_name: []
            for mach_name in eol_config["simulation_error_config"][
                "random_mask_AprilTag"
            ].keys()
        },
        "output_simulation_error_name": "simulation_error.yaml",
    }

    eol_config["is_debug"] = False
    eol_config["is_simulation_error"] = True
    eol_config["simulation_error_config"]["test_num"] = test_num

    def run_simulation(
        simulation_config, eol_config, output_dir, executable_path, index
    ):
        for key in simulation_config.keys():
            if key in eol_config["simulation_error_config"].keys():
                eol_config["simulation_error_config"][key] = simulation_config[key]
            else:
                print(f"Key {key} not found in simulation_error_config.")

        output_eol_config_path = os.path.join(
            output_dir, "eol_config_simulation_error_{}.yaml".format(index + 1)
        )
        print(output_eol_config_path)
        with open(output_eol_config_path, "w") as file:
            yaml.dump(eol_config, file)
        # Run the executable with the updated config
        cmd = "cd {} && {} {}".format(
            os.path.dirname(executable_path),
            "./" + os.path.basename(executable_path),
            output_eol_config_path.replace(os.path.dirname(executable_path) + "/", ""),
        )
        print(cmd)
        os.system(cmd)

    with concurrent.futures.ThreadPoolExecutor(max_workers=max_threads) as executor:
        futures = [
            executor.submit(
                run_simulation,
                simulation_config,
                eol_config.copy(),
                output_dir,
                executable_path,
                i,
            )
            for i, simulation_config in enumerate(
                tqdm(
                    simulation_config_lst,
                    position=0,
                    colour="green",
                    desc="Simulation Error: ",
                )
            )
        ]
        concurrent.futures.wait(futures)

    # for i, simulation_config in enumerate(tqdm(simulation_config_lst, position=0, colour="green", desc="Simulation Error: ")):
    #     for key in simulation_config.keys():
    #         if key in eol_config["simulation_error_config"].keys():
    #             eol_config["simulation_error_config"][key] = simulation_config[key]
    #         else:
    #             print(f"Key {key} not found in simulation_error_config.")

    #     output_eol_config_path = os.path.join(output_dir, "eol_config_simulation_error_{}.yaml".format(i+1))
    #     print(output_eol_config_path)
    #     with open(output_eol_config_path, 'w') as file:
    #         yaml.dump(eol_config, file)
    #     # Run the executable with the updated config
    #     cmd = "cd {} && {} {}".format(os.path.dirname(executable_path), "./" + os.path.basename(executable_path), output_eol_config_path.replace(os.path.dirname(executable_path) + "/", ""))
    #     print(cmd)
    #     os.system(cmd)


def cal_angle_diff(angle1, angle2):
    # diff = abs(angle1 - angle2) % 360
    # return min(diff, 360 - diff)
    diff = (angle1 - angle2) % 180
    return min(diff, 180 - diff)


def error_simulation_vis(
    input_yaml_path_lst, output_dir="./output/error_simulation_vis"
):
    os.makedirs(output_dir, exist_ok=True)

    def error_simulation_sub_vis(input_yaml_path):
        print("input_yaml_path: ", input_yaml_path)
        with open(input_yaml_path, "r") as f:
            lines = f.readlines()
            if lines[0].startswith("%YAML"):
                data = yaml.safe_load("".join(lines[2:]))
            else:
                data = yaml.safe_load("".join(lines))

        # cam_tf_rfu
        tmp_gt_data = {
            camera_name: {
                "x": camera_data["translation"]["x"],
                "y": camera_data["translation"]["y"],
                "z": camera_data["translation"]["z"],
                "q_w": camera_data["rotation"]["w"],
                "q_x": camera_data["rotation"]["x"],
                "q_y": camera_data["rotation"]["y"],
                "q_z": camera_data["rotation"]["z"],
            }
            for camera_name, camera_data in data["gt"].items()
        }

        gt_data = {}
        # trans cam_tf_rfu to rfu_tf_cam
        for camera_name, camera_data in tmp_gt_data.items():
            trans_matrix = np.eye(4, 4)
            q = [
                camera_data["q_w"],
                camera_data["q_x"],
                camera_data["q_y"],
                camera_data["q_z"],
            ]
            trans_matrix[:3, :3] = Rotation.from_quat(q).as_matrix()
            trans_matrix[:3, 3] = [camera_data["x"], camera_data["y"], camera_data["z"]]

            cam_tf_rfu = np.linalg.inv(trans_matrix)
            r = cam_tf_rfu[:3, :3]
            t = cam_tf_rfu[:3, 3].tolist()
            angles = Rotation.from_matrix(r).as_euler("zyx", degrees=True).tolist()

            gt_data[camera_name] = {
                "x": t[0],
                "y": t[1],
                "z": t[2],
                "RotX": angles[2],
                "RotY": angles[1],
                "RotZ": angles[0],
            }

        error_count = {
            cam_name: {"x": [], "y": [], "z": [], "RotX": [], "RotY": [], "RotZ": []}
            for cam_name in gt_data.keys()
        }

        for data_key, data_value in tqdm(
            data.items(), position=0, colour="green", desc="Simulation Error Vis: "
        ):
            if data_key == "gt":
                continue

            for camera_name, camera_data in data_value.items():
                x, y, z = (
                    camera_data["translation"]["x"],
                    camera_data["translation"]["y"],
                    camera_data["translation"]["z"],
                )
                q_w, q_x, q_y, q_z = (
                    camera_data["rotation"]["w"],
                    camera_data["rotation"]["x"],
                    camera_data["rotation"]["y"],
                    camera_data["rotation"]["z"],
                )

                trans_matrix = np.eye(4, 4)
                q = [q_w, q_x, q_y, q_z]
                trans_matrix[:3, :3] = Rotation.from_quat(q).as_matrix()
                trans_matrix[:3, 3] = [x, y, z]

                cam_tf_rfu = np.linalg.inv(trans_matrix)
                r = cam_tf_rfu[:3, :3]
                x, y, z = cam_tf_rfu[:3, 3].tolist()
                RotZ, RotY, RotX = (
                    Rotation.from_matrix(r).as_euler("zyx", degrees=True).tolist()
                )

                gt_x, gt_y, gt_z = (
                    gt_data[camera_name]["x"],
                    gt_data[camera_name]["y"],
                    gt_data[camera_name]["z"],
                )
                gt_RotX, gt_RotY, gt_RotZ = (
                    gt_data[camera_name]["RotX"],
                    gt_data[camera_name]["RotY"],
                    gt_data[camera_name]["RotZ"],
                )

                error_count[camera_name]["x"].append(x - gt_x)
                error_count[camera_name]["y"].append(y - gt_y)
                error_count[camera_name]["z"].append(z - gt_z)
                error_count[camera_name]["RotX"].append(cal_angle_diff(RotX, gt_RotX))
                error_count[camera_name]["RotY"].append(cal_angle_diff(RotY, gt_RotY))
                error_count[camera_name]["RotZ"].append(cal_angle_diff(RotZ, gt_RotZ))

        # Remove outliers from error_count

        def filter_extreme_outliers(data):
            data = np.array(data)
            median = np.median(data)
            distance = np.abs(data - median)
            threshold = np.percentile(distance, 95)  # 99th percentile
            return data[distance <= threshold]

        for cam_name, errors in error_count.items():
            for key, values in errors.items():
                error_count[cam_name][key] = filter_extreme_outliers(values).tolist()

        return error_count

    # Collect error counts in parallel
    error_counts = {}
    with concurrent.futures.ThreadPoolExecutor() as executor:
        futures = {
            executor.submit(error_simulation_sub_vis, input_yaml_path): input_yaml_path
            for input_yaml_path in input_yaml_path_lst
        }
        for future in concurrent.futures.as_completed(futures):
            input_yaml_path = futures[future]
            try:
                error_counts[input_yaml_path] = future.result()
            except Exception as e:
                print(f"Error processing {input_yaml_path}: {e}")

    # 自定义分段线性变换函数
    def piecewise_transform(x):
        x = np.array(x)
        return np.where(
            (x >= -0.3) & (x <= 0.3),
            x * 5,
            np.where(x < -0.3, (x + 0.3) * 0.5 - 1.5, (x - 0.3) * 0.5 + 1.5),
        )

    def inverse_piecewise_transform(x):
        x = np.array(x)
        return np.where(
            (x >= -1.5) & (x <= 1.5),
            x / 5,
            np.where(x < -1.5, (x + 1.5) * 2 - 0.3, (x - 1.5) * 2 + 0.3),
        )

    # Plot in the main thread
    for input_yaml_path, error_count in error_counts.items():
        tmp_output_dir = os.path.join(
            output_dir, os.path.basename(input_yaml_path).replace(".yaml", "")
        )
        if os.path.exists(tmp_output_dir):
            os.system("rm -rf {}".format(tmp_output_dir))
        os.makedirs(tmp_output_dir, exist_ok=True)

        for camera_name, error_count_data in error_count.items():
            x = error_count_data["x"]
            y = error_count_data["y"]
            z = error_count_data["z"]
            RotX = error_count_data["RotX"]
            RotY = error_count_data["RotY"]
            RotZ = error_count_data["RotZ"]

            plt.figure(figsize=(12, 8))
            # ax = plt.gca()
            # ax.xaxis.set_major_locator(plt.MultipleLocator(0.2))  # Set major ticks at intervals of 0.5
            # ax.xaxis.set_minor_locator(plt.MultipleLocator(0.05))  # Set minor ticks at intervals of 0.1

            plt.subplot(231)
            transformed_x = piecewise_transform(x)
            plt.hist(transformed_x, bins=200)  # Increased bins for finer granularity
            plt.title("x error: {:.4f}".format(np.mean(np.abs(x))))
            plt.xticks(
                inverse_piecewise_transform(plt.xticks()[0])
            )  # Map x-axis back to original scale

            plt.subplot(232)
            transformed_y = piecewise_transform(y)
            plt.hist(transformed_y, bins=200)  # Increased bins for finer granularity
            plt.title("y error: {:.4f}".format(np.mean(np.abs(y))))
            plt.xticks(
                inverse_piecewise_transform(plt.xticks()[0])
            )  # Map x-axis back to original scale

            plt.subplot(233)
            transformed_z = piecewise_transform(z)
            plt.hist(transformed_z, bins=200)  # Increased bins for finer granularity
            plt.title("z error: {:.4f}".format(np.mean(np.abs(z))))
            plt.xticks(
                inverse_piecewise_transform(plt.xticks()[0])
            )  # Map x-axis back to original scale

            plt.subplot(234)
            transformed_RotX = piecewise_transform(RotX)
            plt.hist(transformed_RotX, bins=200)  # Increased bins for finer granularity
            plt.title("RotX error: {:.4f}".format(np.mean(np.abs(RotX))))
            plt.xticks(
                inverse_piecewise_transform(plt.xticks()[0])
            )  # Map x-axis back to original scale

            plt.subplot(235)
            transformed_RotY = piecewise_transform(RotY)
            plt.hist(transformed_RotY, bins=200)  # Increased bins for finer granularity
            plt.title("RotY error: {:.4f}".format(np.mean(np.abs(RotY))))
            plt.xticks(
                inverse_piecewise_transform(plt.xticks()[0])
            )  # Map x-axis back to original scale

            plt.subplot(236)
            transformed_RotZ = piecewise_transform(RotZ)
            plt.hist(transformed_RotZ, bins=200)  # Increased bins for finer granularity
            plt.title("RotZ error: {:.4f}".format(np.mean(np.abs(RotZ))))
            plt.xticks(
                inverse_piecewise_transform(plt.xticks()[0])
            )  # Map x-axis back to original scale

            output_path = os.path.join(
                tmp_output_dir, "{}_error_simulation_vis.png".format(camera_name)
            )
            print("save to: ", output_path)
            plt.savefig(output_path)
            plt.close()


if __name__ == "__main__":
    # Z10
    # eol_config_yaml_path = "./config/Z10/eol_config.yaml"
    # executable_path = "./build/camera_eol_calibration_node"

    # P177
    eol_config_yaml_path = "./config/P177/eol_config.yaml"
    executable_path = "./build/camera_eol_calibration_node"

    test_num = 100
    f_offset_simulation_config_lst = [
        {
            "f_offset": [0.005, 0.005],
            "output_simulation_error_name": "f_offset_0.005_simulation_error.yaml",
        },
        {
            "f_offset": [0.01, 0.01],
            "output_simulation_error_name": "f_offset_0.01_simulation_error.yaml",
        },
        {
            "f_offset": [0.013, 0.013],
            "output_simulation_error_name": "f_offset_0.013_simulation_error.yaml",
        },
        {
            "f_offset": [0.016, 0.016],
            "output_simulation_error_name": "f_offset_0.016_simulation_error.yaml",
        },
        {
            "f_offset": [0.02, 0.02],
            "output_simulation_error_name": "f_offset_0.02_simulation_error.yaml",
        },
        {
            "f_offset": [0.05, 0.05],
            "output_simulation_error_name": "f_offset_0.05_simulation_error.yaml",
        },
    ]

    img_detect_points_offset_simulation_config_lst = [
        {
            "img_detect_points_offset": [0.5, 0.5],
            "output_simulation_error_name": "img_detect_points_offset_0.5_simulation_error.yaml",
        },
        {
            "img_detect_points_offset": [1, 1],
            "output_simulation_error_name": "img_detect_points_offset_1_simulation_error.yaml",
        },
        {
            "img_detect_points_offset": [2, 2],
            "output_simulation_error_name": "img_detect_points_offset_2_simulation_error.yaml",
        },
        {
            "img_detect_points_offset": [3, 3],
            "output_simulation_error_name": "img_detect_points_offset_3_simulation_error.yaml",
        },
        {
            "img_detect_points_offset": [5, 5],
            "output_simulation_error_name": "img_detect_points_offset_5_simulation_error.yaml",
        },
        {
            "img_detect_points_offset": [8, 8],
            "output_simulation_error_name": "img_detect_points_offset_8_simulation_error.yaml",
        },
    ]

    calibroom_points_offset_simulation_config_lst = [
        {
            "calibroom_points_offset": [0.0005, 0.0005, 0.0005],
            "output_simulation_error_name": "calibroom_points_offset_0.0005_simulation_error.yaml",
        },
        {
            "calibroom_points_offset": [0.001, 0.001, 0.001],
            "output_simulation_error_name": "calibroom_points_offset_0.001_simulation_error.yaml",
        },
        {
            "calibroom_points_offset": [0.0015, 0.0015, 0.0015],
            "output_simulation_error_name": "calibroom_points_offset_0.0015_simulation_error.yaml",
        },
        {
            "calibroom_points_offset": [0.002, 0.002, 0.002],
            "output_simulation_error_name": "calibroom_points_offset_0.002_simulation_error.yaml",
        },
        {
            "calibroom_points_offset": [0.003, 0.003, 0.003],
            "output_simulation_error_name": "calibroom_points_offset_0.003_simulation_error.yaml",
        },
    ]

    actual_scene_simulation_config_lst = [
        {
            "f_offset": [0.01, 0.01],
            "img_detect_points_offset": [1.0, 1.0],
            "calibroom_points_offset": [0.02, 0.02, 0.01],
            "output_simulation_error_name": "simulation_error_actual_scene.yaml",
        }
    ]

    random_mask_lst = [
        {
            "random_mask_AprilTag": {
                "cam_front_120": [0, 4],
                "cam_front_30": [],
                "cam_front_left_100": [],
                "cam_front_right_100": [],
                "cam_back_left_100": [],
                "cam_back_right_100": [],
                "cam_back_100": [],
            },
            "f_offset": [0.0, 0.0],
            # "img_detect_points_offset": [1.0, 1.0],
            # "calibroom_points_offset": [0.002, 0.002, 0.001],
            "output_simulation_error_name": "random_mask_simulation_error.yaml",
        }
    ]
    max_threads = 24
    # output_dir = "./build/output/simulation_error_eol_config"
    # simulation_config_lst = f_offset_simulation_config_lst + img_detect_points_offset_simulation_config_lst + calibroom_points_offset_simulation_config_lst

    simulation_config_lst = actual_scene_simulation_config_lst
    output_dir = "./build/output/simulation_error_actual_scene"

    # simulation_config_lst = random_mask_lst

    # error_simulation
    error_simulation(
        eol_config_yaml_path,
        executable_path,
        simulation_config_lst,
        test_num,
        output_dir,
        max_threads,
    )

    # error_simulation_vis
    input_yaml_name_lst = [
        x["output_simulation_error_name"] for x in simulation_config_lst
    ]
    vis_output_dir = os.path.dirname(output_dir) + "/error_simulation_vis"
    eol_calib_tmp_output_dir = "./build/output"
    input_yaml_path_lst = [
        os.path.join(eol_calib_tmp_output_dir, x)
        for x in input_yaml_name_lst
        if os.path.exists(os.path.join(eol_calib_tmp_output_dir, x))
    ]
    error_simulation_vis(input_yaml_path_lst, vis_output_dir)
