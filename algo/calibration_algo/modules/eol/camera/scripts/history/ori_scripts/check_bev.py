# -*- coding: utf-8 -*-
import cv2
import argparse
import os
import numpy as np
import json
import quaternion
import concurrent.futures
from tqdm import tqdm
import yaml


def GetClockAngle(v1, v2):
    TheNorm = np.linalg.norm(v1) * np.linalg.norm(v2)
    cos_theta = np.dot(v1, v2) / TheNorm
    return cos_theta


gray_warp_image_list = []


def warp_cam_ground(info_dict):
    global gray_warp_image_list
    cam_id = info_dict["cam_id"]
    undistort_image = info_dict["undistort_image"]
    cam_K = info_dict["cam_K"]
    cam_tf_rfu = info_dict["cam_tf_rfu"]
    size_w = info_dict["size_w"]
    size_h = info_dict["size_h"]
    save_path = info_dict["save_path"]
    w_m = info_dict["w_m"]
    h_m = info_dict["h_m"]
    print(f"{cam_id} begin")
    vir_cam_height = 10
    vir_cam_K = [
        size_w / 2 * vir_cam_height / (0.5 * w_m),
        0,
        size_w / 2,
        0,
        size_h / 2 * vir_cam_height / (0.5 * h_m),
        size_h / 2,
        0,
        0,
        1,
    ]
    vir_cam_K = np.array(vir_cam_K).reshape(3, 3)
    rfu_tf_world = [1, 0, 0, -0.5 * w_m, 0, -1, 0, 0.5 * h_m, 0, 0, -1, 0, 0, 0, 0, 1]
    rfu_tf_world = np.array(rfu_tf_world).reshape(4, 4)
    vir_cam_tf_world = [
        1,
        0,
        0,
        -0.5 * w_m,
        0,
        1,
        0,
        -0.5 * h_m,
        0,
        0,
        1,
        vir_cam_height,
        0,
        0,
        0,
        1,
    ]
    vir_cam_tf_world = np.array(vir_cam_tf_world).reshape(4, 4)
    rfu_tf_vir_cam = rfu_tf_world @ np.linalg.inv(vir_cam_tf_world)
    cam_h, cam_w = undistort_image.shape[:2]
    undistort_image[0, 0] = 0
    map_x = np.zeros((size_h, size_w), dtype=np.float32)
    map_y = np.zeros((size_h, size_w), dtype=np.float32)
    ground_h = rfu_tf_vir_cam[2, 3]
    vir_cam_uvs = []
    for x in range(0, size_w, 1):
        for y in range(0, size_h, 1):
            vir_cam_uvs.append([x, y])
    cam_rays = cv2.undistortPoints(
        np.array(vir_cam_uvs, dtype=np.float32), vir_cam_K, None, None, P=np.eye(3)
    )

    vir_cam_3d_points = []
    print(f"{cam_id} begin 1 cam ray nums {len(cam_rays)}")
    for cam_ray in tqdm(cam_rays, desc=f"{cam_id} cam ray 1"):
        cam_ray_vec = np.array([cam_ray[0][0], cam_ray[0][1], 1])
        rfu_cam_ray_vec = (rfu_tf_vir_cam[:3, :3]) @ cam_ray_vec
        ground_vec = np.array([0, 0, 1])
        cos_theta = GetClockAngle(rfu_cam_ray_vec, ground_vec)
        ground_H = abs(ground_h / cos_theta) / np.linalg.norm(cam_ray_vec)
        vir_cam_3d = [cam_ray[0][0] * ground_H, cam_ray[0][1] * ground_H, ground_H, 1]
        vir_cam_3d_points.append(np.array(vir_cam_3d))
    vir_cam_3d_points = np.array(vir_cam_3d_points)
    rfu_points = (rfu_tf_vir_cam @ vir_cam_3d_points.T).T
    cam_3d_points = (cam_tf_rfu @ rfu_points.T).T
    for cam_3d_point, vir_cam_uv in zip(cam_3d_points, vir_cam_uvs):
        if cam_3d_point[2] < 0:
            continue
        vir_cam_ray = [
            cam_3d_point[0] / cam_3d_point[2],
            cam_3d_point[1] / cam_3d_point[2],
            1,
        ]
        if abs(vir_cam_ray[0]) > 5:
            continue
        vir_cam_ray = np.array(vir_cam_ray).reshape(-1, 1, 3)
        cam_uv, _ = cv2.projectPoints(
            np.array(vir_cam_ray, dtype=np.float32),
            np.zeros(3),
            np.zeros(3),
            cam_K,
            None,
            None,
        )
        cam_u = int(cam_uv[0, 0, 0])
        cam_v = int(cam_uv[0, 0, 1])
        if cam_u > (cam_w - 1) or cam_u < 0 or cam_v > (cam_h - 1) or cam_v < 0:
            continue
        map_x[vir_cam_uv[1], vir_cam_uv[0]] = cam_u
        map_y[vir_cam_uv[1], vir_cam_uv[0]] = cam_v
    save_path_ = f"{save_path}/{cam_id}_warp_ground.jpg"
    dst = cv2.remap(undistort_image, map_x, map_y, cv2.INTER_LINEAR)
    gray_warp_image_list.append(dst)
    cv2.imwrite(save_path_, dst)
    return dst


def warp_bev_img(
    cam_id_list,
    image_path_list,
    save_path,
    intrinsic_list,
    distort_list,
    cam_tf_rfu_list,
):
    global cam_id_ori_point_pts, gray_warp_image_list
    if not os.path.exists(save_path):
        os.makedirs(save_path)

    undistort_image_list = []
    new_intrinsic_K_list = []
    for index, (image_path, intrinsic, distort) in enumerate(
        zip(image_path_list, intrinsic_list, distort_list)
    ):
        image = cv2.imread(image_path, 0)
        K = intrinsic
        fx = K[0, 0] / 2
        fy = K[1, 1] / 2
        cx = K[0, 2]
        cy = K[1, 2]
        new_intrinsic_K = np.eye(3)
        new_intrinsic_K[0][0] = fx
        new_intrinsic_K[1][1] = fy
        new_intrinsic_K[0][2] = cx
        new_intrinsic_K[1][2] = cy
        print(new_intrinsic_K)
        new_intrinsic_K_list.append(new_intrinsic_K)
        print(image_path)
        if len(distort.reshape(-1, 1).tolist()) < 5:
            image = cv2.fisheye.undistortImage(
                image, intrinsic, distort, None, Knew=new_intrinsic_K
            )
            print("use fisheye")
        else:
            image = cv2.undistort(image, intrinsic, distort, None, new_intrinsic_K)
            save_path_ = f"{save_path}/undistort_{index}.jpg"
            cv2.imwrite(save_path_, image)
            print("use pinhole")
        undistort_image_list.append(image.copy())
    warp_img_map_list = []
    for cam_id, new_intrinsic, undistort_image, cam_tf_rfu in zip(
        cam_id_list, new_intrinsic_K_list, undistort_image_list, cam_tf_rfu_list
    ):
        info_dict = {}
        info_dict["cam_id"] = cam_id
        info_dict["undistort_image"] = undistort_image
        info_dict["cam_K"] = new_intrinsic
        info_dict["cam_tf_rfu"] = cam_tf_rfu
        info_dict["size_w"] = 200
        info_dict["size_h"] = 400
        info_dict["w_m"] = 20
        info_dict["h_m"] = 40
        info_dict["save_path"] = save_path
        warp_img_map_list.append(info_dict)
    print(len(warp_img_map_list))
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        results = executor.map(warp_cam_ground, warp_img_map_list)
    all_image = np.zeros((info_dict["size_h"], info_dict["size_w"], 3), dtype=np.uint8)
    for index, gray_warp_image in enumerate(gray_warp_image_list):
        i = index % 3
        all_image[:, :, i] = all_image[:, :, i] + gray_warp_image
    cv2.imwrite("bev.png", all_image)


def load_intrinsic_extrinsic(_dir, cam_id_list, cam_id_intrinsic_list):
    intrinsic_list = []
    distort_list = []
    cam_tf_rfu_list = []
    for camera_id, cam_intrinsic_id in zip(cam_id_list, cam_id_intrinsic_list):
        print("////////////////////////")
        print(camera_id)

        K = np.eye(3)
        D = []
        with open(os.path.join(_dir, "soca_cam_intrisics.yaml"), "r") as f:
            camera_info = yaml.safe_load(f)[cam_intrinsic_id]
            focal_len_y = camera_info["focal_len_y"]
            focal_len_x = camera_info["focal_len_x"]
            optical_center_x = camera_info["optical_center_x"]
            optical_center_y = camera_info["optical_center_y"]
            # if camera_id == "cam_front_120":
            #     focal_len_y =      camera_info["focal_len_y"] * 0.5
            #     focal_len_x =      camera_info["focal_len_x"] * 0.5
            #     optical_center_x = camera_info["optical_center_x"] * 0.5
            #     optical_center_y = camera_info["optical_center_y"] * 0.5
            k_5 = camera_info["k_5"]
            k_6 = camera_info["k_6"]
            k_1 = camera_info["k_1"]
            k_2 = camera_info["k_2"]
            p_1 = camera_info["p_1"]
            p_2 = camera_info["p_2"]
            k_3 = camera_info["k_3"]
            k_4 = camera_info["k_4"]
            K[0][0] = focal_len_x
            K[1][1] = focal_len_y
            K[0][2] = optical_center_x
            K[1][2] = optical_center_y
            if camera_id != "cam_front_120":
                K[1][2] = optical_center_y - 98
            D.append(k_1)
            D.append(k_2)
            D.append(p_1)
            D.append(p_2)
            D.append(k_3)
            D.append(k_4)
            D.append(k_5)
            D.append(k_6)
            D = np.array(D)
            print(K)
            print(D)
        print("////////////////////////")
        with open(os.path.join(_dir, "{}_extrinsic.json".format(camera_id)), "r") as f:
            rt = json.load(f)
            rota = rt["transform"]["rotation"]
            tran = rt["transform"]["translation"]
            q = np.quaternion(rota["w"], rota["x"], rota["y"], rota["z"])
            t = np.array([tran["x"], tran["y"], tran["z"]]).astype(np.float32)
            r = quaternion.as_rotation_matrix(q).astype(np.float32)
            cam_tf_rfu = np.eye(4)
            cam_tf_rfu[:3, :3] = r
            cam_tf_rfu[:3, 3] = t
        intrinsic_list.append(K)
        distort_list.append(D)
        cam_tf_rfu_list.append(cam_tf_rfu)
    return intrinsic_list, distort_list, cam_tf_rfu_list


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--data_path", "-s", default="", help="save path")
    parser.add_argument("--lidar_pcd_path", "-l", default="", help="lidar point cloud")
    parser.add_argument("--image_path", "-i", default="", help="image")
    args = parser.parse_args()
    args.data_path = "/home/<USER>/下载/177直线跑/"
    args.param_path = "/home/<USER>/下载/177直线跑/"
    cam_id_list = []
    cam_id_intrinsic_list = []
    cam_id_list.append("cam_back_100")
    cam_id_list.append("cam_back_left_100")
    cam_id_list.append("cam_back_right_100")
    cam_id_list.append("cam_front_120")
    cam_id_list.append("cam_front_left_100")
    cam_id_list.append("cam_front_right_100")
    cam_id_intrinsic_list.append("cam_back")
    cam_id_intrinsic_list.append("cam_side_left_back")
    cam_id_intrinsic_list.append("cam_side_right_back")
    cam_id_intrinsic_list.append("cam_front")
    cam_id_intrinsic_list.append("cam_side_left_front")
    cam_id_intrinsic_list.append("cam_side_right_front")
    image_path_list = []
    for cam_id in cam_id_list:
        image_path_list.append(f"{args.data_path}/origin_{cam_id}.jpg")
    args.save_path = "./check_bev"
    intrinsic_list, distort_list, cam_tf_rfu_list = load_intrinsic_extrinsic(
        args.param_path, cam_id_list, cam_id_intrinsic_list
    )
    warp_bev_img(
        cam_id_list,
        image_path_list,
        args.save_path,
        intrinsic_list,
        distort_list,
        cam_tf_rfu_list,
    )


if __name__ == "__main__":
    main()
