import json
import numpy as np
import quaternion
from scipy.spatial.transform import Rotation, RotationSpline
import quaternion
from scipy.spatial.transform import RotationSpline, Rotation
import yaml


def load_intrinsic(path):
    K, D = None, None
    with open(path, "r") as f:
        camera_matrix = json.load(f)
        K = np.array(camera_matrix["K"]).reshape(3, 3).astype(np.float32)
        D = np.array(camera_matrix["D"]).reshape(1, -1).astype(np.float32)
        print(D)
    return K, D


def main():
    output_path = "./soca_cam_intrisics.yaml"
    input_dir = "/home/<USER>/calibresult/car_00505/camera_params/"
    camid_input_list = [
        "cam_front_120",
        "cam_back_100",
        "cam_front_left_100",
        "cam_front_right_100",
        "cam_back_left_100",
        "cam_back_right_100",
    ]
    camid_output_list = [
        "cam_front",
        "cam_back",
        "cam_side_left_front",
        "cam_side_right_front",
        "cam_side_left_back",
        "cam_side_right_back",
    ]
    dump_data = {
        "setVersion": "1101",
    }
    for cam_id_input, cam_id_output in zip(camid_input_list, camid_output_list):
        print(cam_id_input, cam_id_output)
        K, D = load_intrinsic(input_dir + cam_id_input + "_intrinsic.json")
        focal_len_x = float(K[0, 0])
        focal_len_y = float(K[1, 1])
        optical_center_x = float(K[0, 2])
        optical_center_y = None
        if cam_id_input == "cam_front_120":
            optical_center_y = float(K[1, 2])
        else:
            optical_center_y = float(K[1, 2]) + 98
        k_1 = float(D[0][0])
        k_2 = float(D[0][1])
        k_3 = float(D[0][2])
        k_4 = float(D[0][3])
        cam_result_dict = {
            "focal_len_x": focal_len_x,
            "focal_len_y": focal_len_y,
            "optical_center_x": optical_center_x,
            "optical_center_y": optical_center_y,
            "focal_dist": 0.000000,
            "k_1": k_1,
            "k_2": k_2,
            "k_3": k_3,
            "k_4": k_4,
            "repro_err": 0.085344,
            "distortion_type": 2,  # 2为fisheye
            "crc": 133,
            "parameter_vaild": 1,
        }
        dump_data[cam_id_output] = cam_result_dict
    yaml_str = yaml.dump(dump_data, default_flow_style=False)
    print(yaml_str)
    with open(output_path, "w") as f:
        yaml.dump(dump_data, f, default_flow_style=False)


if __name__ == "__main__":
    main()
