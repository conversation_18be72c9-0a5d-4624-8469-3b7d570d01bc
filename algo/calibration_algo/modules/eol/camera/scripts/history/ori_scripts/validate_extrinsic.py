import json
import pdb
import cv2 as cv
import numpy as np
from scipy.spatial.transform import Rotation as R
import yaml


def pq2trans(p_x, p_y, p_z, q_w, q_x, q_y, q_z):
    Rm = R.from_quat([q_x, q_y, q_z, q_w]).as_matrix()
    trans = np.eye(4)
    trans[:3, :3] = Rm
    trans[0, 3] = p_x
    trans[1, 3] = p_y
    trans[2, 3] = p_z

    return trans


def load_calibroom_pts(txt_path):
    with open(txt_path, "r") as f:
        data = f.readlines()
    room_dict = {}
    for d in data:
        splited = d.rstrip().split(" ")
        room_dict[splited[0]] = np.array([splited[1], splited[2], splited[3]]).astype(
            np.float32
        )
    return room_dict


def append_tag_rfu_pts(tag_pts, calib_room, tag_id):
    tag_pts = np.vstack([tag_pts, calib_room[f"{tag_id}_leftdown"]])
    tag_pts = np.vstack([tag_pts, calib_room[f"{tag_id}_rightdown"]])
    tag_pts = np.vstack([tag_pts, calib_room[f"{tag_id}_leftup"]])
    tag_pts = np.vstack([tag_pts, calib_room[f"{tag_id}_rightup"]])
    return tag_pts


if __name__ == "__main__":
    # with open("/home/<USER>/下载/extrinsic.json", "r") as f:
    with open(
        "/home/<USER>/下载/692_extrinsic/cam_front_120_extrinsic.json", "r"
    ) as f:
        ex = json.load(f)
    # pdb.set_trace()
    cam_tf_rfu = pq2trans(
        ex["transform"]["translation"]["x"],
        ex["transform"]["translation"]["y"],
        ex["transform"]["translation"]["z"],
        ex["transform"]["rotation"]["w"],
        ex["transform"]["rotation"]["x"],
        ex["transform"]["rotation"]["y"],
        ex["transform"]["rotation"]["z"],
    )
    img = cv.imread("/home/<USER>/下载/12.2/2/origin_cam_front_120.jpg")

    # with open("/home/<USER>/下载/12.2/soca_cam_intrisics.yaml", "r") as f:
    with open(
        "/home/<USER>/下载/20231110_705_biaodingjian2/soca_cam_intrisics.yaml", "r"
    ) as f:
        intri = yaml.safe_load(f)
    K = np.eye(3)
    D = np.zeros([8])
    K[0, 0] = intri["cam_front"]["focal_len_x"]
    K[1, 1] = intri["cam_front"]["focal_len_y"]
    K[0, 2] = intri["cam_front"]["optical_center_x"]
    K[1, 2] = intri["cam_front"]["optical_center_y"]
    D[0] = intri["cam_front"]["k_1"]
    D[1] = intri["cam_front"]["k_2"]
    D[2] = intri["cam_front"]["p_1"]
    D[3] = intri["cam_front"]["p_2"]
    D[4] = intri["cam_front"]["k_3"]
    D[5] = intri["cam_front"]["k_4"]
    D[6] = intri["cam_front"]["k_5"]
    D[7] = intri["cam_front"]["k_6"]

    calib_room = load_calibroom_pts(
        "/home/<USER>/production_calibraton/data/geely_calibroom_points_map.txt"
    )

    tag_pts = np.empty([0, 3])
    tag_pts = append_tag_rfu_pts(tag_pts, calib_room, 8)
    tag_pts = append_tag_rfu_pts(tag_pts, calib_room, 9)
    tag_pts = append_tag_rfu_pts(tag_pts, calib_room, 10)
    tag_pts = append_tag_rfu_pts(tag_pts, calib_room, 11)
    tag_pts = append_tag_rfu_pts(tag_pts, calib_room, 12)
    tag_pts = append_tag_rfu_pts(tag_pts, calib_room, 13)
    tag_pts = append_tag_rfu_pts(tag_pts, calib_room, 14)
    tag_pts = append_tag_rfu_pts(tag_pts, calib_room, 15)

    ## rfu 2 cam
    tag_pts_homo = np.ones([len(tag_pts), 4])
    tag_pts_homo[:, :3] = tag_pts
    cam_pts = (cam_tf_rfu[:3] @ tag_pts_homo.T).T
    img_pts, _ = cv.projectPoints(cam_pts, np.eye(3), np.zeros(3), K, D)
    for i in range(len(img_pts)):
        cv.circle(img, img_pts[i, 0].astype(int), 5, (255, 0, 0), 2)
    # cv.imshow("front_proj", img)
    # cv.waitKey(0)
    cv.imwrite("front_proj.png", img)
