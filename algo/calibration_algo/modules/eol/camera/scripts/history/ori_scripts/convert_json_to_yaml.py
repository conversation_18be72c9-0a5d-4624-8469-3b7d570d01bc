import os, sys
import json
import numpy as np
import quaternion
from scipy.spatial.transform import Rotation, RotationSpline
import quaternion
from scipy.spatial.transform import RotationSpline, Rotation
import yaml


def load_extrinsic(path):
    with open(path, "r") as f:
        rt = json.load(f)
        rota = rt["transform"]["rotation"]
        tran = rt["transform"]["translation"]
        q = np.quaternion(rota["w"], rota["x"], rota["y"], rota["z"])
        t = np.array([tran["x"], tran["y"], tran["z"]]).astype(np.float32)
        r = quaternion.as_rotation_matrix(q).astype(np.float32)
        rt = np.eye(4)
        rt[:3, :3] = r
        rt[:3, 3] = t
    return rt


def main():
    output_dir = "./build/output/sample/E171"
    output_path = "driving_cam_calibration_result.yaml"
    input_dir = "./build/output/sample/E171"
    camid_input_list = [
        "cam_front_120",
        "cam_back_100",
        "cam_front_left_100",
        "cam_front_right_100",
        "cam_back_left_100",
        "cam_back_right_100",
    ]
    camid_output_list = [
        "cam_front",
        "cam_back",
        "cam_side_left_front",
        "cam_side_right_front",
        "cam_side_left_back",
        "cam_side_right_back",
    ]
    dump_data = {
        "VIN": "136 bit ascii code",
    }
    for cam_id_input, cam_id_output in zip(camid_input_list, camid_output_list):
        print(cam_id_input, cam_id_output)
        cam_tf_rfu = load_extrinsic(
            os.path.join(input_dir, cam_id_input + "_extrinsic.json")
        )
        print(cam_tf_rfu)
        avg_tvec = cam_tf_rfu[:, 3]
        avg_quat = quaternion.from_rotation_matrix(cam_tf_rfu[:3, :3])
        avg_tvec = list(map(float, avg_tvec))
        main_r_sub = Rotation.from_matrix(cam_tf_rfu[:3, :3])
        euler = main_r_sub.as_euler("XYZ", degrees=True)
        cam_result_dict = {
            "calib_errcode": 0,
            "euler_degree": {
                "RotX": float(euler[0]),
                "RotY": float(euler[1]),
                "RotZ": float(euler[2]),
            },
            "translation": {"x": avg_tvec[0], "y": avg_tvec[1], "z": avg_tvec[2]},
            "rotation": {
                "x": avg_quat.x,
                "y": avg_quat.y,
                "z": avg_quat.z,
                "w": avg_quat.w,
            },
            "reprojection_error": 0.0,
            "calib_status": 0,
            "calib_valid": 1,
            "calib_mode": 0,
            "calib_time": "1900-01-01 00:00:00",
            "information": f"{cam_id_input}_tf_rfu",
        }
        dump_data[cam_id_output] = cam_result_dict
    yaml_str = yaml.dump(dump_data, default_flow_style=False)
    print(yaml_str)
    with open(os.path.join(output_dir, output_path), "w") as f:
        yaml.dump(dump_data, f, default_flow_style=False)


if __name__ == "__main__":
    main()
