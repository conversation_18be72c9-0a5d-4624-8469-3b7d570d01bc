import pdb
import numpy as np

diff0 = (
    np.array([-1.02415, 0.0327345, 0.890524])
    - np.array([-1000.066, 32.265, 571.528 + 332.4]) / 1000
)
print(diff0 * 100)

diff1 = (
    np.array([2.50137, 0.959583, 0.763285])
    - np.array([2522.649, 949.71, 446.29 + 332.4]) / 1000
)
print(diff1 * 100)

diff2 = (
    np.array([2.51084, -0.94403, 0.796295])
    - np.array([2522.649, -949.71, 446.29 + 332.4]) / 1000
)
print(diff2 * 100)

diff3 = (
    np.array([1.94747, -0.0414638, 1.29936])
    - np.array([1947.746, -42.009, 957.742 + 332.4]) / 1000
)
print(diff3 * 100)

diff4 = (
    np.array([2.21316, 0.900667, 0.967523])
    - np.array([2224.062, 890.456, 639.379 + 332.4]) / 1000
)
print(diff4 * 100)

# diff5 = np.array([2.23103, -0.949404, 0.974956]) - np.array([2224.062, -890.456, 639.379 + 332.4]) / 1000
# print(diff5 * 100)
