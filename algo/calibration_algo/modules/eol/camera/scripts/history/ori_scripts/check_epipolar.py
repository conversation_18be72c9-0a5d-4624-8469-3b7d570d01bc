import sys
import cv2 as cv
import json
from scipy.spatial.transform import Rotation as R
import numpy as np
import copy
import yaml
import math

clicked_pts = []

e171_name_map = {
    "cam_front_120": "cam_front",
    "cam_front_left_100": "cam_side_left_front",
    "cam_front_right_100": "cam_side_right_front",
    "cam_back_left_100": "cam_side_left_back",
    "cam_back_right_100": "cam_side_right_back",
    "cam_back_100": "cam_side_left_back",
}


def load_ext_from_json(path):
    with open(path, "r") as f:
        json_data = json.load(f)
    T = np.eye(4)
    T[:3, :3] = R.from_quat(
        [
            json_data["transform"]["rotation"]["x"],
            json_data["transform"]["rotation"]["y"],
            json_data["transform"]["rotation"]["z"],
            json_data["transform"]["rotation"]["w"],
        ]
    ).as_matrix()
    T[0, 3] = json_data["transform"]["translation"]["x"]
    T[1, 3] = json_data["transform"]["translation"]["y"]
    T[2, 3] = json_data["transform"]["translation"]["z"]

    return T


def load_intrinsic_yaml(path, cam_id):
    K, D = np.eye(3), np.zeros(8)
    with open(path, "r") as f:
        data = yaml.safe_load(f)

        K[0, 0] = data[cam_id]["focal_len_x"]
        K[1, 1] = data[cam_id]["focal_len_y"]
        K[0, 2] = data[cam_id]["optical_center_x"]
        K[1, 2] = data[cam_id]["optical_center_y"]
        if cam_id != "cam_front":
            K[1, 2] -= 98

        D[0] = data[cam_id]["k_1"]
        D[1] = data[cam_id]["k_2"]
        D[2] = data[cam_id]["p_1"]
        D[3] = data[cam_id]["p_2"]
        D[4] = data[cam_id]["k_3"]
        D[5] = data[cam_id]["k_4"]
        D[6] = data[cam_id]["k_5"]
        D[7] = data[cam_id]["k_6"]

    return K, D


def construct_skew_mat(rot, trans):
    ## R t -> t^R

    tx, ty, tz = trans[0], trans[1], trans[2]
    t_up = np.array([[0, -tz, ty], [tz, 0, -tx], [-ty, tx, 0]])
    return t_up @ rot


def MouseCallback(event, x, y, flags, param):
    if event == cv.EVENT_LBUTTONDOWN:
        clicked_pts.append([x, y])


def nothing(x):
    pass


if __name__ == "__main__":
    if len(sys.argv) != 8:
        print(
            "Usage: python check_epipolar.py <cam0_name> <cam1_name> </path/to/img0> </path/to/img1> </path/to/cam0_extrinsic.json> </path/to/cam1_extrinsic.json> </path/to/soca_cam_intrisics.yaml>"
        )
        sys.exit(1)

    img0 = cv.imread(sys.argv[3])
    img1 = cv.imread(sys.argv[4])
    K0, D0 = load_intrinsic_yaml(sys.argv[7], e171_name_map[sys.argv[1]])
    K1, D1 = load_intrinsic_yaml(sys.argv[7], e171_name_map[sys.argv[2]])

    cam0_tf_cam1 = load_ext_from_json(sys.argv[5]) @ np.linalg.inv(
        load_ext_from_json(sys.argv[6])
    )

    cam1_tf_cam0 = np.linalg.inv(cam0_tf_cam1)

    map0_x, map0_y = cv.initUndistortRectifyMap(
        K0, D0, None, K0, (img0.shape[1], img0.shape[0]), cv.CV_32FC1
    )
    map1_x, map1_y = cv.initUndistortRectifyMap(
        K1, D1, None, K1, (img1.shape[1], img1.shape[0]), cv.CV_32FC1
    )

    img0 = cv.remap(img0, map0_x, map0_y, cv.INTER_LINEAR)
    img1 = cv.remap(img1, map1_x, map1_y, cv.INTER_LINEAR)

    if sys.argv[1] == "cam_front_120":
        img0 = cv.resize(img0, (img0.shape[1] // 2, img0.shape[0] // 2))
        K0 /= 2
        K0[2, 2] = 1
    if sys.argv[2] == "cam_front_120":
        img1 = cv.resize(img1, (img1.shape[1] // 2, img1.shape[0] // 2))
        K1 /= 2
        K1[2, 2] = 1

    cv.namedWindow("show_epipolar", cv.WINDOW_NORMAL)
    cv.setMouseCallback("show_epipolar", MouseCallback)
    cv.createTrackbar("pitch", "show_epipolar", 50, 100, nothing)
    cv.createTrackbar("yaw", "show_epipolar", 50, 100, nothing)
    cv.createTrackbar("roll", "show_epipolar", 50, 100, nothing)
    color = tuple(np.random.randint(0, 255, 3).tolist())

    while True:
        img0_show = copy.deepcopy(img0)
        img1_show = copy.deepcopy(img1)

        pitch = cv.getTrackbarPos("pitch", "show_epipolar")
        yaw = cv.getTrackbarPos("yaw", "show_epipolar")
        roll = cv.getTrackbarPos("roll", "show_epipolar")
        pitch = (pitch - 50) * 0.05
        yaw = (yaw - 50) * 0.05
        roll = (roll - 50) * 0.05
        delta_R = R.from_euler("xyz", [roll, pitch, yaw], degrees=True).as_matrix()
        delta_T = np.eye(4)
        delta_T[:3, :3] = delta_R
        show_cam1_tf_cam0 = delta_T @ cam1_tf_cam0

        E10 = construct_skew_mat(show_cam1_tf_cam0[:3, :3], show_cam1_tf_cam0[:3, 3])
        F10 = np.linalg.inv(K1.T) @ E10 @ np.linalg.inv(K0)

        if len(clicked_pts) > 0:
            for pt in clicked_pts:
                cv.circle(img0_show, (pt[0], pt[1]), 3, (0, 0, 255), -1)
            lines1 = cv.computeCorrespondEpilines(np.int0(clicked_pts), 1, F10).reshape(
                -1, 3
            )

            for r in lines1:
                x0, y0 = map(int, [0, -r[2] / r[1]])
                x1, y1 = map(
                    int,
                    [img1_show.shape[1], -(r[2] + r[0] * img1_show.shape[1]) / r[1]],
                )
                img1_show = cv.line(img1_show, (x0, y0), (x1, y1), color, 1)

        img_out = cv.hconcat([img0_show, img1_show])
        cv.imshow("show_epipolar", img_out)
        key = cv.waitKey(100)
        if key == ord("q"):
            break
