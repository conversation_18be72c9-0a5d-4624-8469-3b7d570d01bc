import os, sys
import numpy as np
import json
from scipy.spatial.transform import Rotation as R


output_dir = "./build/output/sample/E171"
if not os.path.exists(output_dir):
    os.makedirs(output_dir, exist_ok=True)

np.set_printoptions(suppress=True)


def pq2trans(p_x, p_y, p_z, q_w, q_x, q_y, q_z):
    Rm = R.from_quat([q_x, q_y, q_z, q_w]).as_matrix()
    trans = np.eye(4)
    trans[:3, :3] = Rm
    trans[0, 3] = p_x
    trans[1, 3] = p_y
    trans[2, 3] = p_z

    return trans


def cam_tf_rfu_to_ego_tf_cam(T):
    # input cam_tf_rfu_T
    # output ego_tf_CAM_t
    t = T[:3, 3]
    t = T[:3, :3].T @ -t
    ego_tf_rfu = np.array([[0, 1, 0], [-1, 0, 0], [0, 0, 1]])
    t = ego_tf_rfu @ t
    return t


def give_ex_result(T, extrinsic_dict):
    extrinsic_dict["transform"]["translation"]["x"] = T[0, 3]
    extrinsic_dict["transform"]["translation"]["y"] = T[1, 3]
    extrinsic_dict["transform"]["translation"]["z"] = T[2, 3]
    q = R.from_matrix(T[:3, :3]).as_quat()
    extrinsic_dict["transform"]["rotation"]["w"] = q[3]
    extrinsic_dict["transform"]["rotation"]["x"] = q[0]
    extrinsic_dict["transform"]["rotation"]["y"] = q[1]
    extrinsic_dict["transform"]["rotation"]["z"] = q[2]
    rpy = R.from_matrix(T[:3, :3]).as_euler("xyz", degrees=True)
    extrinsic_dict["euler_degree"]["RotX"] = rpy[0]
    extrinsic_dict["euler_degree"]["RotY"] = rpy[1]
    extrinsic_dict["euler_degree"]["RotZ"] = rpy[2]

    return extrinsic_dict


t0_structure = np.array([-1.007803, 0.032265, 0.915535])
t1_structure = np.array([2.519305, 0.952585, 0.788477])
t2_structure = np.array([2.519305, -0.952585, 0.788477])
t3_structure = np.array([1.944972, 0.000009, 1.3073238])
t4_structure = np.array([2.219093, 0.905678, 0.977109])
t5_structure = np.array([2.220086, -0.905641, 0.974649])

T0s = []
T1s = []
T2s = []
T3s = []
T4s = []
T5s = []

# # cam_back_100
T0 = pq2trans(
    -0.013968, 0.934142, -0.992236, -0.00012052, 0.00659721, 0.714456, -0.69965
)
# # cam_back_left_100
T1 = pq2trans(-2.58312, 0.778312, 0.768953, 0.315636, 0.307585, 0.631057, -0.638383)
# # cam_back_right_100
T2 = pq2trans(2.58287, 0.770985, 0.761444, 0.323059, 0.298484, -0.625555, 0.644377)
# # cam_front_120
T3 = pq2trans(
    0.0102733, 1.28756, -1.96069, 0.710646, 0.703542, -0.00262797, -0.00199944
)
# # cam_front_left_100
T4 = pq2trans(-1.12517, 0.971192, -2.12217, 0.639986, 0.637477, 0.295329, -0.311163)
# # cam_front_right_100
T5 = pq2trans(1.1377, 0.959892, -2.10594, 0.642977, 0.638623, -0.307598, 0.290042)

T0s.append(T0)
T1s.append(T1)
T2s.append(T2)
T3s.append(T3)
T4s.append(T4)
T5s.append(T5)

T0 = pq2trans(
    -0.017921, 0.92426, -1.00117, -0.00358379, -0.00524028, -0.710928, 0.703236
)

T1 = pq2trans(-2.57577, 0.647448, 0.895977, 0.315191, 0.287281, 0.650276, -0.628701)

T2 = pq2trans(2.50558, 0.88333, 0.879653, 0.284777, 0.308647, -0.63652, 0.646901)

T3 = pq2trans(0.0380667, 1.28555, -1.96078, 0.710936, 0.703195, -0.00717915, 0.0059273)

T4 = pq2trans(-1.12003, 0.994927, -2.10628, 0.638544, 0.644306, 0.297572, -0.297628)

T5 = pq2trans(1.16924, 0.935477, -2.1149, 0.644154, 0.632427, -0.310622, 0.297683)

T0s.append(T0)
T1s.append(T1)
T2s.append(T2)
T3s.append(T3)
T4s.append(T4)
T5s.append(T5)

T0 = pq2trans(0.0291725, 0.925697, -1.00629, 0.0157196, 0.0216023, 0.710896, -0.70279)

T1 = pq2trans(-2.57622, 0.646848, 0.878738, 0.316748, 0.283685, 0.649125, -0.630738)

T2 = pq2trans(2.56747, 0.654987, 0.891152, 0.314034, 0.279146, -0.649774, 0.633446)

T3 = pq2trans(
    0.0305623, 1.28869, -1.95537, 0.710437, 0.703727, -0.00685161, -0.000873175
)

T4 = pq2trans(-1.11452, 0.988611, -2.10576, 0.638263, 0.640882, 0.29435, -0.308625)

T5 = pq2trans(1.14822, 0.925777, -2.11742, 0.647012, 0.632852, -0.30714, 0.294175)

T0s.append(T0)
T1s.append(T1)
T2s.append(T2)
T3s.append(T3)
T4s.append(T4)
T5s.append(T5)

T0 = pq2trans(
    0.00227775, 0.985456, -0.945121, 0.0131552, -0.0127421, -0.73425, 0.678632
)

T1 = pq2trans(-2.57736, 0.637279, 0.883072, 0.314039, 0.285454, 0.652606, -0.627696)

T2 = pq2trans(2.55879, 0.705477, 0.886011, 0.310554, 0.284519, -0.643339, 0.639312)

T3 = pq2trans(
    0.00872032, 1.29179, -1.95207, 0.709867, 0.704331, -0.00219817, -0.00161915
)

T4 = pq2trans(-1.15098, 0.979395, -2.08923, 0.637269, 0.639624, 0.307135, -0.300727)

T5 = pq2trans(1.14391, 0.935432, -2.11034, 0.645224, 0.635732, -0.308208, 0.290755)

T0s.append(T0)
T1s.append(T1)
T2s.append(T2)
T3s.append(T3)
T4s.append(T4)
T5s.append(T5)

T0 = pq2trans(
    -0.0332596, 0.956214, -0.972725, 0.00498084, 0.000382267, -0.723313, 0.690502
)

T1 = pq2trans(-2.55402, 0.707901, 0.899393, 0.304579, 0.288498, 0.647853, -0.635836)

T2 = pq2trans(2.56253, 0.692935, 0.894054, 0.310318, 0.287128, -0.646988, 0.634561)

T3 = pq2trans(0.0125146, 1.27894, -1.96351, 0.712158, 0.702003, -0.00339532, -0.003496)

T4 = pq2trans(-1.14811, 0.949248, -2.10935, 0.642172, 0.634325, 0.302895, -0.305781)

T5 = pq2trans(1.13055, 0.927072, -2.1246, 0.647742, 0.634686, -0.302706, 0.293211)

T0s.append(T0)
T1s.append(T1)
T2s.append(T2)
T3s.append(T3)
T4s.append(T4)
T5s.append(T5)


## average on rotations
import pdb
import pytransform3d.transformations
from scipy.linalg import logm, expm


def is_skew_symmetric(matrix, tolerance=1e-10):
    return np.allclose(matrix, -matrix.T, atol=tolerance)


# Convert a rotation matrix to an element of the Lie algebra so(3)
def rotation_to_lie_algebra(rotation_matrix):
    # Compute the matrix logarithm of the rotation matrix
    lie_algebra_element = logm(rotation_matrix)

    # Since numerical errors might cause the result to not be exactly skew-symmetric,
    # we can project it onto the space of skew-symmetric matrices
    if is_skew_symmetric(lie_algebra_element):
        return lie_algebra_element
    else:
        print(
            "The input is likely not a proper rotation matrix or there are numerical issues."
        )
        return None


def skew_matrix_to_vector(mat):
    vec = np.zeros([3])
    vec[0] = mat[2, 1]
    vec[1] = mat[0, 2]
    vec[2] = mat[1, 0]
    return vec


def vector_to_skew_matrix(vec):
    mat = np.zeros([3, 3])
    mat[0, 1] = -vec[2]
    mat[0, 2] = vec[1]
    mat[1, 0] = vec[2]
    mat[1, 2] = -vec[0]
    mat[2, 0] = -vec[1]
    mat[2, 1] = vec[0]
    return mat


# Ts = [T0s, T1s, T2s, T3s, T4s, T5s]
# structures = [t0_structure, t1_structure, t2_structure, t3_structure, t4_structure, t5_structure]

Ts = [T0s, T1s, T2s, T3s, T4s, T5s]
structures = [
    t0_structure,
    t1_structure,
    t2_structure,
    t3_structure,
    t4_structure,
    t5_structure,
]

results = []

## average on transformation
for i in range(len(Ts)):
    curr_Ts = Ts[i]
    average_lie = np.zeros([3])
    average_t = np.zeros([3])
    for j in range(len(curr_Ts)):
        average_t += curr_Ts[j][:3, 3]
        add_rotvec = R.from_matrix(curr_Ts[j][:3, :3]).as_rotvec()
        if i == 0 and (j == 0 or j == 3 or j == 4):
            add_rotvec = -add_rotvec
        print(add_rotvec)
        average_lie += add_rotvec

    xyzw = R.from_rotvec(average_lie / len(curr_Ts)).as_quat()
    average_t /= len(curr_Ts)
    T = np.eye(4)
    T[:3, :3] = R.from_quat(xyzw).as_matrix()
    T[:3, 3] = average_t
    print(f"diff: {(cam_tf_rfu_to_ego_tf_cam(T) - structures[i]) * 100}")
    print(f"xyzw: {R.from_matrix(T[:3, :3]).as_quat()}")
    results.append(T)
    # break


extrinsic_dict = {}
extrinsic_dict["transform"] = {}
extrinsic_dict["transform"]["translation"] = {}
extrinsic_dict["transform"]["translation"]["x"] = 0
extrinsic_dict["transform"]["translation"]["y"] = 0
extrinsic_dict["transform"]["translation"]["z"] = 0
extrinsic_dict["transform"]["rotation"] = {}
extrinsic_dict["transform"]["rotation"]["w"] = 0
extrinsic_dict["transform"]["rotation"]["x"] = 0
extrinsic_dict["transform"]["rotation"]["y"] = 0
extrinsic_dict["transform"]["rotation"]["z"] = 0
extrinsic_dict["euler_degree"] = {}
extrinsic_dict["euler_degree"]["RotX"] = 0
extrinsic_dict["euler_degree"]["RotY"] = 0
extrinsic_dict["euler_degree"]["RotZ"] = 0
extrinsic_dict["calib_status"] = 0
extrinsic_dict["information"] = ""
extrinsic_dict["calib_time"] = "2023-12-06 13:56:24"
## cam_back_100
extrinsic_dict = give_ex_result(results[0], extrinsic_dict)
extrinsic_dict["information"] = "cam_back_100_tf_rfu"
with open(f"{output_dir}/cam_back_100_extrinsic.json", "w") as f:
    json.dump(extrinsic_dict, f, indent=4)

## cam_back_left_100
extrinsic_dict = give_ex_result(results[1], extrinsic_dict)
extrinsic_dict["information"] = "cam_back_left_100_tf_rfu"
with open(f"{output_dir}/cam_back_left_100_extrinsic.json", "w") as f:
    json.dump(extrinsic_dict, f, indent=4)

## cam_back_right_100
extrinsic_dict = give_ex_result(results[2], extrinsic_dict)
extrinsic_dict["information"] = "cam_back_right_100_tf_rfu"
with open(f"{output_dir}/cam_back_right_100_extrinsic.json", "w") as f:
    json.dump(extrinsic_dict, f, indent=4)

## cam_front_120
extrinsic_dict = give_ex_result(results[3], extrinsic_dict)
extrinsic_dict["information"] = "cam_front_120_tf_rfu"
with open(f"{output_dir}/cam_front_120_extrinsic.json", "w") as f:
    json.dump(extrinsic_dict, f, indent=4)

## cam_front_left_100
extrinsic_dict = give_ex_result(results[4], extrinsic_dict)
extrinsic_dict["information"] = "cam_front_left_100_tf_rfu"
with open(f"{output_dir}/cam_front_left_100_extrinsic.json", "w") as f:
    json.dump(extrinsic_dict, f, indent=4)

## cam_front_right_100
extrinsic_dict = give_ex_result(results[5], extrinsic_dict)
extrinsic_dict["information"] = "cam_front_right_100_tf_rfu"
with open(f"{output_dir}/cam_front_right_100_extrinsic.json", "w") as f:
    json.dump(extrinsic_dict, f, indent=4)


print("***double check****")
print(results[0][:3, :3] - T0s[0][:3, :3])
print(results[1][:3, :3] - T1s[0][:3, :3])
print(results[2][:3, :3] - T2s[0][:3, :3])
print(results[3][:3, :3] - T3s[0][:3, :3])
print(results[4][:3, :3] - T4s[0][:3, :3])
print(results[5][:3, :3] - T5s[0][:3, :3])
