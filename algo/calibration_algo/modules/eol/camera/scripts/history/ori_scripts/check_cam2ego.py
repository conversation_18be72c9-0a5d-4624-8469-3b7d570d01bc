# -*- coding: utf-8 -*-
import cv2
import argparse
from pathlib import Path
import os
import numpy as np
import math
import sys
import pdb
import json
import quaternion
import transforms3d
import time
from scipy.spatial.transform import Rotation
from pyquaternion import Quaternion
import copy

# image_path = "/home/<USER>/下载/a.jpg"
# image_path = "/home/<USER>/下载/person_2/3171.jpg"
# image_path = "/home/<USER>/下载/12.2/2/origin_cam_front_120.jpg"
image_path = "/home/<USER>/2024-1-4-178标定间1/2/origin_cam_front_120.jpg"
cam_tf_rfu_json = {
    "transform": {
        "translation": {"x": 0.0103609, "y": 1.27309, "z": -1.96466},
        "rotation": {
            "w": 0.7132168818763684,
            "x": 0.7009398839096959,
            "y": -0.002031179663594168,
            "z": 0.0009126118488523918,
        },
    },
    "euler_degree": {
        "RotX": 89.57965022683909,
        "RotY": -0.16145737051702616,
        "RotZ": -0.1345143458073617,
    },
    "calib_status": 0,
    "information": "cam_front_120_tf_rfu",
    "calib_time": "2023-11-21 13:04:12",
}

cam_intri_json = {
    "resolution": [3840, 2160],
    "distortion_model": "pinhole",
    "K": [
        [2428.15918, 0.0, 1920.239502],
        [0.0, 2428.121094, 1081.382568],
        [0.0, 0.0, 1.0],
    ],
    "D": [
        [8.352897],
        [4.277428],
        [2.8e-05],
        [1.7e-05],
        [0.15031],
        [9.044541],
        [9.490074],
        [1.373823],
    ],
}


def load_ext(rt):
    rota = rt["transform"]["rotation"]
    tran = rt["transform"]["translation"]
    q = Quaternion([rota["w"], rota["x"], rota["y"], rota["z"]])
    t = np.array([tran["x"], tran["y"], tran["z"]]).astype(np.float32)
    r = q.rotation_matrix.astype(np.float32)
    rt = np.eye(4)
    rt[:3, :3] = r
    rt[:3, 3] = t
    return rt


T_ego2footprint_json = {
    "transform": {
        "translation": {"x": 0, "y": 0, "z": 0.33},
        "rotation": {"w": 1.0, "x": 0.0, "y": 0.0, "z": 0.0},
    }
}
T_ego2footprint = load_ext(T_ego2footprint_json)
T_footprint2footprint_RFU = np.asarray(
    [[0, -1, 0, 0], [1, 0, 0, 0], [0, 0, 1, 0], [0, 0, 0, 1]]
)
rfu_tf_ego = T_footprint2footprint_RFU @ T_ego2footprint
cam_tf_rfu = load_ext(cam_tf_rfu_json)
ori_camx_tf_ego = cam_tf_rfu @ rfu_tf_ego


def nothing(x):
    pass


def dump_result(final_transformation, information, data_path):
    avg_tvec = final_transformation[:, 3]
    avg_quat = quaternion.from_rotation_matrix(final_transformation[:3, :3])
    avg_tvec = list(map(float, avg_tvec))
    main_r_sub = Rotation.from_matrix(final_transformation[:3, :3])
    euler = main_r_sub.as_euler("XYZ", degrees=True)
    res = {
        "transform": {
            "translation": {
                "x": avg_tvec[0],
                "y": avg_tvec[1],
                "z": avg_tvec[2],
            },
            "rotation": {
                "w": avg_quat.w,
                "x": avg_quat.x,
                "y": avg_quat.y,
                "z": avg_quat.z,
            },
        },
        "euler_degree": {
            "RotX": euler[0],
            "RotY": euler[1],
            "RotZ": euler[2],
        },
        "calib_status": 0,
        "information": information,
        "calib_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
    }
    with open(data_path, "w") as f:
        json.dump(
            res,
            f,
            indent=4,
        )


mode = 0
image_show_pitch_yaw_copy = None
c_ground_pts = []
c_vertical_pts = []


def OnMouseGround(event, x, y, flags, param):
    if event == cv2.EVENT_LBUTTONDOWN:
        c_ground_pts.append([x, y])


def OnMouseVetical(event, x, y, flags, param):
    if event == cv2.EVENT_LBUTTONDOWN:
        c_vertical_pts.append([x, y])


def getLinearEquation(p1x, p1y, p2x, p2y):
    sign = 1
    a = p2y - p1y
    if a < 0:
        sign = -1
        a = sign * a
    b = sign * (p1x - p2x)
    c = sign * (p1y * p2x - p1x * p2y)
    return [a, b, c]


def check_cam_ego(distortion_model, intrinsic, distort):
    global mode
    global image_path
    K = intrinsic
    fx = K[0, 0]
    fy = K[1, 1]
    cx = K[0, 2]
    cy = K[1, 2]
    new_intrinsic_K = np.eye(3)
    new_intrinsic_K[0][0] = fx
    new_intrinsic_K[1][1] = fy
    new_intrinsic_K[0][2] = cx
    new_intrinsic_K[1][2] = cy
    intrinsic = np.matrix(new_intrinsic_K)
    new_intrinsic = np.matrix(
        [
            [1000.05988153, 0.00000000e00, 1907.08103852],
            [0.00000000e00, 1000.98728985, 1117.1254331],
            [0.00000000e00, 0.00000000e00, 1.00000000e00],
        ]
    )
    extrinsic = ori_camx_tf_ego
    save_path = "./"
    if not os.path.exists(save_path):
        os.makedirs(save_path)
    image = cv2.imread(image_path)
    image_o = cv2.imread(image_path)
    if len(distort.reshape(-1, 1).tolist()) < 5:
        image = cv2.fisheye.undistortImage(
            image, intrinsic, distort, None, Knew=intrinsic
        )
        print("use fisheye")
    else:
        image = cv2.undistort(image, intrinsic, distort, None, intrinsic)
        print("use pinhole")
    H, W = image.shape[:2]
    vis_dis_thresh = 10
    vir_cam_height = 10
    rfu_tf_world = [
        1,
        0,
        0,
        -0.5 * vis_dis_thresh,
        0,
        -1,
        0,
        0.5 * vis_dis_thresh,
        0,
        0,
        -1,
        0,
        0,
        0,
        0,
        1,
    ]
    rfu_tf_world = np.array(rfu_tf_world).reshape(4, 4)
    vir_cam_tf_world = [
        1,
        0,
        0,
        -0.5 * vis_dis_thresh,
        0,
        1,
        0,
        -0.5 * vis_dis_thresh,
        0,
        0,
        1,
        vir_cam_height,
        0,
        0,
        0,
        1,
    ]
    vir_cam_tf_world = np.array(vir_cam_tf_world).reshape(4, 4)
    vir_cam_k = [100, 0, 1000, 0, 100, 1000, 0, 0, 1]
    vir_cam_k = np.array(vir_cam_k).reshape(3, 3)
    pitch_yaw_points = []
    yaw_roll_points = []
    for i in range(-100, 100):
        pitch_yaw_points.append([1000, i, 0, 1])
        pitch_yaw_points.append([1000, 0, i, 1])
        yaw_roll_points.append([i, 0, -1000, 1])
        yaw_roll_points.append([0, i, -1000, 1])
    pitch_yaw_points = np.array(pitch_yaw_points)
    yaw_roll_points = np.array(yaw_roll_points)
    cv2.namedWindow("show", cv2.WINDOW_NORMAL)
    cv2.namedWindow("show_pitch_yaw", cv2.WINDOW_NORMAL)
    cv2.createTrackbar("pitch", "show", 50, 100, nothing)
    cv2.createTrackbar("yaw", "show", 50, 100, nothing)
    cv2.createTrackbar("roll", "show", 50, 100, nothing)
    cv2.createTrackbar("roll_pitch", "show", 80, 100, nothing)
    cv2.setMouseCallback("show_pitch_yaw", OnMouseGround)
    extrinsic_copy = extrinsic
    while True:
        image_show_pitch_yaw_copy = copy.deepcopy(image)
        pitch = cv2.getTrackbarPos("pitch", "show")
        yaw = cv2.getTrackbarPos("yaw", "show")
        roll = cv2.getTrackbarPos("roll", "show")
        roll_pitch = cv2.getTrackbarPos("roll_pitch", "show")
        pitch = (pitch - 50) * 0.05 * math.pi / 180.0
        yaw = (yaw - 50) * 0.05 * math.pi / 180.0
        roll = (roll - 50) * 0.05 * math.pi / 180.0
        roll_pitch = (roll_pitch - 50) * 2 * math.pi / 180.0
        delta_R = transforms3d.euler.euler2mat(pitch, yaw, roll, "sxyz")
        delta_T = np.eye(4)
        delta_T[:3, :3] = delta_R
        extrinsic_copy = delta_T @ extrinsic
        proj_matrix = intrinsic @ extrinsic_copy[:3, :4]
        pitch_yaw_points_image = (proj_matrix @ pitch_yaw_points.T).T
        H, W = image.shape[:2]
        for i, pt in enumerate(pitch_yaw_points_image):
            u = pt[0, 0]
            v = pt[0, 1]
            flag = pt[0, 2]
            u = int(u / flag)
            v = int(v / flag)
            if (0 <= u and u < W) and (0 <= v and v < H):
                cv2.circle(image_show_pitch_yaw_copy, (u, v), 1, (0, 0, 255), 2)
        # cv2.imwrite("drawed.png", image_show_pitch_yaw_copy)
        w_r_c = Rotation.from_euler("XYZ", [roll_pitch, 0, 0], degrees=False)
        diff = np.eye(4)
        diff[:3, :3] = w_r_c.as_matrix()
        half_road_x = 3.75 / 2.0
        road_y_max = 100
        road_y_1 = 60
        road_y_0 = 40
        road_y_min = 10
        ego_p_list = []
        ego_p_list.append(np.array([road_y_min, half_road_x, -0.33, 1]))
        ego_p_list.append(np.array([road_y_min, -half_road_x, -0.33, 1]))
        ego_p_list.append(np.array([road_y_0, half_road_x, -0.33, 1]))
        ego_p_list.append(np.array([road_y_0, -half_road_x, -0.33, 1]))
        ego_p_list.append(np.array([road_y_1, half_road_x, -0.33, 1]))
        ego_p_list.append(np.array([road_y_1, -half_road_x, -0.33, 1]))
        ego_p_list.append(np.array([road_y_max, half_road_x, -0.33, 1]))
        ego_p_list.append(np.array([road_y_max, -half_road_x, -0.33, 1]))
        ego_p_list = np.array(ego_p_list)
        rfu_uv_list = []
        rfu_uv_points = (proj_matrix @ ego_p_list.T).T
        for i, pt in enumerate(rfu_uv_points):
            u = pt[0, 0]
            v = pt[0, 1]
            flag = pt[0, 2]
            u = int(u / flag)
            v = int(v / flag)
            cv2.circle(image_show_pitch_yaw_copy, (u, v), 1, (255, 0, 0), 2)
            rfu_uv_list.append([int(u), int(v)])
        for i, c_ground_pt in enumerate(c_ground_pts):
            u = c_ground_pt[0]
            v = c_ground_pt[1]
            cv2.circle(image_show_pitch_yaw_copy, (u, v), 1, (0, 0, 255), 2)
            if (i + 1) % 2 != 0:
                continue
            a, b, c = getLinearEquation(
                u, v, c_ground_pts[i - 1][0], c_ground_pts[i - 1][1]
            )
            cv2.line(
                image_show_pitch_yaw_copy,
                (int(-1 * (b * H + c) / a), H),
                (int(-c / a), 0),
                (0, 255, 0),
                3,
            )  # 绿色，3个像素宽度
        cv2.imshow("show_pitch_yaw", image_show_pitch_yaw_copy)
        key = cv2.waitKey(100)
        if key == ord("g"):
            mode = 0
        if key == ord("c"):
            c_vertical_pts.pop()
            c_ground_pts.pop()
        if key == ord("q"):
            break
    cv2.destroyWindow("show")
    cv2.destroyWindow("show_pitch_yaw")
    R = extrinsic_copy[:3, :3]
    t = extrinsic_copy[:3, 3]
    q = transforms3d.quaternions.mat2quat(R)
    output_path_ = os.path.join(save_path, f"extrinsic.json")
    output_path = os.path.join(save_path, f"cam_ego.json")
    cam_tf_ego_refine = extrinsic_copy
    cam_tf_rfu_refine = cam_tf_ego_refine @ np.linalg.inv(rfu_tf_ego)
    dump_result(cam_tf_rfu_refine, f"cam_from_footprint_RFU", output_path_)
    dump_result(np.linalg.inv(cam_tf_rfu_refine), f"rfu_tf_cam", output_path)
    virtual_cam_r_ego = np.matrix(
        [[0.0, -1, 0.0, 0], [-1, 0, 0, 0], [0.0, 0.0, -1, 0], [0, 0, 0, 1]]
    )
    virtual_cam_r_cam_x = virtual_cam_r_ego @ np.linalg.inv(cam_tf_ego_refine)
    new_k = np.matrix(
        [
            [20, 0.00000000e00, 1912.30452319],
            [0.00000000e00, 20, 2048.34280002],
            [0.00000000e00, 0.00000000e00, 1.00000000e00],
        ]
    )
    mapx, mapy = cv2.initUndistortRectifyMap(
        intrinsic, distort, (virtual_cam_r_cam_x[:3, :3]), new_k, (W, H), cv2.CV_16SC2
    )
    dst = cv2.remap(
        image_o, mapx, mapy, cv2.INTER_LINEAR, borderMode=cv2.BORDER_CONSTANT
    )
    cv2.imwrite("refine_ground.jpg", dst)


def main():
    parser = argparse.ArgumentParser()
    distortion_model = cam_intri_json["distortion_model"]
    K = cam_intri_json["K"]
    D = cam_intri_json["D"]
    K = np.array(K).reshape(3, 3)
    D = np.array(D).reshape(-1, 1)
    intrinsic = np.matrix(
        [
            [K[0, 0], 0.00000000e00, K[0, 2]],  # geely1
            [0.00000000e00, K[1, 1], K[1, 2]],
            [0.00000000e00, 0.00000000e00, 1.00000000e00],
        ]
    )
    distort = np.matrix(
        [D[0][0], D[1][0], D[2][0], D[3][0], D[4][0], D[5][0], D[6][0], D[7][0]]
    )
    check_cam_ego(distortion_model, intrinsic, distort)


if __name__ == "__main__":
    main()
