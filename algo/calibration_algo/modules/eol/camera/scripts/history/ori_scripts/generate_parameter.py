import json
import pdb
import yaml
import numpy as np
from scipy.spatial.transform import Rotation as R

t0_structure = np.array([-1.007803, 0.032265, 0.915535])
t1_structure = np.array([2.519305, 0.952585, 0.788477])
t2_structure = np.array([2.519305, -0.952585, 0.788477])
t3_structure = np.array([1.944972, 0.000009, 1.3073238])
t4_structure = np.array([2.219093, 0.905678, 0.977109])
t5_structure = np.array([2.220086, -0.905641, 0.974649])


def pq2trans(p_x, p_y, p_z, q_w, q_x, q_y, q_z):
    Rm = R.from_quat([q_x, q_y, q_z, q_w]).as_matrix()
    trans = np.eye(4)
    trans[:3, :3] = Rm
    trans[0, 3] = p_x
    trans[1, 3] = p_y
    trans[2, 3] = p_z

    return trans


def cam_tf_rfu_to_ego_tf_cam(T):
    # input cam_tf_rfu_T
    # output ego_tf_CAM_t
    t = T[:3, 3]
    t = T[:3, :3].T @ -t
    ego_tf_rfu = np.array([[0, 1, 0], [-1, 0, 0], [0, 0, 1]])
    t = ego_tf_rfu @ t
    return t


def give_ex_result(T, extrinsic_dict, cam_name, reproj_err):
    extrinsic_dict["transform"]["translation"]["x"] = T[0, 3]
    extrinsic_dict["transform"]["translation"]["y"] = T[1, 3]
    extrinsic_dict["transform"]["translation"]["z"] = T[2, 3]
    q = R.from_matrix(T[:3, :3]).as_quat()
    extrinsic_dict["transform"]["rotation"]["w"] = q[3]
    extrinsic_dict["transform"]["rotation"]["x"] = q[0]
    extrinsic_dict["transform"]["rotation"]["y"] = q[1]
    extrinsic_dict["transform"]["rotation"]["z"] = q[2]
    rpy = R.from_matrix(T[:3, :3]).as_euler("xyz", degrees=True)
    extrinsic_dict["euler_degree"]["RotX"] = rpy[0]
    extrinsic_dict["euler_degree"]["RotY"] = rpy[1]
    extrinsic_dict["euler_degree"]["RotZ"] = rpy[2]

    extrinsic_dict["information"] = cam_name + "_tf_rfu"
    extrinsic_dict["reprojection_error"] = reproj_err

    return extrinsic_dict


def give_in_result(intri, name, intrinsic_dict, diff_cy):
    intrinsic_dict["K"][0][0] = intri[name]["focal_len_x"]
    intrinsic_dict["K"][1][1] = intri[name]["focal_len_y"]
    intrinsic_dict["K"][0][2] = intri[name]["optical_center_x"]
    intrinsic_dict["K"][1][2] = intri[name]["optical_center_y"] - diff_cy
    intrinsic_dict["D"][0][0] = intri[name]["k_1"]
    intrinsic_dict["D"][1][0] = intri[name]["k_2"]
    intrinsic_dict["D"][2][0] = intri[name]["p_1"]
    intrinsic_dict["D"][3][0] = intri[name]["p_2"]
    intrinsic_dict["D"][4][0] = intri[name]["k_3"]
    intrinsic_dict["D"][5][0] = intri[name]["k_4"]
    intrinsic_dict["D"][6][0] = intri[name]["k_5"]
    intrinsic_dict["D"][7][0] = intri[name]["k_6"]

    return intrinsic_dict


## input parameters (cam_tf_rfu)
## change to ego_tf_cam
# car_228
# intrinsic_path = "/home/<USER>/下载/EOL_data/20231110_228_biaodingjian1/soca_cam_intrisics.yaml"
# # cam_back_100
# T0 = pq2trans(-0.013968, 0.934142, -0.992236, -0.00012052, 0.00659721, 0.714456, -0.69965)
# # cam_back_left_100
# T1 = pq2trans(-2.58312, 0.778312, 0.768953, 0.315636, 0.307585, 0.631057, -0.638383)
# # cam_back_right_100
# T2 = pq2trans(2.58287, 0.770985, 0.761444, 0.323059, 0.298484, -0.625555, 0.644377)
# # cam_front_120
# T3 = pq2trans(0.0102733, 1.28756, -1.96069, 0.710646, 0.703542, -0.00262797, -0.00199944)
# # cam_front_left_100
# T4 = pq2trans(-1.12517, 0.971192, -2.12217, 0.639986, 0.637477, 0.295329, -0.311163)
# # cam_front_right_100
# T5 = pq2trans(1.1377, 0.959892, -2.10594, 0.642977, 0.638623, -0.307598, 0.290042)

# # car_705
# intrinsic_path = "/home/<USER>/下载/EOL_data/20231110_705_biaodingjian1/soca_cam_intrisics.yaml"
# # cam_back_100
# T0 = pq2trans(-0.017921, 0.92426, -1.00117, -0.00358379, -0.00524028, -0.710928, 0.703236)
# # cam_back_left_100
# T1 = pq2trans(-2.57577, 0.647448, 0.895977, 0.315191, 0.287281, 0.650276, -0.628701)
# # cam_back_right_100
# T2 = pq2trans(2.50558, 0.88333, 0.879653, 0.284777, 0.308647, -0.63652, 0.646901)
# # cam_front_120
# T3 = pq2trans(0.0380667, 1.28555, -1.96078, 0.710936, 0.703195, -0.00717915, 0.0059273)
# # cam_front_left_100
# T4 = pq2trans(-1.12003, 0.994927, -2.10628, 0.638544, 0.644306, 0.297572, -0.297628)
# # cam_front_right_100
# T5 = pq2trans(1.16924, 0.935477, -2.1149, 0.644154, 0.632427, -0.310622, 0.297683)

# car_703
# intrinsic_path = "/home/<USER>/下载/EOL_data/20231110_703_biaodingjian1/soca_cam_intrisics.yaml-703"
# # cam_back_100
# T0 = pq2trans(0.0291725, 0.925697, -1.00629, 0.0157196, 0.0216023, 0.710896, -0.70279)
# # cam_back_left_100
# T1 = pq2trans(-2.57622, 0.646848, 0.878738, 0.316748, 0.283685, 0.649125, -0.630738)
# # cam_back_right_100
# T2 = pq2trans(2.56747, 0.654987, 0.891152, 0.314034, 0.279146, -0.649774, 0.633446)
# # cam_front_120
# T3 = pq2trans(0.0305623, 1.28869, -1.95537, 0.710437, 0.703727, -0.00685161, -0.000873175)
# # cam_front_left_100
# T4 = pq2trans(-1.11452, 0.988611, -2.10576, 0.638263, 0.640882, 0.29435, -0.308625)
# # cam_front_right_100
# T5 = pq2trans(1.14822, 0.925777, -2.11742, 0.647012, 0.632852, -0.30714, 0.294175)

# # car_695
# intrinsic_path = "/home/<USER>/下载/EOL_data/1120_695_6v/tupian/soca_cam_intrisics.yaml"
# # cam_back_100
# T0 = pq2trans(0.00227775, 0.985456, -0.945121, 0.0131552, -0.0127421, -0.73425, 0.678632)
# # cam_back_left_100
# T1 = pq2trans(-2.57736, 0.637279, 0.883072, 0.314039, 0.285454, 0.652606, -0.627696)
# # cam_back_right_100
# T2 = pq2trans(2.55879, 0.705477, 0.886011, 0.310554, 0.284519, -0.643339, 0.639312)
# # cam_front_120
# T3 = pq2trans(0.00872032, 1.29179, -1.95207, 0.709867, 0.704331, -0.00219817, -0.00161915)
# # cam_front_left_100
# T4 = pq2trans(-1.15098, 0.979395, -2.08923, 0.637269, 0.639624, 0.307135, -0.300727)
# # cam_front_right_100
# T5 = pq2trans(1.14391, 0.935432, -2.11034, 0.645224, 0.635732, -0.308208, 0.290755)

# # car_692
# intrinsic_path = "/home/<USER>/下载/12.2/soca_cam_intrisics.yaml"
# # cam_back_100
# T0 = pq2trans(-0.0332596, 0.956214, -0.972725, 0.00498084, 0.000382267, -0.723313, 0.690502)
# # cam_back_left_100
# T1 = pq2trans(-2.55402, 0.707901, 0.899393, 0.304579, 0.288498, 0.647853, -0.635836)
# # cam_back_right_100
# T2 = pq2trans(2.56253, 0.692935, 0.894054, 0.310318, 0.287128, -0.646988, 0.634561)
# # cam_front_120
# T3 = pq2trans(0.0125146, 1.27894, -1.96351, 0.712158, 0.702003, -0.00339532, -0.003496)
# # cam_front_left_100
# T4 = pq2trans(-1.14811, 0.949248, -2.10935, 0.642172, 0.634325, 0.302895, -0.305781)
# # cam_front_right_100
# T5 = pq2trans(1.13055, 0.927072, -2.1246, 0.647742, 0.634686, -0.302706, 0.293211)

# # car_246
# intrinsic_path = "/home/<USER>/246_calib/12.20-246/calibresult/20210505_23_17_25/soca_cam_intrisics.yaml"
# # cam_back_100
# T0 = pq2trans(-0.0150128, 0.963235, -0.965436, 0.00544031, -0.00635952, -0.726455, 0.687163)
# # cam_back_left_100
# T1 = pq2trans(-2.62073, 0.755526, 0.634442, 0.323622, 0.344212, 0.639698, -0.606279)
# # cam_back_right_100
# T2 = pq2trans(2.55353, 0.720576, 0.894732, 0.307864, 0.28777, -0.643361, 0.639137)
# # cam_front_120
# T3 = pq2trans(0.00681064, 1.29574, -1.95139, 0.709117, 0.705085, -0.00183501, -0.00196204)
# # cam_front_left_100
# T4 = pq2trans(-1.13533, 0.976794, -2.10165, 0.637851, 0.63862, 0.299676, -0.30904)
# # cam_front_right_100
# T5 = pq2trans(1.15245, 0.934875, -2.10618, 0.644744, 0.635547, -0.309305, 0.291059)

# # car_721
# intrinsic_path = "/home/<USER>/721_calib/12.18.721/路口/soca_cam_intrisics.yaml"
# # cam_back_100
# T0 = pq2trans(-0.0176198, 0.934007, -0.994592, 0.00774326, -0.00567444, -0.715886, 0.698151)
# # cam_back_left_100
# T1 = pq2trans(-2.54434, 0.706908, 0.930705, 0.294999, 0.298695, 0.657058, -0.626125)
# # cam_back_right_100
# T2 = pq2trans(2.56655, 0.785966, 0.79634, 0.309494, 0.311079, -0.636252, 0.634529)
# # cam_front_120
# T3 = pq2trans(0.0175942, 1.28092, -1.9602, 0.711776, 0.702397, -0.00366257, 0.000685966)
# # cam_front_left_100
# T4 = pq2trans(-1.12518, 0.986136, -2.10136, 0.63691, 0.641468, 0.298662, -0.306049)
# # cam_front_right_100
# T5 = pq2trans(1.11917, 0.957682, -2.11182, 0.643311, 0.641067, -0.30571, 0.285876)

# # car_209
# intrinsic_path = "/home/<USER>/2024.1.4.209标间1/2/soca_cam_intrisics.yaml"
# # cam_back_100
# T0 = pq2trans(0.0270305, 0.929704, -0.996506, 0.0227902, 0.0207138, 0.714434, -0.699025)
# # cam_back_left_100
# T1 = pq2trans(-2.57504, 0.657618, 0.883178, 0.3112, 0.288957, 0.651709, -0.628438)
# # cam_back_right_100
# T2 = pq2trans(2.51527, 0.865491, 0.872709, 0.290258, 0.310544, -0.635842, 0.644219)
# # cam_front_120
# T3 = pq2trans(0.0231533, 1.29634, -1.95094, 0.708966, 0.705222, -0.00527702, -0.00123632)
# # cam_front_left_100
# T4 = pq2trans(-1.13812, 0.995058, -2.09308, 0.635317, 0.642767, 0.303911, -0.301432)
# # cam_front_right_100
# T5 = pq2trans(1.14473, 0.931496, -2.11188, 0.646022, 0.635323, -0.308535, 0.289527)

# # car_698
# intrinsic_path = "/home/<USER>/20240104.698标间1/1/soca_cam_intrisics.yaml"
# # cam_back_100
# T0 = pq2trans(-0.0132291, 0.92484, -1.00087, 0.00647986, -0.00742443, -0.711724, 0.70239)
# # cam_back_left_100
# T1 = pq2trans(-2.58038, 0.659064, 0.874841, 0.312449, 0.289498, 0.651005, -0.628298)
# # cam_back_right_100
# T2 = pq2trans(2.5702, 0.712836, 0.859804, 0.316942, 0.282614, -0.636845, 0.64351)
# # cam_front_120
# T3 = pq2trans(0.0118819, 1.28876, -1.9585, 0.710408, 0.703777, -0.0031355, -0.00289093)
# # cam_front_left_100
# T4 = pq2trans(-1.14161, 0.978658, -2.10234, 0.637942, 0.63932, 0.302087, -0.305032)
# # cam_front_right_100
# T5 = pq2trans(1.13986, 0.923946, -2.12448, 0.647664, 0.633192, -0.302958, 0.296337)

# # car_179
# intrinsic_path = "/home/<USER>/2024-1-4-179标定间1/2/soca_cam_intrisics.yaml"
# # cam_back_100
# T0 = pq2trans(-0.00125428, 0.97664, -0.973532, 0.0341839, -0.0113051, -0.728483, 0.684117)
# # cam_back_left_100
# T1 = pq2trans(-2.49816, 0.828158, 0.906453, 0.285311, 0.304394, 0.643726, -0.641529)
# # cam_back_right_100
# T2 = pq2trans(2.55482, 0.741679, 0.823975, 0.314663, 0.293115, -0.637216, 0.639552)
# # cam_front_120
# T3 = pq2trans(0.000888831, 1.27383, -1.95378, 0.713135, 0.701027, -1.71184e-05, 0.000708926)
# # cam_front_left_100
# T4 = pq2trans(-1.12478, 0.953885, -2.09887, 0.641428, 0.636123, 0.300422, -0.306048)
# # cam_front_right_100
# T5 = pq2trans(1.12874, 0.906447, -2.11134, 0.649246, 0.629744, -0.299475, 0.303674)

# # car_184
# intrinsic_path = "/home/<USER>/2024.1.4.184.标定间1/2/soca_cam_intrisics.yaml"
# # cam_back_100
# T0 = pq2trans(0.0326661, 0.938043, -0.991535, 0.026855, -0.0248093, -0.717299, 0.695806)
# # cam_back_left_100
# T1 = pq2trans(-2.54741, 0.732179, 0.899206, 0.300464, 0.294738, 0.647682, -0.635106)
# # cam_back_right_100
# T2 = pq2trans(2.56495, 0.692534, 0.876085, 0.314009, 0.283912, -0.643297, 0.637935)
# # cam_front_120
# T3 = pq2trans(0.0150844, 1.28035, -1.96056, 0.711908, 0.702265, -0.0033195, -0.000314082)
# # cam_front_left_100
# T4 = pq2trans(-1.14016, 0.980695, -2.09923, 0.63777, 0.640356, 0.303917, -0.301378)
# # cam_front_right_100
# T5 = pq2trans(1.12895, 0.94981, -2.11046, 0.644589, 0.6396, -0.308234, 0.283564)

# # car_178
# intrinsic_path = "/home/<USER>/2024-1-4-178标定间1/2/soca_cam_intrisics.yaml"
# # cam_back_100
# T0 = pq2trans(0.029285, 0.969171, -0.960014, 0.00594022, -0.0226498, -0.728838, 0.684285)
# # cam_back_left_100
# T1 = pq2trans(-2.56408, 0.701293, 0.881911, 0.307804, 0.291558, 0.647308, -0.633438)
# # cam_back_right_100
# T2 = pq2trans(2.54576, 0.760704, 0.874385, 0.304529, 0.294437, -0.640855, 0.640213)
# # cam_front_120
# T3 = pq2trans(0.0103609, 1.27309, -1.96466, 0.713217, 0.70094, -0.00203118, 0.000912612)
# # cam_front_left_100
# T4 = pq2trans(-1.1503, 0.949358, -2.10486, 0.641662, 0.635071, 0.303876, -0.304326)
# # cam_front_right_100
# T5 = pq2trans(1.14699, 0.909363, -2.11852, 0.648701, 0.630504, -0.303644, 0.299084)

# # car_188
# intrinsic_path = "/home/<USER>/12.12-000188车采图标定/标定间2/soca_cam_intrisics.yaml"
# # cam_back_100
# T0 = pq2trans(0.0215851, 0.951998, -0.9941, 0.0198947, -0.0196183, -0.721136, 0.69223)
# # cam_back_left_100
# T1 = pq2trans(-2.53742, 0.725613, 0.878228, 0.302772, 0.295509, 0.647889, -0.633438)
# # cam_back_right_100
# T2 = pq2trans(2.55412, 0.725038, 0.828807, 0.314276, 0.290958, -0.639263, 0.638684)
# # cam_front_120
# T3 = pq2trans(0.0215389, 1.31202, -1.92609, 0.706174, 0.708023, -0.00460134, 0.000635987)
# # cam_front_left_100
# T4 = pq2trans(-1.13949, 0.977061, -2.07137, 0.636403, 0.638604, 0.302842, -0.308972)
# # cam_front_right_100
# T5 = pq2trans(1.12641, 0.933809, -2.09968, 0.645363, 0.636526, -0.307281, 0.289688)

# # car_244
# intrinsic_path = "/home/<USER>/下载/标定2000244(1)/标定间2/soca_cam_intrisics.yaml"
# # cam_back_100
# T0 = pq2trans(0.0499245, 0.927558, -0.999402, 0.0330981, -0.0314716, -0.711608, 0.70109)
# # cam_back_left_100
# T1 = pq2trans(-2.56275, 0.677092, 0.8994, 0.305597, 0.290887, 0.653806, -0.628118)
# # cam_back_right_100
# T2 = pq2trans(2.587, 0.700935, 0.816101, 0.323101, 0.289213, -0.635765, 0.638564)
# # cam_front_120
# T3 = pq2trans(0.00799058, 1.28289, -1.96173, 0.711492, 0.702691, -0.00187064, -0.000609397)
# # cam_front_left_100
# T4 = pq2trans(-1.15754, 0.950589, -2.10276, 0.641266, 0.633186, 0.302471, -0.310427)
# # cam_front_right_100
# T5 = pq2trans(1.12697, 0.93454, -2.12408, 0.646833, 0.635493, -0.300374, 0.295856)

# car_temp
intrinsic_path = "../data/soca_cam_intrisics.yaml"
# cam_back_100
T0 = pq2trans(
    -0.0165164, 0.945224, -0.984189, 0.00779562, -0.00555614, -0.720182, 0.693719
)
error0 = 1.48641
# cam_back_left_100
T1 = pq2trans(-2.5497, 0.678464, 0.935679, 0.29932, 0.290674, 0.656877, -0.628035)
error1 = 1.75741
# cam_back_right_100
T2 = pq2trans(2.56035, 0.677369, 0.910349, 0.30822, 0.285718, -0.650266, 0.632867)
error2 = 1.27555
# cam_front_120
T3 = pq2trans(
    0.00188607, 1.28245, -1.96143, 0.711508, 0.702678, -0.000372856, 0.000137214
)
error3 = 1.9641
# cam_front_left_100
T4 = pq2trans(-1.13661, 1.01694, -2.08252, 0.631412, 0.644231, 0.302232, -0.308126)
error4 = 1.07286
# cam_front_right_100
T5 = pq2trans(1.0984, 0.920623, -2.13279, 0.650787, 0.63671, -0.30296, 0.281588)
error5 = 1.04823

t0 = cam_tf_rfu_to_ego_tf_cam(T0)
t1 = cam_tf_rfu_to_ego_tf_cam(T1)
t2 = cam_tf_rfu_to_ego_tf_cam(T2)
t3 = cam_tf_rfu_to_ego_tf_cam(T3)
t4 = cam_tf_rfu_to_ego_tf_cam(T4)
t5 = cam_tf_rfu_to_ego_tf_cam(T5)

print((t0 - t0_structure) * 100)
print((t1 - t1_structure) * 100)
print((t2 - t2_structure) * 100)
print((t3 - t3_structure) * 100)
print((t4 - t4_structure) * 100)
print((t5 - t5_structure) * 100)

extrinsic_dict = {}
extrinsic_dict["calib_errorcode"] = 0
extrinsic_dict["calib_status"] = 0
extrinsic_dict["calib_time"] = "20XX-0X-XX_XX:XX:XX:XXX"
extrinsic_dict["calib_valid"] = 1
extrinsic_dict["euler_degree"] = {}
extrinsic_dict["euler_degree"]["RotX"] = 0
extrinsic_dict["euler_degree"]["RotY"] = 0
extrinsic_dict["euler_degree"]["RotZ"] = 0
extrinsic_dict["information"] = ""
extrinsic_dict["mode"] = 0
extrinsic_dict["reprojection_error"] = 0
extrinsic_dict["transform"] = {}
extrinsic_dict["transform"]["rotation"] = {}
extrinsic_dict["transform"]["rotation"]["w"] = 0
extrinsic_dict["transform"]["rotation"]["x"] = 0
extrinsic_dict["transform"]["rotation"]["y"] = 0
extrinsic_dict["transform"]["rotation"]["z"] = 0
extrinsic_dict["transform"]["translation"] = {}
extrinsic_dict["transform"]["translation"]["x"] = 0
extrinsic_dict["transform"]["translation"]["y"] = 0
extrinsic_dict["transform"]["translation"]["z"] = 0
extrinsic_dict["vin"] = "XXXXXX"


## cam_back_100
extrinsic_dict = give_ex_result(T0, extrinsic_dict, "cam_back_100", error0)
extrinsic_dict["information"] = "cam_back_100_tf_rfu"
with open("cam_back_100_extrinsic.json", "w") as f:
    json.dump(extrinsic_dict, f, indent=4)

## cam_back_left_100
extrinsic_dict = give_ex_result(T1, extrinsic_dict, "cam_back_left_100", error1)
extrinsic_dict["information"] = "cam_back_left_100_tf_rfu"
with open("cam_back_left_100_extrinsic.json", "w") as f:
    json.dump(extrinsic_dict, f, indent=4)

## cam_back_right_100
extrinsic_dict = give_ex_result(T2, extrinsic_dict, "cam_back_right_100", error2)
extrinsic_dict["information"] = "cam_back_right_100_tf_rfu"
with open("cam_back_right_100_extrinsic.json", "w") as f:
    json.dump(extrinsic_dict, f, indent=4)

## cam_front_120
extrinsic_dict = give_ex_result(T3, extrinsic_dict, "cam_front_120", error3)
extrinsic_dict["information"] = "cam_front_120_tf_rfu"
with open("cam_front_120_extrinsic.json", "w") as f:
    json.dump(extrinsic_dict, f, indent=4)

## cam_front_left_100
extrinsic_dict = give_ex_result(T4, extrinsic_dict, "cam_front_left_100", error4)
extrinsic_dict["information"] = "cam_front_left_100_tf_rfu"
with open("cam_front_left_100_extrinsic.json", "w") as f:
    json.dump(extrinsic_dict, f, indent=4)

## cam_front_right_100
extrinsic_dict = give_ex_result(T5, extrinsic_dict, "cam_front_right_100", error5)
extrinsic_dict["information"] = "cam_front_right_100_tf_rfu"
with open("cam_front_right_100_extrinsic.json", "w") as f:
    json.dump(extrinsic_dict, f, indent=4)


intrinsic_dict = {}
intrinsic_dict["resolution"] = [0, 0]
intrinsic_dict["distortion_model"] = "pinhole"
intrinsic_dict["K"] = np.eye(3).tolist()
intrinsic_dict["D"] = np.ones(8).reshape(8, 1).tolist()


# intrinsic
with open(intrinsic_path, "r") as f:
    intri = yaml.safe_load(f)

# cam_back_100
intrinsic_dict = give_in_result(intri, "cam_back", intrinsic_dict, 98)
intrinsic_dict["resolution"] = [1920, 1080]
with open("cam_back_100_intrinsic.json", "w") as f:
    json.dump(intrinsic_dict, f, indent=4)

# cam_back_left_100
intrinsic_dict = give_in_result(intri, "cam_side_left_back", intrinsic_dict, 98)
intrinsic_dict["resolution"] = [1920, 1080]
with open("cam_back_left_100_intrinsic.json", "w") as f:
    json.dump(intrinsic_dict, f, indent=4)

# cam_back_right_100
intrinsic_dict = give_in_result(intri, "cam_side_right_back", intrinsic_dict, 98)
intrinsic_dict["resolution"] = [1920, 1080]
with open("cam_back_right_100_intrinsic.json", "w") as f:
    json.dump(intrinsic_dict, f, indent=4)

# cam_front_120
intrinsic_dict = give_in_result(intri, "cam_front", intrinsic_dict, 0)
intrinsic_dict["resolution"] = [3840, 2160]
with open("cam_front_120_intrinsic.json", "w") as f:
    json.dump(intrinsic_dict, f, indent=4)

# cam_front_left_100
intrinsic_dict = give_in_result(intri, "cam_side_left_front", intrinsic_dict, 98)
intrinsic_dict["resolution"] = [1920, 1080]
with open("cam_front_left_100_intrinsic.json", "w") as f:
    json.dump(intrinsic_dict, f, indent=4)

# cam_front_right_100
intrinsic_dict = give_in_result(intri, "cam_side_right_front", intrinsic_dict, 98)
intrinsic_dict["resolution"] = [1920, 1080]
with open("cam_front_right_100_intrinsic.json", "w") as f:
    json.dump(intrinsic_dict, f, indent=4)
