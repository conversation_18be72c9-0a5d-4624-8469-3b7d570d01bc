import os, sys
import numpy as np
import cv2
import matplotlib.pyplot as plt
from tqdm import tqdm
import yaml
import concurrent.futures
from sklearn.ensemble import IsolationForest
import subprocess
import shutil
import threading
import json


def is_valid_frame_enhanced(
    image_path,
    std_threshold=5.0,
    mean_range=(5, 250),
    edge_threshold=50,  # Minimum number of edges for Canny edge detection
    histogram_threshold=0.8,  # Histogram concentration threshold
    block_check=True,
):  # Whether to enable block checks
    """
    Enhanced image validity check function to filter more types of invalid frames.
    """
    # Read the image
    img = cv2.imread(image_path)
    if img is None:
        return False

    # Convert to grayscale
    if len(img.shape) == 3:
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    else:
        gray = img

    # 1. Basic statistical checks
    std_dev = np.std(gray)
    mean_val = np.mean(gray)

    if std_dev < std_threshold or mean_val < mean_range[0] or mean_val > mean_range[1]:
        return False

    # 2. Edge detection - Check if the image contains enough edge features
    edges = cv2.Canny(gray, 50, 150)
    if np.count_nonzero(edges) < edge_threshold:
        return False

    # 3. Histogram analysis - Check if pixel value distribution is overly concentrated
    hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
    hist_norm = hist / hist.sum()
    max_bin_value = hist_norm.max()
    if max_bin_value > histogram_threshold:
        return False

    # 4. Block region checks - Divide the image into 3x3 blocks and check the variance of each block
    if block_check:
        h, w = gray.shape
        h_block, w_block = h // 3, w // 3
        block_std_threshold = (
            std_threshold / 2
        )  # Lower standard deviation threshold for blocks

        flat_blocks = 0
        for i in range(3):
            for j in range(3):
                block = gray[
                    i * h_block : (i + 1) * h_block, j * w_block : (j + 1) * w_block
                ]
                if np.std(block) < block_std_threshold:
                    flat_blocks += 1

        # If more than 5 blocks are flat, consider the image not rich enough
        if flat_blocks > 5:
            return False

    return True


def test(
    all_img_dir_lst,
    input_img_dir,
    executable_path,
    eol_config_yaml_path,
    simulation_config,
    output_dir="./output",
    max_threads=8,
):
    if os.path.exists(output_dir):
        os.system("rm -rf {}".format(output_dir))
    os.makedirs(output_dir, exist_ok=True)

    # if os.path.exists(input_img_dir):
    #     os.system("rm -rf {}".format(input_img_dir))
    os.makedirs(input_img_dir, exist_ok=True)

    if not os.path.exists(executable_path):
        assert False, "Executable path does not exist: {}".format(executable_path)

    if not os.path.exists(eol_config_yaml_path):
        assert False, "EOL config path does not exist: {}".format(eol_config_yaml_path)

    output_yaml_dir = os.path.join(output_dir, "calib_result")
    if os.path.exists(output_yaml_dir):
        os.system("rm -rf {}".format(output_yaml_dir))
    os.makedirs(output_yaml_dir, exist_ok=True)

    mach_camera_name_lst = []
    tmp_config_yaml_path = os.path.join(output_dir, "tmp_eol_config.yaml")
    with open(eol_config_yaml_path, "r") as file:
        eol_config = yaml.safe_load(file)
        mach_camera_name_lst = [
            v for v in eol_config["geely2mach_camera_mappings"].values()
        ]

        eol_config["is_debug"] = False
        eol_config["is_print_eol_config"] = False

        eol_config["is_simulation_error"] = True
        for key in simulation_config.keys():
            if key in eol_config["simulation_error_config"].keys():
                eol_config["simulation_error_config"][key] = simulation_config[key]
            else:
                print(f"Key {key} not found in simulation_error_config.")
                exit(1)

        with open(tmp_config_yaml_path, "w") as f:
            yaml.dump(eol_config, f, sort_keys=False)

    cam_name2img_path_lst = []

    # Use multithreading to process all image directories
    def process_directory(img_dir):
        print("Processing image directory: {}".format(img_dir))
        start_idx, stop_idx = 0, -1
        img_name2img_path = {}
        for cam_name in mach_camera_name_lst:
            tmp_img_dir = os.path.join(img_dir, cam_name)
            tmp_img_path_lst = [
                os.path.join(tmp_img_dir, img_path)
                for img_path in os.listdir(tmp_img_dir)
            ]
            tmp_img_path_lst = sorted(
                tmp_img_path_lst, key=lambda x: int(x.split("/")[-1].split(".")[0])
            )
            img_name2img_path[cam_name] = tmp_img_path_lst

            for idx, img_path in enumerate(tmp_img_path_lst):
                if not is_valid_frame_enhanced(img_path):
                    start_idx = max(start_idx, idx + 1)
                if idx >= 50:
                    break

        stop_idx = min(
            [len(img_path_lst) for img_path_lst in img_name2img_path.values()]
        )
        img_pairs = []
        for idx in range(start_idx, stop_idx):
            img_pair = {
                cam_name: img_name2img_path[cam_name][idx]
                for cam_name in mach_camera_name_lst
            }
            img_pairs.append(img_pair)
        return img_pairs

    cam_name2img_path_lst = []

    # Use ThreadPoolExecutor for multithreading
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_threads * 2) as executor:
        futures = [
            executor.submit(process_directory, img_dir) for img_dir in all_img_dir_lst
        ]
        for future in tqdm(
            concurrent.futures.as_completed(futures),
            total=len(futures),
            position=0,
            colour="green",
            desc="Processing Directories: ",
        ):
            cam_name2img_path_lst.extend(future.result())

    input_img_dir_lst = []

    # Function to copy images for a single pair
    def copy_images(i, img_pair):
        target_dir = os.path.join(input_img_dir, str(i))
        os.makedirs(target_dir, exist_ok=True)
        for cam_name, img_path in img_pair.items():
            shutil.copy2(img_path, os.path.join(target_dir, cam_name + ".jpg"))
        return target_dir

    # Use ThreadPoolExecutor for multithreading
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_threads * 5) as executor:
        futures = {
            executor.submit(copy_images, i, img_pair): i
            for i, img_pair in enumerate(cam_name2img_path_lst)
        }
        for future in tqdm(
            concurrent.futures.as_completed(futures),
            total=len(futures),
            position=0,
            colour="green",
            desc="Copy Images: ",
        ):
            input_img_dir_lst.append(future.result())

    error_data = []

    # # # test
    # for i, input_img_dir in enumerate(tqdm(input_img_dir_lst, position=0, colour="green", desc="Test: ")):
    #     output_yaml_path = os.path.join(output_dir.replace(os.path.dirname(executable_path) + os.sep, ""), "calib_result", str(i) + ".yaml")
    #     # Run the executable with the updated config
    #     cmd = "cd {} && {} {} {} {}".format(os.path.dirname(executable_path),
    #                                         "./" + os.path.basename(executable_path),
    #                                         tmp_config_yaml_path.replace(os.path.dirname(executable_path) + "/", ""),
    #                                         input_img_dir.replace(os.path.dirname(executable_path) + "/", ""),
    #                                         output_yaml_path)
    #     # print(cmd)
    #     try:
    #         result = subprocess.run(cmd, shell=True, text=True, capture_output=True, check=True)
    #         # print("Command Output:", result.stdout)
    #         # print("Command Error (if any):", result.stderr)

    #         if "Successfully" in result.stdout:
    #             continue
    #     except subprocess.CalledProcessError as e:
    #         print("Command failed with return code:", e.returncode)
    #         print("Command Output:", e.stdout)
    #         print("Command Error:", e.stderr)

    #     error_data.append(input_img_dir + "\n")

    #     # if i == 10:
    #     #     break

    # test
    # Use multithreading to process the images
    def process_image(input_img_dir):
        output_yaml_path = os.path.join(
            output_yaml_dir.replace(os.path.dirname(executable_path) + os.sep, ""),
            os.path.basename(input_img_dir) + ".yaml",
        )
        # Run the executable with the updated config
        cmd = "cd {} && {} {} {} {}".format(
            os.path.dirname(executable_path),
            "./" + os.path.basename(executable_path),
            tmp_config_yaml_path.replace(os.path.dirname(executable_path) + "/", ""),
            input_img_dir.replace(os.path.dirname(executable_path) + "/", ""),
            output_yaml_path,
        )
        try:
            result = subprocess.run(
                cmd, shell=True, text=True, capture_output=True, check=True
            )
            if "Successfully" not in result.stdout:
                return input_img_dir + "\n"
        except subprocess.CalledProcessError as e:
            print("Command failed with return code:", e.returncode)
            print("Command Output:", e.stdout)
            print("Command Error:", e.stderr)
            return input_img_dir + "\n"
        return None

    # Use ThreadPoolExecutor for multithreading with a controlled number of threads

    with concurrent.futures.ThreadPoolExecutor(max_workers=max_threads) as executor:
        futures = [
            executor.submit(process_image, input_img_dir)
            for input_img_dir in input_img_dir_lst
        ]
        for future in tqdm(
            concurrent.futures.as_completed(futures),
            total=len(futures),
            position=0,
            colour="green",
            desc="Processing Images: ",
        ):
            error = future.result()
            if error:
                error_data.append(os.path.abspath(error))

    with open(os.path.join(output_dir, "error_data.txt"), "w") as f:
        f.writelines(error_data)

    statistics_error_data(
        os.path.join(output_dir, "error_data.txt"),
        output_yaml_dir,
        tmp_config_yaml_path,
    )


def statistics_error_data(error_data_path, output_yaml_dir, eol_config_path):
    error_data_lst = []
    with open(error_data_path, "r") as f:
        error_data_lst = [x.strip() for x in f.readlines()]

    eol_config = {}
    with open(eol_config_path, "r") as f:
        eol_config = yaml.safe_load(f)

    error_data_count = {}

    for i, error_img_dir in enumerate(
        tqdm(error_data_lst, position=0, colour="green", desc="Statistics: ")
    ):
        error_img_dir_name = os.path.basename(error_img_dir)
        output_yaml_path = os.path.join(output_yaml_dir, error_img_dir_name + ".yaml")

        with open(output_yaml_path, "r") as f:
            lines = f.readlines()
            if lines[0].startswith("%YAML"):
                data = yaml.safe_load("".join(lines[2:]))
            else:
                data = yaml.safe_load("".join(lines))

        for key, value in data.items():
            if "cam" not in key:
                continue
            key = eol_config["geely2mach_camera_mappings"][key]

            if i == 0:
                error_data_count[key] = {}

            if value["calib_errcode"] not in error_data_count[key]:
                error_data_count[key][value["calib_errcode"]] = []
                error_data_count[key][str(value["calib_errcode"]) + "_img"] = []

            if value["calib_errcode"] == 10:
                reproj_error = value["custom_fields"]["camera_calib_reproj_error"]
                error_data_count[key][value["calib_errcode"]].append(reproj_error)
            else:
                error_data_count[key][value["calib_errcode"]].append(1)

            if value["calib_errcode"] == 0:
                continue

            error_data_count[key][str(value["calib_errcode"]) + "_img"].append(
                os.path.join(error_img_dir, key + ".jpg")
            )

    if error_data_count == {}:
        print("Calibration results are all correct!")
    else:
        for key, value in error_data_count.items():
            print_data = "{}:\n".format(key)
            for key2, value2 in value.items():
                if "_img" in str(key2):
                    continue

                print_data += "  {}: ".format(key2)
                if key2 == 10:
                    print_data += "{} {} {}\n".format(
                        len(value2), float(np.mean(value2)), float(np.max(value2))
                    )
                    error_data_count[key][key2] = [
                        len(value2),
                        float(np.mean(value2)),
                        float(np.max(value2)),
                    ]
                else:
                    print_data += "{}\n".format(len(value2))
                    error_data_count[key][key2] = len(value2)
            print(print_data)

    output_statistics_path = os.path.join(
        os.path.dirname(error_data_path), "error_data_statistics.yaml"
    )
    print("output_statistics_path: ", output_statistics_path)
    with open(output_statistics_path, "w") as f:
        yaml.dump(error_data_count, f, allow_unicode=True, indent=4)

    output_statistics_path = os.path.join(
        os.path.dirname(error_data_path), "error_data_statistics.json"
    )
    with open(output_statistics_path, "w") as f:
        json.dump(error_data_count, f, indent=4)


if __name__ == "__main__":

    all_img_dir_lst = []
    executable_path = "./build/batch_test_python"
    exec_input_img_dir = "./build/output_batch_test/test_input_img"

    # 2025.04.17: p177 data random mask AprilTag ID = 0 || 4 for cam_front_120
    # eol_config_yaml_path = "./build/output/simulation_error_eol_config/eol_config_simulation_error_1.yaml"
    # output_dir = "./build/output_batch_test/error_simulation_random_mask_cam_front_120"
    # all_img_dir = "/data/data/P177/2025.04.17_calibration_room/20250416P1005"
    # random_mask = {
    #     "random_mask_AprilTag": {
    #         "cam_front_120": [0, 4],
    #         "cam_front_30": [],
    #         "cam_front_left_100": [],
    #         "cam_front_right_100": [],
    #         "cam_back_left_100": [],
    #         "cam_back_right_100": [],
    #         "cam_back_100": [],
    #     },
    #     "f_offset": [0.0, 0.0],
    #     # "img_detect_points_offset": [1.0, 1.0],
    #     # "calibroom_points_offset": [0.002, 0.002, 0.001],
    #     "output_simulation_error_name": "random_mask_simulation_error.yaml",
    # }
    # simulation_config = random_mask

    # 2025.04.17: p177 data actual_scene_simulation
    # eol_config_yaml_path = (
    #     "./config/P177/eol_config.yaml"
    # )
    # output_dir = (
    #     "./build/output_batch_test/error_simulation_actual_scene"
    # )
    # all_img_dir = "/data/data/P177/2025.04.17_calibration_room/20250416P1005"
    # actual_scene_simulation_config = {
    #         "f_offset": [0.02, 0.02],
    #         "img_detect_points_offset": [1.0, 1.0],
    #         "calibroom_points_offset": [0.01, 0.01, 0.02],
    #         "output_simulation_error_name": "simulation_error_actual_scene.yaml",
    # }
    # simulation_config = actual_scene_simulation_config

    # 2025.04.17: p177 data actual_scene_simulation
    eol_config_yaml_path = "./config/P177/eol_config.yaml"
    output_dir = "./build/output_batch_test/error_simulation_random_mask_1"
    all_img_dir = "/data/data/P177/2025.04.17_calibration_room/20250416P1005"
    random_mask_simulation_config = {
        "random_mask_AprilTag": {
            "cam_back_100": [16, 17, 18, 19],
            "cam_back_left_100": [9, 11, 13, 15, 17, 19],
            "cam_back_right_100": [8, 10, 12, 14, 16, 18],
            "cam_front_120": [0, 1, 2, 3, 4, 5, 6, 7],
            "cam_front_30": [0, 1, 2, 3],
            "cam_front_left_100": [2, 3, 4, 6, 9, 11],
            "cam_front_right_100": [0, 1, 5, 7, 8, 10],
        },
        "output_simulation_error_name": "simulation_error_random_mask.yaml",
    }
    simulation_config = random_mask_simulation_config

    max_threads = 24

    for root, dirs, files in os.walk(all_img_dir):
        if root.count(os.sep) - all_img_dir.count(os.sep) < 2:
            for dir_name in dirs:
                if "parse" in dir_name:
                    all_img_dir_lst.append(os.path.join(root, dir_name))
        else:
            del dirs[:]  # Prevent further traversal into deeper directories

    all_img_dir_lst = [os.path.join(x, "camera") for x in all_img_dir_lst]

    if len(all_img_dir_lst) == 0:
        print("No image found!")
        exit(0)

    test(
        all_img_dir_lst,
        exec_input_img_dir,
        executable_path,
        eol_config_yaml_path,
        simulation_config,
        output_dir,
        max_threads,
    )


# statistics_error_data("/data/production_calibraton/build/output_batch_test/multi-threaded_test/error_data.txt",
#                       "/data/production_calibraton/build/output_batch_test/multi-threaded_test/calib_result",
#                       "/data/production_calibraton/build/output_batch_test/multi-threaded_test/tmp_eol_config.yaml")
