import os, sys
import numpy as np
import json
import yaml
import time, datetime
from scipy.spatial.transform import Rotation
import argparse


p177_geely2mach_camera_mappings = {
    "cam_back_100": "cam_back_100",
    "cam_front_120": "cam_front_120",
    "cam_front_30": "cam_front_30",
    "cam_side_left_back": "cam_back_left_100",
    "cam_side_left_front": "cam_front_left_100",
    "cam_side_right_back": "cam_back_right_100",
    "cam_side_right_front": "cam_front_right_100",
}


p177_camera_resolution_mapping = {
    "cam_back_100": [1920, 1280],
    "cam_back_left_100": [1920, 1280],
    "cam_back_right_100": [1920, 1280],
    "cam_front_120": [3840, 2160],
    "cam_front_30": [3840, 2160],
    "cam_front_left_100": [1920, 1280],
    "cam_front_right_100": [1920, 1280],
}


def camera_calibresult2eol(cam_calibresult_dir, cam_eol_dir, cam_eol_mapping):
    """camera calibresult to eol

    Args:
        cam_calibresult_dir (str): calibresult directory, see https://git-core.megvii-inc.com/transformer/calibresult
        cam_eol_dir (str): _path to the camera eol directory, which contains the extrinsics and intrinsics of the camera
    """
    if not os.path.exists(cam_calibresult_dir):
        raise ValueError(f"calibresult directory {cam_calibresult_dir} does not exist!")

    if os.path.exists(cam_eol_dir):
        print(
            f"camera eol directory {cam_eol_dir} already exists, default to overwrite!"
        )
        # os.system(f'rm -rf {cam_eol_dir}')
    os.makedirs(cam_eol_dir, exist_ok=True)

    eol_extrinsics = {
        geely_cam_name: {} for geely_cam_name in p177_geely2mach_camera_mappings.keys()
    }
    eol_intrinsics = {
        "setVersion": 1,
        "VIN": "XXXXX",
    }
    eol_intrinsics.update(
        {
            geely_cam_name: {}
            for geely_cam_name in p177_geely2mach_camera_mappings.keys()
        }
    )

    for geely_cam_name, mach_cam_name in cam_eol_mapping.items():
        extrinsic_path = os.path.join(
            cam_calibresult_dir, mach_cam_name + "_extrinsic.json"
        )
        intrinsic_path = os.path.join(
            cam_calibresult_dir, mach_cam_name + "_intrinsic.json"
        )
        if not os.path.exists(extrinsic_path) or not os.path.exists(intrinsic_path):
            raise ValueError(
                f"extrinsic or intrinsic file does not exist for camera {mach_cam_name}"
            )

        with open(extrinsic_path, "r") as f:
            extrinsic = json.load(f)
        with open(intrinsic_path, "r") as f:
            intrinsic = json.load(f)

        q = [
            extrinsic["transform"]["rotation"]["w"],
            extrinsic["transform"]["rotation"]["x"],
            extrinsic["transform"]["rotation"]["y"],
            extrinsic["transform"]["rotation"]["z"],
        ]
        # Convert quaternion to Euler angles (ZYX rotation order)
        euler_angles = (
            Rotation.from_quat([q[1], q[2], q[3], q[0]])
            .as_euler("ZYX", degrees=True)
            .tolist()
        )

        eol_extrinsics[geely_cam_name] = {
            "translation": extrinsic["transform"]["translation"],
            "rotation": extrinsic["transform"]["rotation"],
            "euler_degree": {
                "RotX": euler_angles[0],  # ZYX order
                "RotY": euler_angles[1],
                "RotZ": euler_angles[2],
            },
            "calib_status": 0,
            "calib_errcode": 0,
            "calib_valid": 1,
            # "calib_mode": 0,
            "calib_time": extrinsic.get(
                "calib_time", time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            ),
            "information": f"{mach_cam_name}_tf_rfu",
            "custom_fields": {
                "camera_calib_reproj_error": extrinsic.get("reproj_error", 0)
            },
        }

        fK, fD = intrinsic["K"], intrinsic["D"]
        distortion_model = intrinsic["distortion_model"].upper()

        if distortion_model == "PINHOLE":
            eol_intrinsics[geely_cam_name] = {
                "focal_len_x": fK[0][0],
                "focal_len_y": fK[1][1],
                "optical_center_x": fK[0][2],
                "optical_center_y": fK[1][2],
                "focal_dist": 0.000000,
                "k_1": fD[0],
                "k_2": fD[1],
                "p_1": fD[2],
                "p_2": fD[3],
                "k_3": fD[4],
                "k_4": fD[5] if len(fD) > 5 else 0.0,
                "k_5": fD[6] if len(fD) > 6 else 0.0,
                "k_6": fD[7] if len(fD) > 7 else 0.0,
                "repro_err": 0.0,
                "distortion_type": 3,
                "crc": 0.0,
                "parameter_vaild": 1,
                "sn": "XXXXXXX",
                "calib_data": extrinsic.get(
                    "calib_time", time.strftime("%Y-%m-%d", time.localtime())
                ),
            }
        elif distortion_model == "FISHEYE":
            eol_intrinsics[geely_cam_name] = {
                "focal_len_x": fK[0][0],
                "focal_len_y": fK[1][1],
                "optical_center_x": fK[0][2],
                "optical_center_y": fK[1][2],
                "focal_dist": 0.000000,
                "k_1": fD[0][0],
                "k_2": fD[1][0],
                "k_3": fD[2][0],
                "k_4": fD[3][0],
                "k_5": fD[4][0] if len(fD) > 4 else 0.0,
                "k_6": fD[5][0] if len(fD) > 5 else 0.0,
                "p_1": 0.0,
                "p_2": 0.0,
                "repro_err": 0.0,
                "distortion_type": 1,
                "crc": 0.0,
                "parameter_vaild": 1,
                "sn": "XXXXXXX",
                "calib_data": extrinsic.get(
                    "calib_time", time.strftime("%Y-%m-%d", time.localtime())
                ),
            }
        else:
            raise ValueError(f"unsupported distortion model {distortion_model}")

    eol_extrinsic_path = os.path.join(cam_eol_dir, "driving_camera_calibresult.yaml")
    with open(eol_extrinsic_path, "w") as f:
        yaml.dump(eol_extrinsics, f, indent=4, sort_keys=False)

    eol_intrinsic_path = os.path.join(cam_eol_dir, "soca_cam_intrinsics.yaml")
    with open(eol_intrinsic_path, "w") as f:
        yaml.dump(eol_intrinsics, f, indent=4, sort_keys=False)

    print(
        "\n================================ Success ================================\n"
    )
    print(f"camera eol extrinsics saved to {os.path.abspath(eol_extrinsic_path)}")
    print(f"camera eol intrinsics saved to {os.path.abspath(eol_intrinsic_path)}")
    print(
        "\n=========================================================================\n"
    )


def camera_eol2calibresult(
    cam_eol_dir, cam_calibresult_dir, cam_eol_mapping, cam_resolution_mapping
):
    """camera eol to calibresult

    Args:
        cam_eol_dir (str): _path to the camera eol directory, which contains the extrinsics and intrinsics of the camera
        cam_calibresult_dir (str): calibresult directory, see https://git-core.megvii-inc.com/transformer/calibresult
    """
    cam_eol_extrinsics_path = os.path.join(
        cam_eol_dir, "driving_camera_calibresult.yaml"
    )
    cam_eol_intrinsics_path = os.path.join(cam_eol_dir, "soca_cam_intrinsics.yaml")

    if not os.path.exists(cam_eol_extrinsics_path):
        raise FileNotFoundError(
            f"camera eol extrinsics file not found at {cam_eol_extrinsics_path}"
        )
    if not os.path.exists(cam_eol_intrinsics_path):
        raise FileNotFoundError(
            f"camera eol intrinsics file not found at {cam_eol_intrinsics_path}"
        )

    if os.path.basename(cam_calibresult_dir) != "camera_params":
        cam_calibresult_dir = os.path.join(cam_calibresult_dir, "camera_params")

    if os.path.exists(cam_calibresult_dir):
        print(
            f"calibresult directory {cam_calibresult_dir} already exists, default to overwrite!"
        )
        # os.system(f'rm -rf {cam_calibresult_dir}')
    os.makedirs(cam_calibresult_dir, exist_ok=True)

    with open(cam_eol_extrinsics_path, "r") as f:
        eol_extrinsics = yaml.safe_load(f)

    for key, value in eol_extrinsics.items():
        if key in ["VIN", "software_version"]:
            eol_extrinsics[key] = str(value)
            continue

        for k, v in value.items():
            if isinstance(v, datetime.datetime):
                eol_extrinsics[key][k] = v.strftime("%Y-%m-%d %H:%M:%S")

    with open(cam_eol_intrinsics_path, "r") as f:
        eol_intrinsics = yaml.safe_load(f)

    for geely_cam_name, mach_cam_name in cam_eol_mapping.items():

        cam_calibresult_extrinsic_path = os.path.join(
            cam_calibresult_dir, mach_cam_name + "_extrinsic.json"
        )
        cam_calibresult_intrinsic_path = os.path.join(
            cam_calibresult_dir, mach_cam_name + "_intrinsic.json"
        )

        q = [
            eol_extrinsics[geely_cam_name]["rotation"]["w"],
            eol_extrinsics[geely_cam_name]["rotation"]["x"],
            eol_extrinsics[geely_cam_name]["rotation"]["y"],
            eol_extrinsics[geely_cam_name]["rotation"]["z"],
        ]
        # Convert quaternion to Euler angles (XYZ rotation order)
        euler_angles = (
            Rotation.from_quat([q[1], q[2], q[3], q[0]])
            .as_euler("XYZ", degrees=True)
            .tolist()
        )

        calibresult_extrinsics = {
            "transform": {
                "translation": eol_extrinsics[geely_cam_name]["translation"],
                "rotation": eol_extrinsics[geely_cam_name]["rotation"],
            },
            "euler_degree": {
                "RotX": euler_angles[0],
                "RotY": euler_angles[1],
                "RotZ": euler_angles[2],
            },
            "calib_status": 0,
            "information": f"{mach_cam_name}_tf_rfu",
            "calib_time": eol_extrinsics.get(
                "calib_time", time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            ),
        }

        distortion_model = None
        fD = []
        if eol_intrinsics[geely_cam_name]["distortion_type"] == 1:
            distortion_model = "FISHEYE".lower()
            fD = [
                [eol_intrinsics[geely_cam_name]["k_1"]],
                [eol_intrinsics[geely_cam_name]["k_2"]],
                [eol_intrinsics[geely_cam_name]["k_3"]],
                [eol_intrinsics[geely_cam_name]["k_4"]],
            ]
        elif eol_intrinsics[geely_cam_name]["distortion_type"] == 3:
            distortion_model = "PINHOLE".lower()
            fD = [
                [eol_intrinsics[geely_cam_name]["k_1"]],
                [eol_intrinsics[geely_cam_name]["k_2"]],
                [eol_intrinsics[geely_cam_name]["p_1"]],
                [eol_intrinsics[geely_cam_name]["p_2"]],
                [eol_intrinsics[geely_cam_name]["k_3"]],
            ]
        else:
            raise ValueError(
                f'unsupported distortion model {eol_intrinsics[geely_cam_name]["distortion_type"]}'
            )

        fK = [
            [
                eol_intrinsics[geely_cam_name]["focal_len_x"],
                0,
                eol_intrinsics[geely_cam_name]["optical_center_x"],
            ],
            [
                0,
                eol_intrinsics[geely_cam_name]["focal_len_y"],
                eol_intrinsics[geely_cam_name]["optical_center_y"],
            ],
            [0, 0, 1],
        ]

        calibresult_intrinsics = {
            "resolution": cam_resolution_mapping[mach_cam_name],
            "distortion_model": distortion_model,
            "K": fK,
            "D": fD,
        }

        with open(cam_calibresult_extrinsic_path, "w") as f:
            json.dump(calibresult_extrinsics, f, indent=4)
        with open(cam_calibresult_intrinsic_path, "w") as f:
            json.dump(calibresult_intrinsics, f, indent=4)

    print(
        "\n================================ Success ================================\n"
    )
    print(f"camera calibresult saved to {os.path.abspath(cam_calibresult_dir)}")
    print(
        "\n=========================================================================\n"
    )


def lidar_calibresult2eol(lidar_calibresult_dir, lidar_eol_extrinsics_dir):
    """lidar calibresult to eol

    Args:
        lidar_calibresult_dir (str): calibresult directory, see https://git-core.megvii-inc.com/transformer/calibresult
        lidar_eol_extrinsics_path (str): _path to the lidar eol extrinsics file
    """
    if not os.path.exists(lidar_calibresult_dir):
        raise FileNotFoundError(
            f"calibresult directory {lidar_calibresult_dir} not found"
        )
    lidar_calibresult_extrinsics_path = os.path.join(
        lidar_calibresult_dir, "front_2_lidar.json"
    )

    lidar_eol_extrinsics_path = os.path.join(
        lidar_eol_extrinsics_dir, "driving_lidar_calibresult.yaml"
    )
    if os.path.exists(lidar_eol_extrinsics_dir):
        print(
            f"lidar eol extrinsics file {lidar_eol_extrinsics_path} already exists, default to overwrite"
        )
    os.makedirs(lidar_eol_extrinsics_dir, exist_ok=True)

    with open(lidar_calibresult_extrinsics_path, "r") as f:
        lidar_calibresult_extrinsics = json.load(f)

    q = lidar_calibresult_extrinsics["transform"]["rotation"]
    euler_angles = (
        Rotation.from_quat([q["x"], q["y"], q["z"], q["w"]])
        .as_euler("ZYX", degrees=True)
        .tolist()
    )

    lidar_eol_extrinsics = {
        "lidar": {
            "translation": lidar_calibresult_extrinsics["transform"]["translation"],
            "rotation": lidar_calibresult_extrinsics["transform"]["rotation"],
            "euler_degree": {
                "RotX": euler_angles[0],
                "RotY": euler_angles[1],
                "RotZ": euler_angles[2],
            },
            "calib_status": 0,
            "calib_errcode": 128,
            "calib_valid": 1,
            "calib_time": lidar_calibresult_extrinsics.get(
                "calib_time", time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            ),
            "information": f"rfu_tf_front_2_lidar",
            "custom_fields": {"error_msg": "", "error_code": 1},
        }
    }

    with open(lidar_eol_extrinsics_path, "w") as f:
        yaml.dump(lidar_eol_extrinsics, f, indent=4, sort_keys=False)

    print(
        "\n================================ Success ================================\n"
    )
    print(f"lidar eol extrinsics saved to {os.path.abspath(lidar_eol_extrinsics_dir)}")
    print(
        "\n=========================================================================\n"
    )


def lidar_eol2calibresult(lidar_eol_dir, lidar_calibresult_dir):
    """lidar eol to calibresult

    Args:
        lidar_eol_dir (str): dir to the lidar eol file
        lidar_calibresult_dir (str): calibresult directory, see https://git-core.megvii-inc.com/transformer/calibresult
    """
    if not os.path.exists(lidar_eol_dir):
        raise FileNotFoundError(f"lidar eol directory {lidar_eol_dir} not found")
    lidar_eol_extrinsics_path = os.path.join(
        lidar_eol_dir, "driving_lidar_calibresult.yaml"
    )
    if not os.path.exists(lidar_eol_extrinsics_path):
        raise FileNotFoundError(
            f"lidar eol extrinsics file {lidar_eol_extrinsics_path} not found"
        )

    if os.path.exists(lidar_calibresult_dir):
        print(
            f"lidar calibresult directory {lidar_calibresult_dir} already exists, default to overwrite"
        )
    os.makedirs(lidar_calibresult_dir, exist_ok=True)

    lidar_calibresult_extrinsics_path = os.path.join(
        lidar_calibresult_dir, "lidar_params", "front_2_lidar.json"
    )
    lidar_ego_path = os.path.join(
        lidar_calibresult_dir, "lidar_params", "lidar_ego.json"
    )
    lidar_gnss_path = os.path.join(
        lidar_calibresult_dir, "lidar_params", "lidar_gnss.json"
    )

    ego_footprint_path = os.path.join(
        lidar_calibresult_dir, "ego_params", "ego_footprint.json"
    )
    ego_model_path = os.path.join(lidar_calibresult_dir, "ego_params", "ego_model.json")
    gnss_ego_path = os.path.join(lidar_calibresult_dir, "gnss_params", "gnss_ego.json")

    os.makedirs(os.path.dirname(lidar_calibresult_extrinsics_path), exist_ok=True)
    os.makedirs(os.path.dirname(lidar_ego_path), exist_ok=True)
    os.makedirs(os.path.dirname(lidar_gnss_path), exist_ok=True)
    os.makedirs(os.path.dirname(ego_footprint_path), exist_ok=True)
    os.makedirs(os.path.dirname(ego_model_path), exist_ok=True)
    os.makedirs(os.path.dirname(gnss_ego_path), exist_ok=True)

    with open(lidar_eol_extrinsics_path, "r") as f:
        lidar_eol_extrinsics = yaml.safe_load(f)

    for key, value in lidar_eol_extrinsics["lidar"].items():
        if isinstance(value, datetime.datetime):
            lidar_eol_extrinsics["lidar"][key] = value.strftime("%Y-%m-%d %H:%M:%S")

    q = lidar_eol_extrinsics["lidar"]["rotation"]
    euler_angles = (
        Rotation.from_quat([q["x"], q["y"], q["z"], q["w"]])
        .as_euler("XYZ", degrees=True)
        .tolist()
    )

    lidar_calibresult_extrinsics = {
        "transform": {
            "translation": lidar_eol_extrinsics["lidar"]["translation"],
            "rotation": lidar_eol_extrinsics["lidar"]["rotation"],
        },
        "euler_degree": {
            "RotX": euler_angles[0],
            "RotY": euler_angles[1],
            "RotZ": euler_angles[2],
        },
        "information": f"rfu_tf_front_2_lidar",
        "calib_time": lidar_eol_extrinsics["lidar"].get(
            "calib_time", time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        ),
    }

    with open(lidar_calibresult_extrinsics_path, "w") as f:
        json.dump(lidar_calibresult_extrinsics, f, indent=4, sort_keys=False)

    lidar_ego = {
        "transform": {
            "translation": {"x": 0.0, "y": 0.0, "z": -0.33000001311302185},
            "rotation": {
                "w": -0.7071067811865474,
                "x": -0.0,
                "y": -0.0,
                "z": 0.7071067811865477,
            },
        },
        "euler_degree": {"RotX": 0.0, "RotY": 0.0, "RotZ": -90.00000000000003},
        "calib_status": 0,
        "information": "ego_tf_rfu",
        "calib_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
    }

    lidar_gnss = {
        "transform": {
            "translation": {
                "x": 0.523590881085121,
                "y": 0.05304474445461211,
                "z": -0.7817174667526632,
            },
            "rotation": {"w": 0.707107, "x": 0.0, "y": 0.0, "z": -0.707107},
        },
        "euler_degree": {"RotX": 0.0, "RotY": 0.0, "RotZ": -90.0},
        "calib_status": 0,
        "information": "ins_tf_rfu",
        "calib_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
    }

    gnss_ego = {
        "transform": {
            "translation": {
                "x": -0.5204360261086516,
                "y": -0.04517422957247493,
                "z": 0.4543143428916788,
            },
            "rotation": {"w": 1.0, "x": 0.0, "y": 0.0, "z": 0.0},
        },
        "euler_degree": {"RotX": 0.0, "RotY": 0.0, "RotZ": 0.0},
        "calib_status": 0,
        "information": "ego_tf_ins",
        "calib_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
    }

    ego_footprint = {
        "transform": {
            "translation": {"x": 0, "y": 0, "z": 0.33},
            "rotation": {"w": 1.0, "x": 0.0, "y": 0.0, "z": 0.0},
        }
    }

    ego_model = {
        "0": [3.6112714464301434, -0.20176310449765822, 0.25866750575022235],
        "1": [3.6087258781568394, 0.18764614989904338, 0.26961979648642864],
        "10": [0.005535382435347458, 0.8854754127794613, -0.017109294137605202],
        "11": [-0.5656197656059323, 0.8654868586018591, 0.1738865603271571],
        "11-0": [-0.563769146013315, 0.865695375562721, 0.18191972457298533],
        "11-1": [-0.5726708613437008, 0.8646554154666091, 0.17578148808558458],
        "11-2": [-0.5648439569118962, 0.8668804980346785, 0.16894127064893283],
        "11-3": [-0.5603016127154978, 0.8689051736545479, 0.17406648262852453],
        "12": [1.0597164581460508, 0.6090803256514561, 1.2418323309685497],
        "13": [3.1644231681334105, -0.8978200302177193, 0.1608187165716659],
        "13-0": [3.1649215503266186, -0.8970663740464477, 0.16683399857378367],
        "13-1": [3.1692616902890522, -0.894566080215041, 0.16084136010119798],
        "13-2": [3.1629630890249087, -0.895914947989203, 0.15488248622745648],
        "13-3": [3.158129786422267, -0.8970606057825457, 0.1599150456331513],
        "14": [2.6966746975122495, -0.8826875738207489, -0.01710929413760609],
        "15": [-0.00553538243534657, -0.8854754127794617, 0.01710929413760498],
        "16": [-0.5744772661134441, -0.8678877877378852, 0.2206401479220219],
        "16-0": [-0.5652935741034231, -0.8745151351630804, 0.22636186682203974],
        "16-1": [-0.5594862604029291, -0.8760534826076967, 0.2222498606542822],
        "16-2": [-0.5599723284135765, -0.8768324056171375, 0.21723418354800916],
        "16-3": [-0.5735032550155905, -0.8705717929758414, 0.22156070115995874],
        "18": [-0.8683627024578797, 0.6719269141905224, 0.1276560281461676],
        "18-0": [-0.8699026133248573, 0.6733670733324577, 0.13469553211787377],
        "18-1": [-0.8736917380678362, 0.668472932093612, 0.12847541031710707],
        "18-2": [-0.8682974061407513, 0.6721409164558483, 0.12166020131232691],
        "18-3": [-0.8657245676501333, 0.677756508591778, 0.1278928273290294],
        "19": [-0.9344775162180943, 0.24969788902210688, 0.14388814774843484],
        "19-0": [-0.939418840264187, 0.2523340233008957, 0.1509332959183256],
        "19-1": [-0.9400431146864952, 0.24724795152381684, 0.14374009389668996],
        "19-2": [-0.9395356932078958, 0.25373040538594616, 0.13897351070026698],
        "19-3": [-0.934024508337103, 0.25600200766313197, 0.14411808691354544],
        "2": [3.6113903732548738, 0.18631755357377777, 0.19156766456373853],
        "20": [-0.9418212833669575, -0.21845207850442705, 0.12910041672004402],
        "20-0": [-0.9448249260642596, -0.21532209091100896, 0.13618429335730853],
        "20-1": [-0.9471712606143834, -0.21778562372642618, 0.13006663692316134],
        "20-2": [-0.9432197188801563, -0.2165482477876175, 0.12314896255176166],
        "20-3": [-0.9464917487928934, -0.20832944576489076, 0.1304115456708277],
        "21": [-0.8714234928433138, -0.6367565087736304, 0.08191403408112063],
        "21-0": [-0.8722101842568719, -0.6381858152506696, 0.08785862823165136],
        "21-1": [-0.8708967773303913, -0.6427780337131166, 0.0807041546773899],
        "21-2": [-0.8745121641273341, -0.6363149089258506, 0.07589198299584177],
        "21-3": [-0.8833916757834137, -0.6223051706515905, 0.08330018832751906],
        "22": [-0.5624800195568396, 0.014089152279328943, 0.3386685000641405],
        "3": [3.6089402119167646, -0.20229338461542135, 0.1775485551366922],
        "4": [3.4049878735740173, -0.6751160861749721, 0.17648957500632823],
        "4-0": [3.4054985699497875, -0.6743876968418707, 0.18350446190429048],
        "4-1": [3.408877012996929, -0.6692286646106771, 0.1775908750896127],
        "4-2": [3.404913988478647, -0.6749644851459484, 0.17049194563126013],
        "4-3": [3.401535545431505, -0.680123517377142, 0.1764055324459377],
        "5": [3.5595127595241807, -0.2929864541638647, 0.2602750389075621],
        "5-0": [3.568647005142897, -0.31400261770499593, 0.26863484913204627],
        "5-1": [3.5695030939681343, -0.31432566235191306, 0.2646145563467628],
        "5-2": [3.5686488802194583, -0.31824446896183733, 0.2595240481574377],
        "5-3": [3.5610116562569276, -0.299209188163732, 0.2600992829646971],
        "6": [3.575756099656477, 0.31334759959560454, 0.2882246144059877],
        "6-0": [3.576112431979425, 0.32331723947469015, 0.2905654065703984],
        "6-1": [3.574444019452695, 0.3022870689175887, 0.2868306607986131],
        "6-2": [3.5749041955340246, 0.313554600344383, 0.28021016432777546],
        "6-3": [3.5747147700337294, 0.31099217766383713, 0.2971334650692403],
        "7": [3.4081457154018433, 0.6604212197487191, 0.21368830591257293],
        "7-0": [3.401794627537671, 0.6660478112107282, 0.21376007959306742],
        "7-1": [3.413438288621986, 0.655732393530378, 0.21362849451216093],
        "7-2": [3.4069045805931335, 0.6610253808436704, 0.22369303348297476],
        "7-3": [3.409271549771317, 0.660714625223016, 0.20671360805530603],
        "8": [3.1702194806998643, 0.8785051801480259, 0.18359689422929537],
        "8-0": [3.1631124501408205, 0.8800812977843875, 0.18351962955967704],
        "8-1": [3.1792802593939395, 0.8780798358807909, 0.18274755818783373],
        "8-2": [3.1681378984495563, 0.8771208838747993, 0.19151843064359841],
        "8-3": [3.1714423534921057, 0.8778676586382033, 0.1745914431879254],
        "9": [2.7044068718120453, 0.8826875738207476, 0.01710929413760609],
    }

    with open(lidar_ego_path, "w") as f:
        json.dump(lidar_ego, f, indent=4, sort_keys=False)

    with open(lidar_gnss_path, "w") as f:
        json.dump(lidar_gnss, f, indent=4, sort_keys=False)

    with open(gnss_ego_path, "w") as f:
        json.dump(gnss_ego, f, indent=4, sort_keys=False)

    with open(ego_footprint_path, "w") as f:
        json.dump(ego_footprint, f, indent=4, sort_keys=False)

    with open(ego_model_path, "w") as f:
        json.dump(ego_model, f, indent=4, sort_keys=False)

    print(
        "\n================================ Success ================================\n"
    )
    print(
        f"lidar calibresult extrinsics saved to {os.path.abspath(lidar_calibresult_extrinsics_path)}"
    )
    print(f"lidar calibresult ego saved to {os.path.abspath(lidar_ego_path)}")
    print(f"lidar calibresult gnss saved to {os.path.abspath(lidar_gnss_path)}")
    print(f"gnss ego saved to {os.path.abspath(gnss_ego_path)}")
    print(f"ego footprint saved to {os.path.abspath(ego_footprint_path)}")
    print(f"ego model saved to {os.path.abspath(ego_model_path)}")
    print(
        "\n================================ Success ================================\n"
    )


def main():
    # lidar_calib2eol
    # lidar_calibresult_dir = "/data/production_calibraton/debug/calibresult/lidar_params"
    # output_dir = "/data/production_calibraton/debug/output"
    # lidar_calibresult2eol(lidar_calibresult_dir, output_dir)

    # lidar_eol2calib
    # lidar_eol_dir = "/data/production_calibraton/debug/eol"
    # output_dir = "/data/production_calibraton/debug/output"
    # lidar_eol2calibresult(lidar_eol_dir, output_dir)

    parser = argparse.ArgumentParser(
        description="Run calibration conversion functions."
    )
    parser.add_argument(
        "--mode",
        type=str,
        required=True,
        choices=[
            "all_calib2eol",
            "camera_calib2eol",
            "camera_eol2calib",
            "lidar_calib2eol",
            "lidar_eol2calib",
        ],
        help="Mode of operation",
    )
    parser.add_argument("--input_dir", type=str, required=True, help="Input directory")
    parser.add_argument(
        "--output_dir", type=str, required=True, help="Output directory"
    )
    parser.add_argument("--car", type=str, default="P177", help="Car model")

    args = parser.parse_args()

    if args.car == "P177" or args.car == "p177":
        camera_name_mappings = p177_geely2mach_camera_mappings
        camera_resolution_mapping = p177_camera_resolution_mapping
    else:
        raise ValueError("Invalid car model selected: {}".format(args.car))

    if args.mode == "camera_calib2eol":
        camera_calibresult2eol(args.input_dir, args.output_dir, camera_name_mappings)

    elif args.mode == "camera_eol2calib":
        camera_eol2calibresult(
            args.input_dir,
            args.output_dir,
            camera_name_mappings,
            camera_resolution_mapping,
        )

    elif args.mode == "lidar_calib2eol":
        lidar_calibresult2eol(args.input_dir, args.output_dir)

    elif args.mode == "lidar_eol2calib":
        lidar_eol2calibresult(args.input_dir, args.output_dir)

    elif args.mode == "all_calib2eol":
        args.lidar_input_dir = os.path.join(args.input_dir, "lidar_params")
        args.cam_input_dir = os.path.join(args.input_dir, "camera_params")
        lidar_calibresult2eol(args.lidar_input_dir, args.output_dir)
        camera_calibresult2eol(
            args.cam_input_dir, args.output_dir, camera_name_mappings
        )
    else:
        raise ValueError("Invalid mode selected")


if __name__ == "__main__":
    main()


# example usage:
# python camera_calibresult2eol.py --mode camera_calib2eol --input_dir /data/production_calibraton/debug/calibresult/camera_params --output_dir /data/production_calibraton/debug/output --car P177
