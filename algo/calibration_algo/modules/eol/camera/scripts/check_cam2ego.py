# -*- coding: utf-8 -*-
import cv2
import argparse
from pathlib import Path
import os
import numpy as np
import math
import sys
import pdb
import json
import quaternion
import transforms3d
import time
from scipy.spatial.transform import Rotation
from pyquaternion import Quaternion
import copy
import yaml


def load_ext(rt):
    rota = rt["transform"]["rotation"]
    tran = rt["transform"]["translation"]
    q = Quaternion([rota["w"], rota["x"], rota["y"], rota["z"]])
    t = np.array([tran["x"], tran["y"], tran["z"]]).astype(np.float32)
    r = q.rotation_matrix.astype(np.float32)
    rt = np.eye(4)
    rt[:3, :3] = r
    rt[:3, 3] = t
    return rt


def nothing(x):
    pass


def dump_result(final_transformation, information, data_path):
    avg_tvec = final_transformation[:, 3].flatten()
    avg_tvec = np.array(avg_tvec).reshape(-1).tolist()

    avg_quat = quaternion.from_rotation_matrix(final_transformation[:3, :3])

    main_r_sub = Rotation.from_matrix(final_transformation[:3, :3])
    euler = main_r_sub.as_euler("XYZ", degrees=True)
    res = {
        "transform": {
            "translation": {
                "x": avg_tvec[0],
                "y": avg_tvec[1],
                "z": avg_tvec[2],
            },
            "rotation": {
                "w": avg_quat.w,
                "x": avg_quat.x,
                "y": avg_quat.y,
                "z": avg_quat.z,
            },
        },
        "euler_degree": {
            "RotX": euler[0],
            "RotY": euler[1],
            "RotZ": euler[2],
        },
        "calib_status": 0,
        "information": information,
        "calib_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
    }
    with open(data_path, "w") as f:
        json.dump(
            res,
            f,
            indent=4,
        )


def getLinearEquation(p1x, p1y, p2x, p2y):
    sign = 1
    a = p2y - p1y
    if a < 0:
        sign = -1
        a = sign * a
    b = sign * (p1x - p2x)
    c = sign * (p1y * p2x - p1x * p2y)
    return [a, b, c]


def load_intrinsic_extrinsic(
    calib_yaml_path,
    camera_intrisic_path,
    lidar_id,
    camera_id,
    geely2mach_camera_mappings,
):
    # read yaml
    with open(calib_yaml_path, "r") as f:
        eol_calib_data = yaml.load(f, Loader=yaml.FullLoader)

    with open(camera_intrisic_path, "r") as f:
        camera_intrinsic = yaml.load(f, Loader=yaml.FullLoader)
    # get camera intrinsic

    for geely_name, mach_name in geely2mach_camera_mappings.items():
        if geely_name in camera_intrinsic:
            camera_intrinsic[mach_name] = camera_intrinsic[geely_name]
        if geely_name in eol_calib_data:
            eol_calib_data[mach_name] = eol_calib_data[geely_name]

    this_cam_intrinsic = camera_intrinsic[camera_id]
    this_cam_extrinsic = eol_calib_data[camera_id]
    if lidar_id is not None:
        lidar_extrinsic = eol_calib_data[lidar_id]
    else:
        lidar_extrinsic = None

    # camera intrinsic
    focal_len_x = this_cam_intrinsic["focal_len_x"]
    focal_len_y = this_cam_intrinsic["focal_len_y"]
    optical_center_x = this_cam_intrinsic["optical_center_x"]
    optical_center_y = this_cam_intrinsic["optical_center_y"]
    K = np.array(
        [
            [focal_len_x, 0, optical_center_x],
            [0, focal_len_y, optical_center_y],
            [0, 0, 1],
        ],
        dtype=np.float32,
    )
    if this_cam_intrinsic["distortion_type"] == 1:
        distortion_model = "FISHEYE"
    elif this_cam_intrinsic["distortion_type"] == 3:
        distortion_model = "PINHOLE"

    # distortion_model = this_cam_intrinsic["distortion_model"]
    if distortion_model == "FISHEYE":  # FISHEYE
        D = np.array(
            [
                this_cam_intrinsic["k_1"],
                this_cam_intrinsic["k_2"],
                this_cam_intrinsic["k_3"],
                this_cam_intrinsic["k_4"],
            ],
            dtype=np.float32,
        ).reshape(1, -1)
    elif distortion_model == "PINHOLE":
        D = np.array(
            [
                this_cam_intrinsic["k_1"],
                this_cam_intrinsic["k_2"],
                this_cam_intrinsic["p_1"],
                this_cam_intrinsic["p_2"],
                this_cam_intrinsic["k_3"] if "k_3" in this_cam_intrinsic else 0.0,
                this_cam_intrinsic["k_4"] if "k_4" in this_cam_intrinsic else 0.0,
                this_cam_intrinsic["k_5"] if "k_5" in this_cam_intrinsic else 0.0,
                this_cam_intrinsic["k_6"] if "k_6" in this_cam_intrinsic else 0.0,
            ],
            dtype=np.float32,
        ).reshape(1, -1)

    # camera extrinsic
    q = np.quaternion(
        this_cam_extrinsic["rotation"]["w"],
        this_cam_extrinsic["rotation"]["x"],
        this_cam_extrinsic["rotation"]["y"],
        this_cam_extrinsic["rotation"]["z"],
    )
    t = np.array(
        [
            this_cam_extrinsic["translation"]["x"],
            this_cam_extrinsic["translation"]["y"],
            this_cam_extrinsic["translation"]["z"],
        ]
    ).astype(np.float32)
    r = quaternion.as_rotation_matrix(q).astype(np.float32)
    cam_tf_rfu = np.eye(4)
    cam_tf_rfu[:3, :3] = r
    cam_tf_rfu[:3, 3] = t

    rfu_tf_cam = np.linalg.inv(cam_tf_rfu)
    # lidar extrinsic
    if lidar_extrinsic is None:
        return K, D, distortion_model, None, None, rfu_tf_cam

    q = np.quaternion(
        lidar_extrinsic["rotation"]["w"],
        lidar_extrinsic["rotation"]["x"],
        lidar_extrinsic["rotation"]["y"],
        lidar_extrinsic["rotation"]["z"],
    )
    t = np.array(
        [
            lidar_extrinsic["translation"]["x"],
            lidar_extrinsic["translation"]["y"],
            lidar_extrinsic["translation"]["z"],
        ]
    ).astype(np.float32)
    r = quaternion.as_rotation_matrix(q).astype(np.float32)
    rfu_tf_lidar = np.eye(4)
    rfu_tf_lidar[:3, :3] = r
    rfu_tf_lidar[:3, 3] = t

    cam_tf_lidar = np.linalg.inv(rfu_tf_cam) @ rfu_tf_lidar
    return K, D, distortion_model, cam_tf_lidar, rfu_tf_lidar, rfu_tf_cam


def check_cam_ego(
    image_path,
    cam_calib_result_path,
    camera_intrisic_path,
    cam_name,
    rfu_tf_ego,
    geely2mach_camera_mappings,
    output_dir="./build/output/check_cam2ego",
):
    os.makedirs(output_dir, exist_ok=True)

    intrinsic, distort, distort_model, _, _, rfu_tf_cam = load_intrinsic_extrinsic(
        cam_calib_result_path,
        camera_intrisic_path,
        None,
        cam_name,
        geely2mach_camera_mappings,
    )

    cam_tf_rfu = np.linalg.inv(rfu_tf_cam)
    cam_tf_ego = cam_tf_rfu @ rfu_tf_ego

    image_show_pitch_yaw_copy = None
    c_ground_pts = []
    c_vertical_pts = []

    def OnMouseGround(event, x, y, flags, param):
        if event == cv2.EVENT_LBUTTONDOWN:
            c_ground_pts.append([x, y])

    K = intrinsic
    # fx = K[0, 0]
    # fy = K[1, 1]
    # cx = K[0, 2]
    # cy = K[1, 2]
    # new_intrinsic_K = np.eye(3)
    # new_intrinsic_K[0][0] = fx
    # new_intrinsic_K[1][1] = fy
    # new_intrinsic_K[0][2] = cx
    # new_intrinsic_K[1][2] = cy
    # intrinsic = np.matrix(new_intrinsic_K)
    # new_intrinsic = np.matrix([[1000.05988153, 0.00000000e+00,  1907.08103852],
    #                            [0.00000000e+00, 1000.98728985,   1117.1254331],
    #                            [0.00000000e+00, 0.00000000e+00, 1.00000000e+00]])

    extrinsic = cam_tf_ego
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    image = cv2.imread(image_path)
    image_o = image.copy()

    if len(distort.reshape(-1, 1).tolist()) < 5:
        image = cv2.fisheye.undistortImage(
            image, intrinsic, distort, None, Knew=intrinsic
        )
        # print("use fisheye")
    else:
        image = cv2.undistort(image, intrinsic, distort, None, intrinsic)
        # print("use pinhole")

    H, W = image.shape[:2]
    vis_dis_thresh = 10
    vir_cam_height = 10
    rfu_tf_world = [
        1,
        0,
        0,
        -0.5 * vis_dis_thresh,
        0,
        -1,
        0,
        0.5 * vis_dis_thresh,
        0,
        0,
        -1,
        0,
        0,
        0,
        0,
        1,
    ]
    rfu_tf_world = np.array(rfu_tf_world).reshape(4, 4)
    vir_cam_tf_world = [
        1,
        0,
        0,
        -0.5 * vis_dis_thresh,
        0,
        1,
        0,
        -0.5 * vis_dis_thresh,
        0,
        0,
        1,
        vir_cam_height,
        0,
        0,
        0,
        1,
    ]
    vir_cam_tf_world = np.array(vir_cam_tf_world).reshape(4, 4)
    vir_cam_k = [100, 0, 1000, 0, 100, 1000, 0, 0, 1]
    vir_cam_k = np.array(vir_cam_k).reshape(3, 3)
    pitch_yaw_points = []
    yaw_roll_points = []

    # Using 1000m to represent points at infinity for visualization
    for i in range(-100, 100):
        pitch_yaw_points.append([1000, i, 0, 1])
        pitch_yaw_points.append([1000, 0, i, 1])
        yaw_roll_points.append([i, 0, -1000, 1])
        yaw_roll_points.append([0, i, -1000, 1])

    pitch_yaw_points = np.array(pitch_yaw_points)
    yaw_roll_points = np.array(yaw_roll_points)
    cv2.namedWindow("show", cv2.WINDOW_NORMAL)
    cv2.namedWindow("show_pitch_yaw", cv2.WINDOW_NORMAL)
    cv2.createTrackbar("pitch", "show", 50, 100, nothing)
    cv2.createTrackbar("yaw", "show", 50, 100, nothing)
    cv2.createTrackbar("roll", "show", 50, 100, nothing)
    cv2.createTrackbar("roll_pitch", "show", 80, 100, nothing)
    cv2.setMouseCallback("show_pitch_yaw", OnMouseGround)
    extrinsic_copy = extrinsic
    while True:
        image_show_pitch_yaw_copy = copy.deepcopy(image)
        pitch = cv2.getTrackbarPos("pitch", "show")
        yaw = cv2.getTrackbarPos("yaw", "show")
        roll = cv2.getTrackbarPos("roll", "show")
        roll_pitch = cv2.getTrackbarPos("roll_pitch", "show")
        pitch = (pitch - 50) * 0.05 * math.pi / 180.0
        yaw = (yaw - 50) * 0.05 * math.pi / 180.0
        roll = (roll - 50) * 0.05 * math.pi / 180.0
        roll_pitch = (roll_pitch - 50) * 2 * math.pi / 180.0
        delta_R = transforms3d.euler.euler2mat(pitch, yaw, roll, "sxyz")
        delta_T = np.eye(4)
        delta_T[:3, :3] = delta_R
        extrinsic_copy = delta_T @ extrinsic
        proj_matrix = intrinsic @ extrinsic_copy[:3, :4]
        pitch_yaw_points_image = (proj_matrix @ pitch_yaw_points.T).T
        H, W = image.shape[:2]
        for i, pt in enumerate(pitch_yaw_points_image):
            u = pt[0, 0]
            v = pt[0, 1]
            flag = pt[0, 2]
            u = int(u / flag)
            v = int(v / flag)
            if (0 <= u and u < W) and (0 <= v and v < H):
                cv2.circle(image_show_pitch_yaw_copy, (u, v), 1, (0, 0, 255), 2)
        # cv2.imwrite("drawed.png", image_show_pitch_yaw_copy)
        w_r_c = Rotation.from_euler("XYZ", [roll_pitch, 0, 0], degrees=False)
        diff = np.eye(4)
        diff[:3, :3] = w_r_c.as_matrix()
        half_road_x = 3.75 / 2.0
        road_y_max = 100
        road_y_1 = 60
        road_y_0 = 40
        road_y_min = 10
        ego_p_list = []
        ego_p_list.append(np.array([road_y_min, half_road_x, -0.33, 1]))
        ego_p_list.append(np.array([road_y_min, -half_road_x, -0.33, 1]))
        ego_p_list.append(np.array([road_y_0, half_road_x, -0.33, 1]))
        ego_p_list.append(np.array([road_y_0, -half_road_x, -0.33, 1]))
        ego_p_list.append(np.array([road_y_1, half_road_x, -0.33, 1]))
        ego_p_list.append(np.array([road_y_1, -half_road_x, -0.33, 1]))
        ego_p_list.append(np.array([road_y_max, half_road_x, -0.33, 1]))
        ego_p_list.append(np.array([road_y_max, -half_road_x, -0.33, 1]))
        ego_p_list = np.array(ego_p_list)
        rfu_uv_list = []
        rfu_uv_points = (proj_matrix @ ego_p_list.T).T
        for i, pt in enumerate(rfu_uv_points):
            u = pt[0, 0]
            v = pt[0, 1]
            flag = pt[0, 2]
            u = int(u / flag)
            v = int(v / flag)
            cv2.circle(image_show_pitch_yaw_copy, (u, v), 1, (255, 0, 0), 5)
            rfu_uv_list.append([int(u), int(v)])

        for i, c_ground_pt in enumerate(c_ground_pts):
            u = c_ground_pt[0]
            v = c_ground_pt[1]
            cv2.circle(image_show_pitch_yaw_copy, (u, v), 1, (0, 0, 255), 5)
            if (i + 1) % 2 != 0:
                continue
            a, b, c = getLinearEquation(
                u, v, c_ground_pts[i - 1][0], c_ground_pts[i - 1][1]
            )
            cv2.line(
                image_show_pitch_yaw_copy,
                (int(-1 * (b * H + c) / a), H),
                (int(-c / a), 0),
                (0, 255, 0),
                3,
            )  # 绿色，3个像素宽度

        cv2.imshow("show_pitch_yaw", image_show_pitch_yaw_copy)
        key = cv2.waitKey(100)

        if key == ord("q"):
            cv2.imwrite(
                os.path.join(output_dir, "cam2ego_{}.jpg".format(cam_name)),
                image_show_pitch_yaw_copy,
            )
            break
    cv2.destroyWindow("show")
    cv2.destroyWindow("show_pitch_yaw")
    R = extrinsic_copy[:3, :3]
    t = extrinsic_copy[:3, 3]
    q = transforms3d.quaternions.mat2quat(R)

    output_path_cam_tf_rfu = os.path.join(output_dir, f"cam_tf_rfu_refine.json")
    output_path_rfu_tf_cam = os.path.join(output_dir, f"rfu_tf_cam_refine.json")

    cam_tf_ego_refine = extrinsic_copy.copy()
    cam_tf_rfu_refine = cam_tf_ego_refine @ np.linalg.inv(rfu_tf_ego)
    dump_result(cam_tf_rfu_refine, f"cam_tf_rfu_refine", output_path_cam_tf_rfu)
    dump_result(
        np.linalg.inv(cam_tf_rfu_refine), f"rfu_tf_cam_refine", output_path_rfu_tf_cam
    )

    virtual_cam_r_ego = np.matrix(
        [[0.0, -1, 0.0, 0], [-1, 0, 0, 0], [0.0, 0.0, -1, 0], [0, 0, 0, 1]]
    )
    virtual_cam_r_cam_x = virtual_cam_r_ego @ np.linalg.inv(cam_tf_ego_refine)
    new_k = np.matrix(
        [
            [20, 0.00000000e00, 1912.30452319],
            [0.00000000e00, 20, 2048.34280002],
            [0.00000000e00, 0.00000000e00, 1.00000000e00],
        ]
    )
    mapx, mapy = cv2.initUndistortRectifyMap(
        intrinsic, distort, (virtual_cam_r_cam_x[:3, :3]), new_k, (W, H), cv2.CV_16SC2
    )
    dst = cv2.remap(
        image_o, mapx, mapy, cv2.INTER_LINEAR, borderMode=cv2.BORDER_CONSTANT
    )
    cv2.imwrite(os.path.join(output_dir, "refine_ground_{}.jpg".format(cam_name)), dst)


def main():

    cam_img_dir = "data/P177/P1023_geely_calibroom_2025.05.25/images"
    cam_calib_result_path = (
        "data/P177/P1023_geely_calibroom_2025.05.25/driving_camera_calibresult.yaml"
    )
    camera_intrisic_path = (
        "data/P177/P1023_geely_calibroom_2025.05.25/soca_cam_intrinsics.yaml"
    )

    # cam_name = "cam_back_100"
    cam_name = "cam_front_120"

    wheel_diameter = 0.7718  # P177

    cam_name2img_path = {}
    cam_names = [
        "cam_back_100",
        "cam_back_left_100",
        "cam_back_right_100",
        "cam_front_30",
        "cam_front_120",
        "cam_front_left_100",
        "cam_front_right_100",
    ]
    for tmp_cam_name in cam_names:
        img_path = os.path.join(cam_img_dir, tmp_cam_name + ".png")
        if not os.path.exists(img_path):
            img_path = os.path.join(cam_img_dir, tmp_cam_name + ".jpg")

        if not os.path.exists(img_path):
            print(f"Image path {img_path} does not exist.")
            exit
        cam_name2img_path[tmp_cam_name] = img_path

    image_path = cam_name2img_path[cam_name]

    output_dir = "./build/output/check_cam2ego"

    # ###############################################################################################

    geely2mach_camera_mappings = {
        "cam_back_100": "cam_back_100",
        "cam_side_left_back": "cam_back_left_100",
        "cam_side_right_back": "cam_back_right_100",
        "cam_front_120": "cam_front_120",
        "cam_front_30": "cam_front_30",
        "cam_side_left_front": "cam_front_left_100",
        "cam_side_right_front": "cam_front_right_100",
    }

    rfu_tf_ego = np.matrix(
        [[0, -1, 0, 0], [1, 0, 0, 0], [0, 0, 1, -wheel_diameter / 2], [0, 0, 0, 1]]
    )

    check_cam_ego(
        image_path,
        cam_calib_result_path,
        camera_intrisic_path,
        cam_name,
        rfu_tf_ego,
        geely2mach_camera_mappings,
        output_dir,
    )


if __name__ == "__main__":
    main()
