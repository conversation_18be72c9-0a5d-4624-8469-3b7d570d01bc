import os, sys
import numpy as np
import cv2
import matplotlib.pyplot as plt
from tqdm import tqdm
import yaml
import concurrent.futures
from sklearn.ensemble import IsolationForest
from scipy.spatial.transform import Rotation, RotationSpline


def cal_angle_diff(angle1, angle2):
    # diff = abs(angle1 - angle2) % 360
    # return min(diff, 360 - diff)
    diff = (angle1 - angle2) % 180
    return min(diff, 180 - diff)


def check_random_mask_vis(
    gt_calib_result_dir, simulation_calib_result_dir, eol_config_path, output_dir
):
    if os.path.exists(output_dir):
        os.system(f"rm -rf {output_dir}")
    os.makedirs(output_dir)

    gt_calib_file_lst, simulation_calib_file_lst = [], []
    for root, dirs, files in os.walk(gt_calib_result_dir):
        for file in files:
            if file.endswith(".yaml"):
                gt_calib_file_lst.append(os.path.join(root, file))
    for root, dirs, files in os.walk(simulation_calib_result_dir):
        for file in files:
            if file.endswith(".yaml"):
                simulation_calib_file_lst.append(os.path.join(root, file))

    eol_config = None
    with open(eol_config_path, "r") as f:
        lines = f.readlines()
        if lines[0].startswith("%YAML"):
            eol_config = yaml.safe_load("".join(lines[2:]))
        else:
            eol_config = yaml.safe_load("".join(lines))
    geely2mach_camera_mappings = eol_config["geely2mach_camera_mappings"]
    mach2geely_camera_mappings = {v: k for k, v in geely2mach_camera_mappings.items()}

    error_count = {
        mach_name: {
            "error_x": [],
            "error_y": [],
            "error_z": [],
            "error_RotX": [],
            "error_RotY": [],
            "error_RotZ": [],
        }
        for mach_name in mach2geely_camera_mappings.keys()
    }

    gt_calib_file_lst = sorted(gt_calib_file_lst)
    simulation_calib_file_lst = sorted(simulation_calib_file_lst)

    # check the number of files
    assert len(gt_calib_file_lst) == len(
        simulation_calib_file_lst
    ), "The number of files is not the same."

    for i, simulation_calib_file in enumerate(
        tqdm(
            simulation_calib_file_lst,
            position=0,
            colour="green",
            desc="Checking random mask",
        )
    ):
        gt_calib_file = gt_calib_file_lst[i]
        with open(gt_calib_file, "r") as f:
            lines = f.readlines()
            if lines[0].startswith("%YAML"):
                gt_calib_data = yaml.safe_load("".join(lines[2:]))
            else:
                gt_calib_data = yaml.safe_load("".join(lines))

        with open(simulation_calib_file, "r") as f:
            lines = f.readlines()
            if lines[0].startswith("%YAML"):
                simulation_calib_data = yaml.safe_load("".join(lines[2:]))
            else:
                simulation_calib_data = yaml.safe_load("".join(lines))

        for mach_camera_name, geely_camera_name in mach2geely_camera_mappings.items():
            gt_x, gt_y, gt_z = (
                gt_calib_data[geely_camera_name]["translation"]["x"],
                gt_calib_data[geely_camera_name]["translation"]["y"],
                gt_calib_data[geely_camera_name]["translation"]["z"],
            )
            gt_RotX, gt_RotY, gt_RotZ = (
                gt_calib_data[geely_camera_name]["euler_degree"]["RotX"],
                gt_calib_data[geely_camera_name]["euler_degree"]["RotY"],
                gt_calib_data[geely_camera_name]["euler_degree"]["RotZ"],
            )

            simulation_x, simulation_y, simulation_z = (
                simulation_calib_data[geely_camera_name]["translation"]["x"],
                simulation_calib_data[geely_camera_name]["translation"]["y"],
                simulation_calib_data[geely_camera_name]["translation"]["z"],
            )
            simulation_RotX, simulation_RotY, simulation_RotZ = (
                simulation_calib_data[geely_camera_name]["euler_degree"]["RotX"],
                simulation_calib_data[geely_camera_name]["euler_degree"]["RotY"],
                simulation_calib_data[geely_camera_name]["euler_degree"]["RotZ"],
            )

            error_count[mach_camera_name]["error_x"].append(abs(simulation_x - gt_x))
            error_count[mach_camera_name]["error_y"].append(abs(simulation_y - gt_y))
            error_count[mach_camera_name]["error_z"].append(abs(simulation_z - gt_z))
            error_count[mach_camera_name]["error_RotX"].append(
                cal_angle_diff(simulation_RotX, gt_RotX)
            )
            error_count[mach_camera_name]["error_RotY"].append(
                cal_angle_diff(simulation_RotY, gt_RotY)
            )
            error_count[mach_camera_name]["error_RotZ"].append(
                cal_angle_diff(simulation_RotZ, gt_RotZ)
            )

    for mach_camera_name, errors in tqdm(
        error_count.items(),
        position=0,
        colour="green",
        desc="Plotting error visualization",
    ):
        fig, axs = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle(f"Error Visualization for {mach_camera_name}", fontsize=16)

        axs[0, 0].hist(errors["error_x"], bins=20, color="b", edgecolor="black")
        axs[0, 0].set_title(
            "Error Distribution in X (Mean: {:.4f} m)".format(
                np.mean(errors["error_x"])
            )
        )
        axs[0, 0].set_xlabel("Error (m)")
        axs[0, 0].set_ylabel("Count")

        axs[0, 1].hist(errors["error_y"], bins=20, color="g", edgecolor="black")
        axs[0, 1].set_title(
            "Error Distribution in Y (Mean: {:.4f} m)".format(
                np.mean(errors["error_y"])
            )
        )
        axs[0, 1].set_xlabel("Error (m)")
        axs[0, 1].set_ylabel("Count")

        axs[0, 2].hist(errors["error_z"], bins=20, color="r", edgecolor="black")
        axs[0, 2].set_title(
            "Error Distribution in Z (Mean: {:.4f} m)".format(
                np.mean(errors["error_z"])
            )
        )
        axs[0, 2].set_xlabel("Error (m)")
        axs[0, 2].set_ylabel("Count")

        axs[1, 0].hist(errors["error_RotX"], bins=20, color="c", edgecolor="black")
        axs[1, 0].set_title(
            "Error Distribution in RotX (Mean: {:.4f} degrees)".format(
                np.mean(errors["error_RotX"])
            )
        )
        axs[1, 0].set_xlabel("Error (degrees)")
        axs[1, 0].set_ylabel("Count")

        axs[1, 1].hist(errors["error_RotY"], bins=20, color="m", edgecolor="black")
        axs[1, 1].set_title(
            "Error Distribution in RotY (Mean: {:.4f} degrees)".format(
                np.mean(errors["error_RotY"])
            )
        )
        axs[1, 1].set_xlabel("Error (degrees)")
        axs[1, 1].set_ylabel("Count")

        axs[1, 2].hist(errors["error_RotZ"], bins=20, color="y", edgecolor="black")
        axs[1, 2].set_title(
            "Error Distribution in RotZ (Mean: {:.4f} degrees)".format(
                np.mean(errors["error_RotZ"])
            )
        )
        axs[1, 2].set_xlabel("Error (degrees)")
        axs[1, 2].set_ylabel("Count")

        plt.tight_layout(rect=[0, 0, 1, 0.96])
        output_path = os.path.join(
            output_dir, f"{mach_camera_name}_error_visualization.png"
        )
        if not os.path.exists(os.path.dirname(output_path)):
            os.makedirs(os.path.dirname(output_path))
        plt.savefig(output_path)
        plt.close()


if __name__ == "__main__":
    gt_calib_result_dir = "./build/output_batch_test/multi-threaded_test/calib_result"
    eol_config_path = "./config/P177/eol_config.yaml"

    # random_mask_cam_front_120
    # simulation_calib_result_dir = "./build/output_batch_test/multi-threaded_test_random_mask_cam_front_120/calib_result"

    # actual_scene_simulation
    # simulation_calib_result_dir = "build/output_batch_test/multi-threaded_test_actual_scene_simulation/calib_result"

    # random_mask_1_AprilTag
    simulation_calib_result_dir = (
        "./build/output_batch_test/multi-threaded_test_random_mask_1/calib_result"
    )

    output_dir = "./build/output_batch_test/error_simulation_vis"
    check_random_mask_vis(
        gt_calib_result_dir, simulation_calib_result_dir, eol_config_path, output_dir
    )
    print("Saved to: {}".format(output_dir))
