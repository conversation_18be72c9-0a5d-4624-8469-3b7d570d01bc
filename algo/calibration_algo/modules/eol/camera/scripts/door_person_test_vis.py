import os, sys
import numpy as np
import cv2
import matplotlib.pyplot as plt
from tqdm import tqdm
import yaml
import concurrent.futures
from sklearn.ensemble import IsolationForest
from scipy.spatial.transform import Rotation, RotationSpline
from scipy.optimize import curve_fit

COLORS = ["green", "red", "blue", "yellow", "purple", "orange", "pink", "cyan", "black"]


def cal_angle_diff(angle1, angle2, is_sybolic=False):
    # diff = abs(angle1 - angle2) % 360
    # return min(diff, 360 - diff)
    diff = (angle1 - angle2) % 180
    if not is_sybolic:
        return min(diff, 180 - diff)
    else:
        if angle1 > angle2:
            return min(diff, 180 - diff)
        else:
            return -min(diff, 180 - diff)


def get_mean_all_res_from_dir(calib_dir, eol_config):
    base_calib_path_lst = []
    for root, dirs, files in os.walk(calib_dir):
        for file in files:
            if file.endswith(".yaml"):
                base_calib_path_lst.append(os.path.join(root, file))
    base_calib_path_lst.sort()
    base_calib_data_dict = {
        key: [] for key in eol_config["geely2mach_camera_mappings"].values()
    }

    for base_calib_path in tqdm(
        base_calib_path_lst, position=0, colour="green", desc="Loading calib data"
    ):
        with open(base_calib_path, "r") as f:
            lines = f.readlines()
            if lines[0].startswith("%YAML"):
                this_data = yaml.safe_load("".join(lines[2:]))
            else:
                this_data = yaml.safe_load("".join(lines))

        for key, value in this_data.items():
            if key not in eol_config["geely2mach_camera_mappings"].keys():
                continue
            base_calib_data_dict[
                eol_config["geely2mach_camera_mappings"].get(key)
            ].append(value)

    base_calib_data = {
        cam_name: {
            "translation": {"x": 0, "y": 0, "z": 0},
            "euler_degree": {"RotX": 0, "RotY": 0, "RotZ": 0},
        }
        for cam_name in eol_config["geely2mach_camera_mappings"].values()
    }

    for key, value in base_calib_data_dict.items():
        if len(value) == 0:
            continue
        for sub_data in value:
            for attr in ["x", "y", "z"]:
                base_calib_data[key]["translation"][attr] += sub_data["translation"][
                    attr
                ]
            for attr in ["RotX", "RotY", "RotZ"]:
                base_calib_data[key]["euler_degree"][attr] += sub_data["euler_degree"][
                    attr
                ]

    for key, value in base_calib_data.items():
        for attr in ["x", "y", "z"]:
            base_calib_data[key]["translation"][attr] /= len(base_calib_path_lst)
        for attr in ["RotX", "RotY", "RotZ"]:
            base_calib_data[key]["euler_degree"][attr] /= len(base_calib_path_lst)

    return base_calib_data, base_calib_data_dict


def statistics_base_stability_plot(
    calib_dir_dict, base_key, eol_config_path, output_dir
):
    if base_key not in calib_dir_dict:
        raise ValueError(f"Base key {base_key} not found in calib_dir_dict")

    if os.path.exists(output_dir):
        os.system(f"rm -rf {output_dir}")
    os.makedirs(output_dir)

    with open(eol_config_path, "r") as f:
        lines = f.readlines()
        if lines[0].startswith("%YAML"):
            eol_config = yaml.safe_load("".join(lines[2:]))
        else:
            eol_config = yaml.safe_load("".join(lines))

    base_calib_dir = calib_dir_dict[base_key]

    base_calib_data, base_calib_data_all = get_mean_all_res_from_dir(
        base_calib_dir, eol_config
    )

    # plot base error distribution
    for key, value in base_calib_data.items():
        fig, axs = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle(f"Base {key} Error Distribution", fontsize=16)
        for idx, attr in enumerate(["x", "y", "z"]):
            error_lst = [
                tmp["translation"][attr] - value["translation"][attr]
                for tmp in base_calib_data_all[key]
            ]
            axs[0, idx].hist(error_lst, bins=50, color="b", edgecolor="black")
            axs[0, idx].set_title(f"Translation {attr} Error Distribution")
            axs[0, idx].set_xlabel("Error")
            axs[0, idx].set_ylabel("Count")

        for idx, attr in enumerate(["RotX", "RotY", "RotZ"]):
            error_lst = [
                cal_angle_diff(
                    tmp["euler_degree"][attr], value["euler_degree"][attr], True
                )
                for tmp in base_calib_data_all[key]
            ]
            axs[1, idx].hist(error_lst, bins=50, color="g", edgecolor="black")
            axs[1, idx].set_title(f"Rotation {attr} Error Distribution")
            axs[1, idx].set_xlabel("Error (degrees)")
            axs[1, idx].set_ylabel("Count")

        plt.tight_layout(rect=[0, 0, 1, 0.96])

        plt.savefig(
            os.path.join(output_dir, "base_error_distribution_{}.png".format(key))
        )

    error_count = {
        mach_name: {
            "error_x": [],
            "error_y": [],
            "error_z": [],
            "error_RotX": [],
            "error_RotY": [],
            "error_RotZ": [],
        }
        for mach_name in eol_config["geely2mach_camera_mappings"].keys()
    }

    for key, calib_dir in tqdm(
        calib_dir_dict.items(), position=1, colour="red", desc="Processing"
    ):
        if key == base_key:
            continue
        calib_data, calib_data_all = get_mean_all_res_from_dir(calib_dir, eol_config)
        for mach_camera_name, geely_camera_name in eol_config[
            "geely2mach_camera_mappings"
        ].items():
            for attr in ["x", "y", "z"]:
                for idx, sub_data in enumerate(calib_data_all[geely_camera_name]):
                    error_count[mach_camera_name][f"error_{attr}"].append(
                        {
                            "value": abs(
                                sub_data["translation"][attr]
                                - base_calib_data[geely_camera_name]["translation"][
                                    attr
                                ]
                            ),
                            "source": f"{key}_data_{idx}",
                        }
                    )
            for attr in ["RotX", "RotY", "RotZ"]:
                for idx, sub_data in enumerate(calib_data_all[geely_camera_name]):
                    error_count[mach_camera_name][f"error_{attr}"].append(
                        {
                            "value": cal_angle_diff(
                                sub_data["euler_degree"][attr],
                                base_calib_data[geely_camera_name]["euler_degree"][
                                    attr
                                ],
                            ),
                            "source": f"{key}_data_{idx}",
                        }
                    )
                    for mach_camera_name, errors in tqdm(
                        error_count.items(),
                        position=0,
                        colour="green",
                        desc="Plotting error visualization",
                    ):
                        fig, axs = plt.subplots(2, 3, figsize=(15, 10))
                        fig.suptitle(
                            f"Error Visualization for {mach_camera_name}", fontsize=16
                        )

                        for idx, attr in enumerate(["x", "y", "z"]):
                            error_values = [e["value"] for e in errors[f"error_{attr}"]]
                            axs[0, idx].hist(
                                error_values, bins=50, color="b", edgecolor="black"
                            )
                            axs[0, idx].set_title(
                                f"Error Distribution in {attr.upper()} (Mean: {np.mean(error_values):.4f} m)"
                            )
                            axs[0, idx].set_xlabel("Error (m)")
                            axs[0, idx].set_ylabel("Count")

                        for idx, attr in enumerate(["RotX", "RotY", "RotZ"]):
                            error_values = [e["value"] for e in errors[f"error_{attr}"]]
                            axs[1, idx].hist(
                                error_values, bins=50, color="g", edgecolor="black"
                            )
                            axs[1, idx].set_title(
                                f"Error Distribution in {attr} (Mean: {np.mean(error_values):.4f} degrees)"
                            )
                            axs[1, idx].set_xlabel("Error (degrees)")
                            axs[1, idx].set_ylabel("Count")

                        plt.tight_layout(rect=[0, 0, 1, 0.96])
                        output_path = os.path.join(
                            output_dir, f"{mach_camera_name}_error_vis.png"
                        )
                        if not os.path.exists(os.path.dirname(output_path)):
                            os.makedirs(os.path.dirname(output_path))
                        plt.savefig(output_path)
                        plt.close()


def gaussian(x, amplitude, mean, std_dev):
    return amplitude * np.exp(-((x - mean) ** 2) / (2 * std_dev**2))


def statistics_base_stability_fit_guassian(calib_dir_dict, eol_config_path, output_dir):

    if os.path.exists(output_dir):
        os.system(f"rm -rf {output_dir}")
    os.makedirs(output_dir)

    with open(eol_config_path, "r") as f:
        lines = f.readlines()
        if lines[0].startswith("%YAML"):
            eol_config = yaml.safe_load("".join(lines[2:]))
        else:
            eol_config = yaml.safe_load("".join(lines))

    gaussian_res = {
        cam_name: {} for cam_name in eol_config["geely2mach_camera_mappings"].values()
    }

    # Combine all data for Gaussian fitting and plotting across calib_dir_dict
    for key, calib_dir in tqdm(
        calib_dir_dict.items(), position=1, colour="red", desc="Processing"
    ):
        calib_data, calib_data_all = get_mean_all_res_from_dir(calib_dir, eol_config)

        for mach_camera_name in eol_config["geely2mach_camera_mappings"].values():
            if key not in gaussian_res[mach_camera_name]:
                gaussian_res[mach_camera_name][key] = {}

            for attr in ["x", "y", "z"]:

                attr_data = [
                    tmp["translation"][attr] for tmp in calib_data_all[mach_camera_name]
                ]

                hist, bin_edges = np.histogram(attr_data, bins=50, density=False)
                bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2

                initial_guess = [1, np.mean(attr_data), np.std(attr_data)]
                popt, _ = curve_fit(
                    gaussian, bin_centers, hist, p0=initial_guess, maxfev=500000
                )

                x_fit = np.linspace(bin_centers[0], bin_centers[-1], 100)
                y_fit = gaussian(x_fit, *popt)
                gaussian_res[mach_camera_name][key][attr] = [
                    popt,
                    x_fit,
                    y_fit,
                    attr_data,
                ]

            for attr in ["RotX", "RotY", "RotZ"]:
                attr_data = [
                    tmp["euler_degree"][attr]
                    for tmp in calib_data_all[mach_camera_name]
                ]

                hist, bin_edges = np.histogram(attr_data, bins=50, density=False)
                bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2

                initial_guess = [1, np.mean(attr_data), np.std(attr_data)]
                popt, _ = curve_fit(
                    gaussian, bin_centers, hist, p0=initial_guess, maxfev=500000
                )

                x_fit = np.linspace(bin_centers[0], bin_centers[-1], 100)
                y_fit = gaussian(x_fit, *popt)
                gaussian_res[mach_camera_name][key][attr] = [popt, x_fit, y_fit]

    # Plotting
    for cam_name, cam_values in tqdm(
        gaussian_res.items(), position=1, colour="green", desc="Plotting"
    ):
        fig, axs = plt.subplots(2, 3, figsize=(15, 10))

        for i, (key, values) in enumerate(cam_values.items()):
            tmp_color = plt.cm.tab10(i / len(calib_dir_dict.keys()))

            for idx, attr in enumerate(["x", "y", "z"]):
                popt, x_fit, y_fit, attr_data = values[attr]
                axs[0, idx].plot(
                    x_fit,
                    y_fit,
                    label=f"{key}: mean = {popt[1]:.5f}, std = {popt[2]:.5f}",
                    color=tmp_color,
                )
                axs[0, idx].set_title(f"Translation {attr} Gaussian Fit")
                axs[0, idx].set_xlabel("Value (m)")
                axs[0, idx].set_ylabel("Density")
                axs[0, idx].legend()

            for idx, attr in enumerate(["RotX", "RotY", "RotZ"]):
                popt, x_fit, y_fit = values[attr]
                axs[1, idx].plot(
                    x_fit,
                    y_fit,
                    label=f"{key}: mean = {popt[1]:.5f}, std = {popt[2]:.5f}",
                    color=tmp_color,
                )
                axs[1, idx].set_title(f"Rotation {attr} Gaussian Fit")
                axs[1, idx].set_xlabel("Value (degrees)")
                axs[1, idx].set_ylabel("Density")
                axs[1, idx].legend()
            axs[0, 0].set_xlim(-1, 1)
            axs[0, 1].set_xlim(-2, 2)
            axs[0, 2].set_xlim(-2, 2)
            axs[1, 0].set_xlim(-10, 10)
            axs[1, 1].set_xlim(-10, 10)
            axs[1, 2].set_xlim(-10, 10)

        plt.tight_layout(rect=[0, 0, 1, 0.96])
        output_path = os.path.join(output_dir, f"{cam_name}_gaussian_fit.png")

        if not os.path.exists(os.path.dirname(output_path)):
            os.makedirs(os.path.dirname(output_path))
        plt.savefig(output_path)
        plt.close(fig)


def statistics_base_stability(calib_dir_dict, eol_config_path, output_dir):
    if os.path.exists(output_dir):
        os.system(f"rm -rf {output_dir}")
    os.makedirs(output_dir)

    with open(eol_config_path, "r") as f:
        lines = f.readlines()
        if lines[0].startswith("%YAML"):
            eol_config = yaml.safe_load("".join(lines[2:]))
        else:
            eol_config = yaml.safe_load("".join(lines))

    hist_res = {
        cam_name: {} for cam_name in eol_config["geely2mach_camera_mappings"].values()
    }

    # Combine all data for plot histogram
    for key, calib_dir in tqdm(
        calib_dir_dict.items(), position=1, colour="red", desc="Processing"
    ):
        calib_data, calib_data_all = get_mean_all_res_from_dir(calib_dir, eol_config)

        for mach_camera_name in eol_config["geely2mach_camera_mappings"].values():
            if key not in hist_res[mach_camera_name]:
                hist_res[mach_camera_name][key] = {}

            for attr in ["x", "y", "z"]:
                attr_data = [
                    tmp["translation"][attr] for tmp in calib_data_all[mach_camera_name]
                ]

                hist, bin_edges = np.histogram(attr_data, bins=50, density=False)

                hist_res[mach_camera_name][key][attr] = [hist, bin_edges]

            for attr in ["RotX", "RotY", "RotZ"]:
                attr_data = [
                    tmp["euler_degree"][attr]
                    for tmp in calib_data_all[mach_camera_name]
                ]

                hist, bin_edges = np.histogram(attr_data, bins=50, density=False)
                hist_res[mach_camera_name][key][attr] = [hist, bin_edges]

    # Plotting
    for cam_name, cam_values in tqdm(
        hist_res.items(), position=1, colour="green", desc="Plotting"
    ):
        fig, axs = plt.subplots(2, 3, figsize=(15, 10))
        for i, (key, values) in enumerate(cam_values.items()):
            tmp_color = plt.cm.tab10(i / len(calib_dir_dict.keys()))

            for idx, attr in enumerate(["x", "y", "z"]):
                hist, bin_edges = values[attr]
                axs[0, idx].bar(
                    bin_edges[:-1],
                    hist,
                    width=np.diff(bin_edges),
                    align="edge",
                    color=tmp_color,
                    alpha=0.7,
                    label=key,
                )
                axs[0, idx].set_title(f"Translation {attr.upper()} Histogram")
                axs[0, idx].set_xlabel("Value (m)")
                axs[0, idx].set_ylabel("Count")
                axs[0, idx].legend()

            for idx, attr in enumerate(["RotX", "RotY", "RotZ"]):
                hist, bin_edges = values[attr]
                axs[1, idx].bar(
                    bin_edges[:-1],
                    hist,
                    width=np.diff(bin_edges),
                    align="edge",
                    color=tmp_color,
                    alpha=0.7,
                    label=key,
                )
                axs[1, idx].set_title(f"Rotation {attr.upper()} Histogram")
                axs[1, idx].set_xlabel("Value (degrees)")
                axs[1, idx].set_ylabel("Count")
                axs[1, idx].legend()

        plt.tight_layout(rect=[0, 0, 1, 0.96])
        output_path = os.path.join(output_dir, f"{cam_name}_combined_histogram.png")
        if not os.path.exists(os.path.dirname(output_path)):
            os.makedirs(os.path.dirname(output_path))
        plt.savefig(output_path)
        plt.close(fig)
        print(f"Saved combined histogram for {cam_name} to {output_path}")


def calculate_diff(calib_dir_dict, eol_config_path, output_dir):
    if os.path.exists(output_dir):
        os.system(f"rm -rf {output_dir}")
    os.makedirs(output_dir)

    with open(eol_config_path, "r") as f:
        lines = f.readlines()
        if lines[0].startswith("%YAML"):
            eol_config = yaml.safe_load("".join(lines[2:]))
        else:
            eol_config = yaml.safe_load("".join(lines))

    calib_res = {
        cam_name: {} for cam_name in eol_config["geely2mach_camera_mappings"].values()
    }

    # Combine all data for plot histogram
    for key, calib_dir in tqdm(
        calib_dir_dict.items(), position=1, colour="red", desc="Processing"
    ):
        calib_data, calib_data_all = get_mean_all_res_from_dir(calib_dir, eol_config)

        for cam_name, cam_values in calib_data.items():
            if key not in calib_res[cam_name]:
                calib_res[cam_name][key] = {}

            for attr in ["x", "y", "z"]:
                calib_res[cam_name][key][attr] = cam_values.get("translation", {}).get(
                    attr, "N/A"
                )
            for attr in ["RotX", "RotY", "RotZ"]:
                calib_res[cam_name][key][attr] = cam_values.get("euler_degree", {}).get(
                    attr, "N/A"
                )

    # Prepare data for table output
    table_data = []
    for cam_name, cam_values in calib_res.items():
        for key, values in cam_values.items():
            row = [cam_name, key]
            for attr in ["x", "y", "z", "RotX", "RotY", "RotZ"]:
                if attr in values:
                    mean_value = np.mean(values[attr])
                    std_value = np.std(values[attr])
                    # row.append(f"{mean_value:.5f} ± {std_value:.5f}")
                    row.append(f"{mean_value:.5f}")
                else:
                    row.append("N/A")
            table_data.append(row)

    # Define table headers
    headers = [
        "Camera",
        "Key",
        "Translation X",
        "Translation Y",
        "Translation Z",
        "Rotation X",
        "Rotation Y",
        "Rotation Z",
    ]

    # Write table to markdown file
    markdown_output_path = os.path.join(output_dir, "calibration_results.md")
    with open(markdown_output_path, "w") as md_file:
        md_file.write("# Calibration Results\n\n")
        md_file.write("| " + " | ".join(headers) + " |\n")
        md_file.write("|" + " --- |" * len(headers) + "\n")
        for row in table_data:
            md_file.write("| " + " | ".join(row) + " |\n")

    print(f"Calibration results saved to {markdown_output_path}")


def main():

    door_calib_dir_dict = {
        "door1": "build/output_batch_test/multi-threaded_test_2025.04.27/calib_result/door1_parse",
        "door2": "build/output_batch_test/multi-threaded_test_2025.04.27/calib_result/door2_parse",
        "door3": "build/output_batch_test/multi-threaded_test_2025.04.27/calib_result/door3_parse",
        "door4": "build/output_batch_test/multi-threaded_test_2025.04.27/calib_result/door4_parse",
        "door5": "build/output_batch_test/multi-threaded_test_2025.04.27/calib_result/door5_parse",
    }

    person_calib_dir_dict = {
        "person1": "build/output_batch_test/multi-threaded_test_2025.04.27/calib_result/one_parse",
        "person3": "build/output_batch_test/multi-threaded_test_2025.04.27/calib_result/three_parse",
        "person4": "build/output_batch_test/multi-threaded_test_2025.04.27/calib_result/four_parse",
    }

    eol_config_path = "config/P177/eol_config.yaml"
    output_dir = "build/output_batch_test/door_person_test_vis"

    print("Door calibration directories:")
    calculate_diff(
        door_calib_dir_dict, eol_config_path, os.path.join(output_dir, "door")
    )
    # statistics_base_stability_fit_guassian(door_calib_dir_dict, eol_config_path, os.path.join(output_dir, "door"))
    # statistics_base_stability(door_calib_dir_dict, eol_config_path, os.path.join(output_dir, "door"))

    print("Person calibration directories:")
    calculate_diff(
        person_calib_dir_dict, eol_config_path, os.path.join(output_dir, "person")
    )
    # statistics_base_stability(person_calib_dir_dict, eol_config_path, os.path.join(output_dir, "person"))
    # statistics_base_stability_fit_guassian(person_calib_dir_dict, eol_config_path, os.path.join(output_dir, "person"))


if __name__ == "__main__":
    main()
