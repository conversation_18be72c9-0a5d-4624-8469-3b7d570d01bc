# -*- coding: utf-8 -*-
import cv2
import argparse
from pathlib import Path
import os
import numpy as np
import math
import sys
import json
import quaternion
import transforms3d
import concurrent.futures
import time
from scipy.spatial.transform import Rotation

# import pcl
from matplotlib import cm
import open3d as o3d
import yaml
from tqdm import tqdm

color_lst = [
    tuple((np.array(cm.get_cmap("tab20", 20)(i)[:3]) * 255).astype(int))
    for i in range(15)
]


def load_intrinsic_extrinsic(
    eol_calib_yaml_path, camera_intrisic_path, geely2mach_camera_mappings
):
    # read yaml
    with open(eol_calib_yaml_path, "r") as f:
        eol_calib_data = yaml.load(f, Loader=yaml.FullLoader)

    with open(camera_intrisic_path, "r") as f:
        camera_intrinsic = yaml.load(f, Loader=yaml.FullLoader)

    cam_names = [x for x in camera_intrinsic.keys() if "cam_" in x]
    # cam_names = [geely2mach_camera_mappings[x] for x in cam_names]

    cam_name2intrinsic = {}
    cam_name2extrinsic = {}

    for cam_name in tqdm(
        cam_names,
        position=0,
        colour="green",
        desc="Loading camera intrinsic and extrinsic",
    ):
        this_cam_intrinsic = camera_intrinsic[cam_name]
        this_cam_extrinsic = eol_calib_data[cam_name]

        # camera intrinsic
        focal_len_x = this_cam_intrinsic["focal_len_x"]
        focal_len_y = this_cam_intrinsic["focal_len_y"]
        optical_center_x = this_cam_intrinsic["optical_center_x"]
        optical_center_y = this_cam_intrinsic["optical_center_y"]
        K = np.array(
            [
                [focal_len_x, 0, optical_center_x],
                [0, focal_len_y, optical_center_y],
                [0, 0, 1],
            ],
            dtype=np.float32,
        )

        if this_cam_intrinsic["distortion_type"] == 1:
            distortion_model = "FISHEYE"
        elif this_cam_intrinsic["distortion_type"] == 3:
            distortion_model = "PINHOLE"
        else:
            raise ValueError("Unknown distortion model")

        if distortion_model == "FISHEYE":  # FISHEYE
            D = np.array(
                [
                    this_cam_intrinsic["k_1"],
                    this_cam_intrinsic["k_2"],
                    this_cam_intrinsic["k_3"],
                    this_cam_intrinsic["k_4"],
                ],
                dtype=np.float32,
            ).reshape(1, -1)
        elif distortion_model == "PINHOLE":
            D = np.array(
                [
                    this_cam_intrinsic["k_1"],
                    this_cam_intrinsic["k_2"],
                    this_cam_intrinsic["p_1"],
                    this_cam_intrinsic["p_2"],
                    this_cam_intrinsic["k_3"] if "k_3" in this_cam_intrinsic else 0.0,
                    this_cam_intrinsic["k_4"] if "k_4" in this_cam_intrinsic else 0.0,
                    this_cam_intrinsic["k_5"] if "k_5" in this_cam_intrinsic else 0.0,
                    this_cam_intrinsic["k_6"] if "k_6" in this_cam_intrinsic else 0.0,
                ],
                dtype=np.float32,
            ).reshape(1, -1)

        # camera extrinsic
        q = np.quaternion(
            this_cam_extrinsic["rotation"]["w"],
            this_cam_extrinsic["rotation"]["x"],
            this_cam_extrinsic["rotation"]["y"],
            this_cam_extrinsic["rotation"]["z"],
        )
        t = np.array(
            [
                this_cam_extrinsic["translation"]["x"],
                this_cam_extrinsic["translation"]["y"],
                this_cam_extrinsic["translation"]["z"],
            ]
        ).astype(np.float32)
        r = quaternion.as_rotation_matrix(q).astype(np.float32)
        cam_tf_rfu = np.eye(4)
        cam_tf_rfu[:3, :3] = r
        cam_tf_rfu[:3, 3] = t

        cam_name2intrinsic[cam_name] = {
            "K": K,
            "D": D,
            "distortion_model": distortion_model,
        }
        cam_name2extrinsic[cam_name] = {
            "cam_tf_rfu": cam_tf_rfu,
            "rfu_tf_cam": np.linalg.inv(cam_tf_rfu),
        }

    cam_name2intrinsic = {
        geely2mach_camera_mappings[x]: y for x, y in cam_name2intrinsic.items()
    }
    cam_name2extrinsic = {
        geely2mach_camera_mappings[x]: y for x, y in cam_name2extrinsic.items()
    }

    return cam_name2intrinsic, cam_name2extrinsic


def GetClockAngle(v1, v2):
    TheNorm = np.linalg.norm(v1) * np.linalg.norm(v2)
    cos_theta = np.dot(v1, v2) / TheNorm
    return cos_theta


def warp_cam_ground(info_dict):
    cam_name = info_dict["cam_name"]
    undistort_image = info_dict["undistort_image"]
    cam_K = info_dict["cam_K"]
    cam_tf_rfu = info_dict["cam_tf_rfu"]
    size_w = info_dict["size_w"]
    size_h = info_dict["size_h"]
    save_path = info_dict["save_path"]
    w_m = info_dict["w_m"]
    h_m = info_dict["h_m"]

    # print(f"{cam_name} begin")

    # Create a virtual camera, suspended 10 meters above the ground
    # Calculate the intrinsic parameters of the virtual camera so that its field of view just covers the w_m×h_m ground area
    vir_cam_height = 10
    vir_cam_K = [
        size_w / 2 * vir_cam_height / (0.5 * w_m),
        0,
        size_w / 2,
        0,
        size_h / 2 * vir_cam_height / (0.5 * h_m),
        size_h / 2,
        0,
        0,
        1,
    ]
    vir_cam_K = np.array(vir_cam_K).reshape(3, 3)

    rfu_tf_world = [1, 0, 0, -0.5 * w_m, 0, -1, 0, 0.5 * h_m, 0, 0, -1, 0, 0, 0, 0, 1]
    rfu_tf_world = np.array(rfu_tf_world).reshape(4, 4)
    vir_cam_tf_world = [
        1,
        0,
        0,
        -0.5 * w_m,
        0,
        1,
        0,
        -0.5 * h_m,
        0,
        0,
        1,
        vir_cam_height,
        0,
        0,
        0,
        1,
    ]

    vir_cam_tf_world = np.array(vir_cam_tf_world).reshape(4, 4)
    rfu_tf_vir_cam = rfu_tf_world @ np.linalg.inv(vir_cam_tf_world)
    cam_h, cam_w = undistort_image.shape[:2]
    undistort_image[0, 0] = 0

    map_x = np.zeros((size_h, size_w), dtype=np.float32)
    map_y = np.zeros((size_h, size_w), dtype=np.float32)
    ground_h = rfu_tf_vir_cam[2, 3]
    vir_cam_uvs = []
    for x in range(0, size_w, 1):
        for y in range(0, size_h, 1):
            vir_cam_uvs.append([x, y])

    # get the ray of the virtual camera
    cam_rays = cv2.undistortPoints(
        np.array(vir_cam_uvs, dtype=np.float32), vir_cam_K, None, None, P=np.eye(3)
    )

    vir_cam_3d_points = []
    print(f"{cam_name} ray num: {len(cam_rays)}")
    for cam_ray in tqdm(cam_rays, desc=f"{cam_name}:", colour="green"):
        cam_ray_vec = np.array([cam_ray[0][0], cam_ray[0][1], 1])
        rfu_cam_ray_vec = (rfu_tf_vir_cam[:3, :3]) @ cam_ray_vec
        ground_vec = np.array([0, 0, 1])

        # Calculate the angle between the ray and the ground normal vector
        cos_theta = GetClockAngle(rfu_cam_ray_vec, ground_vec)

        ground_H = abs(ground_h / cos_theta) / np.linalg.norm(
            cam_ray_vec
        )  # Calculate the height of the point on the ground
        vir_cam_3d = [
            cam_ray[0][0] * ground_H,
            cam_ray[0][1] * ground_H,
            ground_H,
            1,
        ]  # Calculate the 3D coordinates of the point on the ground
        vir_cam_3d_points.append(np.array(vir_cam_3d))

    vir_cam_3d_points = np.array(vir_cam_3d_points)
    rfu_points = (rfu_tf_vir_cam @ vir_cam_3d_points.T).T
    cam_3d_points = (cam_tf_rfu @ rfu_points.T).T

    # Calculate the pixel position in the actual camera:
    for cam_3d_point, vir_cam_uv in zip(cam_3d_points, vir_cam_uvs):
        if cam_3d_point[2] < 0:
            continue
        # Calculate normalized coordinates
        vir_cam_ray = [
            cam_3d_point[0] / cam_3d_point[2],
            cam_3d_point[1] / cam_3d_point[2],
            1,
        ]

        if abs(vir_cam_ray[0]) > 5:
            continue
        vir_cam_ray = np.array(vir_cam_ray).reshape(-1, 1, 3)
        # Project onto the camera plane
        cam_uv, _ = cv2.projectPoints(
            np.array(vir_cam_ray, dtype=np.float32),
            np.zeros(3),
            np.zeros(3),
            cam_K,
            None,
            None,
        )

        cam_u = int(cam_uv[0, 0, 0])
        cam_v = int(cam_uv[0, 0, 1])
        if cam_u > (cam_w - 1) or cam_u < 0 or cam_v > (cam_h - 1) or cam_v < 0:
            continue
        map_x[vir_cam_uv[1], vir_cam_uv[0]] = cam_u
        map_y[vir_cam_uv[1], vir_cam_uv[0]] = cam_v

    save_path_ = f"{save_path}/{cam_name}_warp_ground.jpg"

    dst = cv2.remap(undistort_image, map_x, map_y, cv2.INTER_LINEAR)
    # gray_warp_image_list.append(dst)
    cv2.imwrite(save_path_, dst)
    return dst


def warp_bev_img(
    cam_name2image_path,
    cam_name2intrinsic,
    cam_name2extrinsic,
    output_dir,
    bev_img_size,
    calib_room_size,
):
    # global gray_warp_image_list
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    cam_name2undistort_image = {}
    cam_name2new_intrinsic_K = {}

    for index, (cam_name, img_path) in enumerate(cam_name2image_path.items()):
        print(f"Processing {index + 1} / {len(cam_name2image_path)} image: {cam_name}")

        intrinsic = cam_name2intrinsic[cam_name]
        extrinsic = cam_name2extrinsic[cam_name]
        if not os.path.exists(img_path):
            print(f"Image {img_path} does not exist.")
            exit()

        img = cv2.imread(img_path)

        K = intrinsic["K"]
        distort = intrinsic["D"]

        fx = K[0, 0] / 2
        fy = K[1, 1] / 2
        cx = K[0, 2]
        cy = K[1, 2]
        new_intrinsic_K = np.eye(3)
        new_intrinsic_K[0][0] = fx
        new_intrinsic_K[1][1] = fy
        new_intrinsic_K[0][2] = cx
        new_intrinsic_K[1][2] = cy
        print("new_intrinsic_K: \n", new_intrinsic_K)

        cam_name2new_intrinsic_K[cam_name] = new_intrinsic_K

        if len(distort.reshape(-1, 1).tolist()) < 5:
            undistort_img = cv2.fisheye.undistortImage(
                img, K, distort, None, Knew=new_intrinsic_K
            )
            # print("use fisheye")
        else:
            undistort_img = cv2.undistort(img, K, distort, None, new_intrinsic_K)
            # print("use pinhole")

        save_path_ = f"{output_dir}/undistort_{cam_name}.jpg"
        cv2.imwrite(save_path_, undistort_img)
        cam_name2undistort_image[cam_name] = undistort_img

    warp_img_map_list = []

    for index, cam_name in enumerate(cam_name2image_path.keys()):
        print(f"Processing {index + 1} / {len(cam_name2image_path)} image: {cam_name}")

        new_intrinsic = cam_name2new_intrinsic_K[cam_name]
        undistort_image = cam_name2undistort_image[cam_name]
        cam_tf_rfu = cam_name2extrinsic[cam_name]["cam_tf_rfu"]

        info_dict = {}
        info_dict["cam_name"] = cam_name
        info_dict["undistort_image"] = undistort_image
        info_dict["cam_K"] = new_intrinsic
        info_dict["cam_tf_rfu"] = cam_tf_rfu
        info_dict["size_w"] = bev_img_size[0]
        info_dict["size_h"] = bev_img_size[1]
        info_dict["w_m"] = calib_room_size[0]
        info_dict["h_m"] = calib_room_size[1]
        info_dict["save_path"] = output_dir

        warp_img_map_list.append(info_dict)
    # print(len(warp_img_map_list))

    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        warp_img_ground_lst = list(executor.map(warp_cam_ground, warp_img_map_list))

    bev_img = np.zeros((info_dict["size_h"], info_dict["size_w"], 3), dtype=np.uint8)

    for index, warp_image_ground in enumerate(warp_img_ground_lst):
        color = color_lst[index % len(color_lst)]

        if len(warp_image_ground.shape) == 2:  # Ensure warp_image_ground has 3 channels
            warp_image_ground = cv2.cvtColor(warp_image_ground, cv2.COLOR_GRAY2BGR)

        mask = (warp_image_ground > 0).astype(np.uint8)
        color_mask = np.zeros_like(warp_image_ground, dtype=np.uint8)
        color_mask[:, :] = color
        blended_image = cv2.addWeighted(color_mask, 0.3, warp_image_ground, 0.7, 0)
        # bev_img = np.where(np.dstack([mask]), blended_image, bev_img)
        bev_img = np.where(
            np.dstack([mask]),
            np.where(
                np.all(bev_img > 0, axis=-1, keepdims=True),
                blended_image * 0.5 + bev_img * 0.5,
                blended_image,
            ),
            bev_img,
        )

    return bev_img


if __name__ == "__main__":

    output_dir = "build/output/check_bev"

    # Z10 data
    # cam_name2image_path = {
    #     "cam_back_70": "./data/Z10/geely_calibroom_image/cam_back_70.jpg",
    #     "cam_back_left_100": "./data/Z10/geely_calibroom_image/cam_back_left_100.jpg",
    #     "cam_back_right_100": "./data/Z10/geely_calibroom_image/cam_back_right_100.jpg",
    #     "cam_front_30": "./data/Z10/geely_calibroom_image/cam_front_30.jpg",
    #     "cam_front_120": "./data/Z10/geely_calibroom_image/cam_front_120.jpg",
    #     "cam_front_left_100": "./data/Z10/geely_calibroom_image/cam_front_left_100.jpg",
    #     "cam_front_right_100": "./data/Z10/geely_calibroom_image/cam_front_right_100.jpg",
    # }

    # P177 data
    cam_calib_result_path = (
        "data/P177/P1023_geely_calibroom_2025.05.25/driving_camera_calibresult.yaml"
    )
    camera_intrisic_path = (
        "data/P177/P1023_geely_calibroom_2025.05.25/soca_cam_intrinsics.yaml"
    )
    cam_img_dir = "data/P177/geely_calibroom_images_2025.05.25"

    cam_name2cam_path = {}
    cam_names = [
        "cam_back_100",
        "cam_back_left_100",
        "cam_back_right_100",
        "cam_front_30",
        "cam_front_120",
        "cam_front_left_100",
        "cam_front_right_100",
    ]
    for cam_name in cam_names:
        img_path = os.path.join(cam_img_dir, cam_name + ".png")
        if not os.path.exists(img_path):
            img_path = os.path.join(cam_img_dir, cam_name + ".jpg")

        if not os.path.exists(img_path):
            print(f"Image path {img_path} does not exist.")
            exit
        cam_name2cam_path[cam_name] = img_path

    geely2mach_camera_mappings = camera_mappings = {
        "cam_back_100": "cam_back_100",
        "cam_side_left_back": "cam_back_left_100",
        "cam_side_right_back": "cam_back_right_100",
        "cam_front_120": "cam_front_120",
        "cam_front_30": "cam_front_30",
        "cam_side_left_front": "cam_front_left_100",
        "cam_side_right_front": "cam_front_right_100",
    }

    bev_img_size = (600, 1200)  # w, h
    calib_room_size = (20, 40)  # w, h, meter
    # calib_room_size = (9, 23)  # w, h, meter

    cam_name2intrinsic, cam_name2extrinsic = load_intrinsic_extrinsic(
        cam_calib_result_path, camera_intrisic_path, geely2mach_camera_mappings
    )

    # bev_img = bev_check(cam_name2image_path,
    #                     cam_name2intrinsic, cam_name2extrinsic)

    bev_img = warp_bev_img(
        cam_name2cam_path,
        cam_name2intrinsic,
        cam_name2extrinsic,
        output_dir,
        bev_img_size,
        calib_room_size,
    )

    cv2.imwrite(os.path.join(output_dir, "bev_img.png"), bev_img)
