import os, sys
import cv2 as cv
import json
from scipy.spatial.transform import Rotation as R
import numpy as np
import copy
import yaml
import math
import quaternion
from tqdm import tqdm
from matplotlib import cm


color_lst = [
    tuple((np.array(cm.get_cmap("tab20", 20)(i)[:3]) * 255).astype(int))
    for i in range(15)
]


def load_ext_from_json(path):
    with open(path, "r") as f:
        json_data = json.load(f)
    T = np.eye(4)
    T[:3, :3] = R.from_quat(
        [
            json_data["transform"]["rotation"]["x"],
            json_data["transform"]["rotation"]["y"],
            json_data["transform"]["rotation"]["z"],
            json_data["transform"]["rotation"]["w"],
        ]
    ).as_matrix()
    T[0, 3] = json_data["transform"]["translation"]["x"]
    T[1, 3] = json_data["transform"]["translation"]["y"]
    T[2, 3] = json_data["transform"]["translation"]["z"]

    return T


def load_intrinsic_yaml(path, cam_id):
    K, D = np.eye(3), np.zeros(8)
    with open(path, "r") as f:
        data = yaml.safe_load(f)

        K[0, 0] = data[cam_id]["focal_len_x"]
        K[1, 1] = data[cam_id]["focal_len_y"]
        K[0, 2] = data[cam_id]["optical_center_x"]
        K[1, 2] = data[cam_id]["optical_center_y"]
        if cam_id != "cam_front":
            K[1, 2] -= 98

        D[0] = data[cam_id]["k_1"]
        D[1] = data[cam_id]["k_2"]
        D[2] = data[cam_id]["p_1"]
        D[3] = data[cam_id]["p_2"]
        D[4] = data[cam_id]["k_3"]
        D[5] = data[cam_id]["k_4"]
        D[6] = data[cam_id]["k_5"]
        D[7] = data[cam_id]["k_6"]

    return K, D


def load_intrinsic_extrinsic(
    eol_calib_yaml_path, camera_intrisic_path, p177_geely2mach_camera_mappings=None
):
    # read yaml
    with open(eol_calib_yaml_path, "r") as f:
        eol_calib_data = yaml.load(f, Loader=yaml.FullLoader)

    with open(camera_intrisic_path, "r") as f:
        camera_intrinsic = yaml.load(f, Loader=yaml.FullLoader)

    cam_names = [cam_name for cam_name in camera_intrinsic.keys() if "cam_" in cam_name]

    cam_name2intrinsic = {}
    cam_name2extrinsic = {}

    for cam_name in tqdm(
        cam_names,
        position=0,
        colour="green",
        desc="Loading camera intrinsic and extrinsic",
    ):
        this_cam_intrinsic = camera_intrinsic[cam_name]
        this_cam_extrinsic = eol_calib_data[cam_name]

        # camera intrinsic
        focal_len_x = this_cam_intrinsic["focal_len_x"]
        focal_len_y = this_cam_intrinsic["focal_len_y"]
        optical_center_x = this_cam_intrinsic["optical_center_x"]
        optical_center_y = this_cam_intrinsic["optical_center_y"]
        K = np.array(
            [
                [focal_len_x, 0, optical_center_x],
                [0, focal_len_y, optical_center_y],
                [0, 0, 1],
            ],
            dtype=np.float32,
        )

        if this_cam_intrinsic["distortion_type"] == 1:
            distortion_model = "FISHEYE"
        elif this_cam_intrinsic["distortion_type"] == 3:
            distortion_model = "PINHOLE"
        else:
            raise ValueError(
                "Unknown distortion type: {}".format(
                    this_cam_intrinsic["distortion_type"]
                )
            )

        if distortion_model == "FISHEYE":  # FISHEYE
            D = np.array(
                [
                    this_cam_intrinsic["k_1"],
                    this_cam_intrinsic["k_2"],
                    this_cam_intrinsic["k_3"],
                    this_cam_intrinsic["k_4"],
                ],
                dtype=np.float32,
            ).reshape(1, -1)
        elif distortion_model == "PINHOLE":
            D = np.array(
                [
                    this_cam_intrinsic["k_1"],
                    this_cam_intrinsic["k_2"],
                    this_cam_intrinsic["p_1"],
                    this_cam_intrinsic["p_2"],
                    this_cam_intrinsic["k_3"] if "k_3" in this_cam_intrinsic else 0.0,
                    this_cam_intrinsic["k_4"] if "k_4" in this_cam_intrinsic else 0.0,
                    this_cam_intrinsic["k_5"] if "k_5" in this_cam_intrinsic else 0.0,
                    this_cam_intrinsic["k_6"] if "k_6" in this_cam_intrinsic else 0.0,
                ],
                dtype=np.float32,
            ).reshape(1, -1)

        # camera extrinsic
        q = np.quaternion(
            this_cam_extrinsic["rotation"]["w"],
            this_cam_extrinsic["rotation"]["x"],
            this_cam_extrinsic["rotation"]["y"],
            this_cam_extrinsic["rotation"]["z"],
        )
        t = np.array(
            [
                this_cam_extrinsic["translation"]["x"],
                this_cam_extrinsic["translation"]["y"],
                this_cam_extrinsic["translation"]["z"],
            ]
        ).astype(np.float32)
        r = quaternion.as_rotation_matrix(q).astype(np.float32)
        cam_tf_rfu = np.eye(4)
        cam_tf_rfu[:3, :3] = r
        cam_tf_rfu[:3, 3] = t

        cam_name2intrinsic[p177_geely2mach_camera_mappings[cam_name]] = {
            "K": K,
            "D": D,
            "distortion_model": distortion_model,
        }
        cam_name2extrinsic[p177_geely2mach_camera_mappings[cam_name]] = {
            "cam_tf_rfu": cam_tf_rfu,
            "rfu_tf_cam": np.linalg.inv(cam_tf_rfu),
        }

    return cam_name2intrinsic, cam_name2extrinsic


def construct_skew_mat(rot, trans):
    # Construct the skew-symmetric matrix for calculating the essential matrix
    # Formula: E = [t]× R, where [t]× is the skew-symmetric matrix of the translation vector

    ## R t -> t^R

    tx, ty, tz = trans[0], trans[1], trans[2]
    t_up = np.array([[0, -tz, ty], [tz, 0, -tx], [-ty, tx, 0]])
    return t_up @ rot


def nothing(x):
    pass


if __name__ == "__main__":

    cam_name0 = sys.argv[1]
    cam_name1 = sys.argv[2]

    # Z10 data
    # eol_calib_yaml_path = "data/Z10/cam_lidar_eol_calibration_output.yaml"
    # camera_intrisic_path = "data/Z10/soca_cam_intrisics.yaml"
    # output_dir = "build/output/check_epipolar"

    # cam_name2image_path = {
    #     "cam_back_70": "./data/Z10/geely_calibroom_image/cam_back_70.jpg",
    #     "cam_back_left_100": "./data/Z10/geely_calibroom_image/cam_back_left_100.jpg",
    #     "cam_back_right_100": "./data/Z10/geely_calibroom_image/cam_back_right_100.jpg",
    #     "cam_front_30": "./data/Z10/geely_calibroom_image/cam_front_30.jpg",
    #     "cam_front_120": "./data/Z10/geely_calibroom_image/cam_front_120.jpg",
    #     "cam_front_left_100": "./data/Z10/geely_calibroom_image/cam_front_left_100.jpg",
    #     "cam_front_right_100": "./data/Z10/geely_calibroom_image/cam_front_right_100.jpg",
    # }

    # P177 data

    p177_geely2mach_camera_mappings = {
        "cam_back_100": "cam_back_100",
        "cam_front_120": "cam_front_120",
        "cam_front_30": "cam_front_30",
        "cam_side_left_back": "cam_back_left_100",
        "cam_side_left_front": "cam_front_left_100",
        "cam_side_right_back": "cam_back_right_100",
        "cam_side_right_front": "cam_front_right_100",
    }

    cam_name2img_path = {
        "cam_back_100": "data/P177/P1023_geely_calibroom_2025.05.25/images/cam_back_100.png",
        "cam_back_left_100": "data/P177/P1023_geely_calibroom_2025.05.25/images/cam_back_left_100.png",
        "cam_back_right_100": "data/P177/P1023_geely_calibroom_2025.05.25/images/cam_back_right_100.png",
        "cam_front_30": "data/P177/P1023_geely_calibroom_2025.05.25/images/cam_front_30.png",
        "cam_front_120": "data/P177/P1023_geely_calibroom_2025.05.25/images/cam_front_120.png",
        "cam_front_left_100": "data/P177/P1023_geely_calibroom_2025.05.25/images/cam_front_left_100.png",
        "cam_front_right_100": "data/P177/P1023_geely_calibroom_2025.05.25/images/cam_front_right_100.png",
    }

    output_dir = "build/output/check_epipolar"
    cam_calib_result_path = (
        "data/P177/P1023_geely_calibroom_2025.05.25/driving_camera_calibresult.yaml"
    )
    camera_intrisic_path = (
        "data/P177/P1023_geely_calibroom_2025.05.25/soca_cam_intrinsics.yaml"
    )

    if os.path.exists(output_dir):
        os.system("rm -r " + output_dir)
    os.makedirs(output_dir)

    img0 = cv.imread(cam_name2img_path[cam_name0])
    img1 = cv.imread(cam_name2img_path[cam_name1])

    cam_name2intrinsic, cam_name2extrinsic = load_intrinsic_extrinsic(
        cam_calib_result_path, camera_intrisic_path, p177_geely2mach_camera_mappings
    )

    K0, D0 = cam_name2intrinsic[cam_name0]["K"], cam_name2intrinsic[cam_name0]["D"]
    K1, D1 = cam_name2intrinsic[cam_name1]["K"], cam_name2intrinsic[cam_name1]["D"]

    cam0_tf_cam1 = (
        cam_name2extrinsic[cam_name0]["cam_tf_rfu"]
        @ cam_name2extrinsic[cam_name1]["rfu_tf_cam"]
    )
    cam1_tf_cam0 = np.linalg.inv(cam0_tf_cam1)

    map0_x, map0_y = cv.initUndistortRectifyMap(
        K0, D0, None, K0, (img0.shape[1], img0.shape[0]), cv.CV_32FC1
    )
    map1_x, map1_y = cv.initUndistortRectifyMap(
        K1, D1, None, K1, (img1.shape[1], img1.shape[0]), cv.CV_32FC1
    )

    img0 = cv.remap(img0, map0_x, map0_y, cv.INTER_LINEAR)
    img1 = cv.remap(img1, map1_x, map1_y, cv.INTER_LINEAR)

    # if sys.argv[1] == "cam_front_120":
    #     img0 = cv.resize(img0, (img0.shape[1]//2, img0.shape[0]//2))
    #     K0 /= 2
    #     K0[2,2] = 1
    # if sys.argv[2] == "cam_front_120":
    #     img1 = cv.resize(img1, (img1.shape[1]//2, img1.shape[0]//2))
    #     K1 /= 2
    #     K1[2,2] = 1

    # Ensure the widths of the two images are consistent, and scale K accordingly
    if img0.shape[1] < img1.shape[1]:
        img0 = cv.resize(img0, (img1.shape[1], img0.shape[0]))
        K0 = K1 * img0.shape[1] / img1.shape[1]
        K0[2, 2] = 1
    elif img0.shape[1] > img1.shape[1]:
        img1 = cv.resize(img1, (img0.shape[1], img1.shape[0]))
        K1 = K0 * img1.shape[1] / img0.shape[1]
        K1[2, 2] = 1

    clicked_pts = []

    def MouseCallback(event, x, y, flags, param):
        if event == cv.EVENT_LBUTTONDOWN:
            clicked_pts.append([x, y])

    cv.namedWindow("show_epipolar", cv.WINDOW_NORMAL)
    cv.setMouseCallback("show_epipolar", MouseCallback)
    cv.createTrackbar("pitch", "show_epipolar", 50, 100, nothing)
    cv.createTrackbar("yaw", "show_epipolar", 50, 100, nothing)
    cv.createTrackbar("roll", "show_epipolar", 50, 100, nothing)
    cv.createTrackbar("thickness", "show_epipolar", 1, 20, nothing)

    while True:
        img0_show = copy.deepcopy(img0)
        img1_show = copy.deepcopy(img1)

        pitch = cv.getTrackbarPos("pitch", "show_epipolar")
        yaw = cv.getTrackbarPos("yaw", "show_epipolar")
        roll = cv.getTrackbarPos("roll", "show_epipolar")

        # 控制画线的粗细
        thickness = cv.getTrackbarPos("thickness", "show_epipolar")
        thickness = thickness if thickness > 0 else 1

        # Calculate the fine-tuning rotation matrix based on the slider values
        pitch = (pitch - 50) * 0.05
        yaw = (yaw - 50) * 0.05
        roll = (roll - 50) * 0.05
        delta_R = R.from_euler("xyz", [roll, pitch, yaw], degrees=True).as_matrix()
        delta_T = np.eye(4)
        delta_T[:3, :3] = delta_R
        show_cam1_tf_cam0 = delta_T @ cam1_tf_cam0

        # Compute the essential matrix and fundamental matrix
        E10 = construct_skew_mat(show_cam1_tf_cam0[:3, :3], show_cam1_tf_cam0[:3, 3])
        F10 = np.linalg.inv(K1.T) @ E10 @ np.linalg.inv(K0)

        # Draw corresponding epipolar lines for clicked points
        if len(clicked_pts) > 0:
            for i, pt in enumerate(clicked_pts):
                color = tuple(map(int, color_lst[i % 15]))
                cv.circle(img0_show, (pt[0], pt[1]), thickness + 3, color, -1)

                lines1 = cv.computeCorrespondEpilines(
                    np.array(clicked_pts), 1, F10
                ).reshape(-1, 3)

            for i, r in enumerate(lines1):
                color = tuple(map(int, color_lst[i % 15]))
                x0, y0 = map(int, [0, -r[2] / r[1]])
                x1, y1 = map(
                    int,
                    [img1_show.shape[1], -(r[2] + r[0] * img1_show.shape[1]) / r[1]],
                )
                img1_show = cv.line(img1_show, (x0, y0), (x1, y1), color, thickness)

        img_out = cv.hconcat([img0_show, img1_show])
        cv.imshow("show_epipolar", img_out)
        key = cv.waitKey(100)
        if key == ord("q"):
            cv.imwrite(output_dir + "/epipolar.png", img_out)
            break
