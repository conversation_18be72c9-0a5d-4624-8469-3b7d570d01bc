# -*- coding: utf-8 -*-
import cv2
import argparse
from pathlib import Path
import os
import numpy as np
import math
import sys
import json
import quaternion
import transforms3d
import time
from scipy.spatial.transform import Rotation

# from pclpy import pcl
# import pcl
# import open3d as o3d
from matplotlib import cm

# import open3d as o3d
import matplotlib.cm as cm
import yaml


def nothing(x):
    pass


def fix(a):
    b = cv2.cvtColor(a, cv2.COLOR_BGR2YUV)
    y0 = np.expand_dims(b[..., 0][::, ::2], axis=2)
    u = np.expand_dims(b[..., 1][::, ::2], axis=2)
    y1 = np.expand_dims(b[..., 0][::, 1::2], axis=2)
    v = np.expand_dims(b[..., 2][::, ::2], axis=2)
    img_uyvy = np.concatenate((u, y0, v, y1), axis=2)
    img_uyvy_cvt = img_uyvy.reshape(
        img_uyvy.shape[0], img_uyvy.shape[1] * 2, int(img_uyvy.shape[2] / 2)
    )
    b = cv2.cvtColor(img_uyvy_cvt, cv2.COLOR_YUV2BGR_YUYV)
    return b


def dump_result(final_transformation, information, data_path):
    avg_tvec = final_transformation[:, 3]
    avg_quat = quaternion.from_rotation_matrix(final_transformation[:3, :3])
    avg_tvec = list(map(float, avg_tvec))
    main_r_sub = Rotation.from_matrix(final_transformation[:3, :3])
    euler = main_r_sub.as_euler("XYZ", degrees=True)
    res = {
        "transform": {
            "translation": {
                "x": avg_tvec[0],
                "y": avg_tvec[1],
                "z": avg_tvec[2],
            },
            "rotation": {
                "w": avg_quat.w,
                "x": avg_quat.x,
                "y": avg_quat.y,
                "z": avg_quat.z,
            },
        },
        "euler_degree": {
            "RotX": euler[0],
            "RotY": euler[1],
            "RotZ": euler[2],
        },
        "calib_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
        "information": information,
        "destination": "rfu",
    }
    with open(data_path, "w") as f:
        json.dump(
            res,
            f,
            indent=4,
        )


def intensity2color(intensity):
    # 检查数组是否为空
    if len(intensity) == 0:
        return np.array([]).reshape(0, 3)

    intensity = np.array(intensity, dtype=np.float32)

    min_intensity = np.percentile(intensity, 10)
    max_intensity = np.percentile(intensity, 90)
    intensity = (intensity - min_intensity) / (max_intensity - min_intensity)  # 归一化

    # cmap = cm.get_cmap("coolwarm")  # 使用coolwarm色图
    # colors = cmap(1 - intensity) * 255.0
    min_intensity = np.percentile(intensity, 10)
    max_intensity = np.percentile(intensity, 60)

    color1 = [0, 0, 255]  # 红色
    color2 = [0, 255, 0]  # 绿色

    normalized_intensity = (intensity - min_intensity) / (max_intensity - min_intensity)
    normalized_intensity = np.clip(normalized_intensity, 0, 1)
    colors = (1 - normalized_intensity[:, None]) * np.array(
        color1
    ) + normalized_intensity[:, None] * np.array(color2)

    return colors


def load_lidar_npy(path):
    print(path)
    points = np.loadtxt(str(path))
    print(points)
    nan_indices = np.isnan(points[:, 0])
    points = points[~nan_indices, :3]
    print(points)
    return points


def refine_camera_lidar_old1(
    cam_id,
    lidar_id,
    lidar_pcd_path,
    image_path,
    save_path,
    intrinsic,
    cam_tf_lidar,
    distort,
    rfu_tf_lidar,
    rfu_tf_cam_ori,
):

    if not os.path.exists(save_path):
        os.makedirs(save_path)
    # point_cloud = np.loadtxt(lidar_pcd_path)
    if not os.path.exists(lidar_pcd_path):
        print(f"Lidar {lidar_pcd_path} does not exist.")
        exit()
    point_cloud = np.load(lidar_pcd_path)
    points = point_cloud

    if not os.path.exists(image_path):
        print(f"Image {image_path} does not exist.")
        exit()
    image = cv2.imread(image_path)
    #     exit()
    print(distort)
    print(image)
    print(intrinsic)
    if len(distort.reshape(-1, 1).tolist()) < 5:
        # cv2.imwrite('image.png', image)
        image = cv2.fisheye.undistortImage(
            image, intrinsic, distort, None, Knew=intrinsic
        )
        print("use fisheye")
        # cv2.imwrite('un_image.png', image)
    else:
        image = cv2.undistort(image, intrinsic, distort, None, intrinsic)
        print("use pinhole")
    points_copy = np.ones((len(points), 4))
    points_copy[:, :3] = points[:, :3]
    intensitys = np.asarray(points[:, 3])
    color = intensity2color(intensitys)
    cv2.namedWindow("show", cv2.WINDOW_NORMAL)
    cv2.createTrackbar("pitch", "show", 100, 200, nothing)
    cv2.createTrackbar("yaw", "show", 100, 200, nothing)
    cv2.createTrackbar("roll", "show", 100, 200, nothing)
    cv2.createTrackbar("x", "show", 50, 100, nothing)
    cv2.createTrackbar("y", "show", 50, 100, nothing)
    cv2.createTrackbar("z", "show", 50, 100, nothing)
    cv2.createTrackbar("max_dis", "show", 200, 600, nothing)
    cv2.createTrackbar("min_dis", "show", 0, 400, nothing)
    cv2.createTrackbar("intensity_thresh", "show", 10, 20, nothing)

    cam_tf_lidar_copy = cam_tf_lidar

    point_radius = 1
    h, w = image.shape[:2]
    if w > 2500:
        point_radius = 4
    else:
        point_radius = 2

    print(cam_tf_lidar_copy)
    while True:
        image_copy = image.copy()
        pitch = cv2.getTrackbarPos("pitch", "show")
        yaw = cv2.getTrackbarPos("yaw", "show")
        roll = cv2.getTrackbarPos("roll", "show")
        x = cv2.getTrackbarPos("x", "show")
        y = cv2.getTrackbarPos("y", "show")
        z = cv2.getTrackbarPos("z", "show")
        max_dis = cv2.getTrackbarPos("max_dis", "show")
        min_dis = cv2.getTrackbarPos("min_dis", "show")
        intensity_thresh = cv2.getTrackbarPos("intensity_thresh", "show")

        angle_step = 0.03

        max_dis = 0.5 * max_dis
        min_dis = 0.5 * min_dis
        pitch = (pitch - 100) * angle_step * math.pi / 180.0
        yaw = (yaw - 100) * angle_step * math.pi / 180.0
        roll = (roll - 100) * angle_step * math.pi / 180.0
        x = (x - 50) * 0.02
        y = (y - 50) * 0.02
        z = (z - 50) * 0.02

        delta_R = transforms3d.euler.euler2mat(pitch, yaw, roll, "sxyz")
        delta_T = np.eye(4)
        delta_T[:3, :3] = delta_R
        delta_T[0][3] = x
        delta_T[1][3] = y
        delta_T[2][3] = z
        cam_tf_lidar_copy = delta_T @ cam_tf_lidar
        # cam_tf_lidar_copy =  cam_tf_lidar @ delta_T
        proj_matrix = intrinsic @ cam_tf_lidar_copy[:3, :4]
        points_image = (proj_matrix @ points_copy.T).T
        H, W = image.shape[:2]
        for index, (pt, c, i) in enumerate(zip(points_image, color, intensitys_show)):
            if i < intensity_thresh:
                continue
            u, v, flag = pt
            u = int(u / flag)
            v = int(v / flag)
            if (
                flag > min_dis
                and flag < max_dis
                and (0 <= u and u < W)
                and (0 <= v and v < H)
            ):
                cv2.circle(image_copy, (u, v), point_radius, tuple(map(int, c)), -1)
        image_copy_show = cv2.resize(image_copy, (960, 540))
        cv2.imshow("show", image_copy_show)
        if cv2.waitKey(1) == ord("q"):
            break
    cv2.destroyWindow("show")
    R = cam_tf_lidar_copy[:3, :3]
    t = cam_tf_lidar_copy[:3, 3]
    q = transforms3d.quaternions.mat2quat(R)

    output_path = os.path.join(save_path, f"{cam_id}_tf_rfu.json")
    output_path_inv = os.path.join(save_path, f"rfu_tf_{cam_id}.json")
    output_cam_lidar_path = os.path.join(save_path, f"{cam_id}_cam_tf_lidar.json")
    output_lidar_path = os.path.join(save_path, f"rfu_tf_{lidar_id}.json")
    cam_tf_lidar = cam_tf_lidar_copy
    cam_tf_rfu = cam_tf_lidar @ np.linalg.inv(rfu_tf_lidar)
    rfu_tf_lidar_re = rfu_tf_cam_ori @ cam_tf_lidar

    # dump_result(cam_tf_rfu, f"{cam_id}_tf_rfu", output_path)
    dump_result(np.linalg.inv(cam_tf_rfu), f"rfu_tf_{cam_id}", output_path_inv)
    # dump_result(cam_tf_lidar, f"{cam_id}_tf_{lidar_id}", output_cam_lidar_path)
    dump_result(rfu_tf_lidar_re, f"rfu_tf_{lidar_id}", output_lidar_path)


def refine_camera_lidar_old2(
    cam_id,
    lidar_id,
    lidar_pcd_path,
    image_path,
    save_path,
    intrinsic,
    extrinsic,
    distort,
    rfu_tf_lidar,
    rfu_tf_cam_ori,
):

    if not os.path.exists(save_path):
        os.makedirs(save_path)

    # cloud = pcl.load_XYZI(str(lidar_pcd_path))
    # points = np.array( cloud.to_list(), dtype=np.float32)
    # points = np.loadtxt(str(lidar_pcd_path))
    points = np.load(lidar_pcd_path)
    # print(points)
    # print(cloud)
    image = cv2.imread(image_path)

    print(rfu_tf_lidar)
    print(image_path)

    cv2.namedWindow("show", cv2.WINDOW_NORMAL)
    cv2.createTrackbar("pitch", "show", 100, 200, nothing)
    cv2.createTrackbar("yaw", "show", 100, 200, nothing)
    cv2.createTrackbar("roll", "show", 100, 200, nothing)

    cv2.createTrackbar("x", "show", 50, 100, nothing)
    cv2.createTrackbar("y", "show", 50, 100, nothing)
    cv2.createTrackbar("z", "show", 50, 100, nothing)

    cv2.createTrackbar("max_dis", "show", 200, 600, nothing)
    cv2.createTrackbar("min_dis", "show", 0, 400, nothing)

    cv2.createTrackbar("intensity_thresh", "show", 10, 20, nothing)

    extrinsic_copy = extrinsic
    #  extrinsic_copy cam_tf_lidar
    points_copy = points[:, :3]
    intensitys = np.asarray(points[:, 3])

    # point_radius = 1
    point_radius = int(intrinsic[0][0] / 2433 * 3.0 + 1)
    # h, w = image.shape[:2]
    # if w > 2500:
    #     point_radius = 3
    # else:
    #     point_radius = 2

    while True:
        image_copy = image.copy()

        pitch = cv2.getTrackbarPos("pitch", "show")
        yaw = cv2.getTrackbarPos("yaw", "show")
        roll = cv2.getTrackbarPos("roll", "show")
        x = cv2.getTrackbarPos("x", "show")
        y = cv2.getTrackbarPos("y", "show")
        z = cv2.getTrackbarPos("z", "show")
        max_dis = cv2.getTrackbarPos("max_dis", "show")
        min_dis = cv2.getTrackbarPos("min_dis", "show")
        intensity_thresh = cv2.getTrackbarPos("intensity_thresh", "show")

        max_dis = 0.5 * max_dis
        min_dis = 0.5 * min_dis

        pitch = (pitch - 100) * 0.05 * math.pi / 180.0
        yaw = (yaw - 100) * 0.05 * math.pi / 180.0
        roll = (roll - 100) * 0.05 * math.pi / 180.0
        x = (x - 50) * 0.02
        y = (y - 50) * 0.02
        z = (z - 50) * 0.02

        delta_R = transforms3d.euler.euler2mat(pitch, yaw, roll, "sxyz")
        delta_T = np.eye(4)
        delta_T[:3, :3] = delta_R
        delta_T[0][3] = x
        delta_T[1][3] = y
        delta_T[2][3] = z
        extrinsic_copy = delta_T @ extrinsic  # extrinsic = cam_tf_lidar
        p = np.dot(extrinsic_copy, np.c_[points_copy, np.ones(len(points_copy))].T).T
        p = p[:, :3] / p[:, 3:4]

        maska = np.linalg.norm(p[:, :3], axis=1) > min_dis
        p = p[np.where(maska)[0]]
        intensitys_show = intensitys[np.where(maska)[0]]
        maska = np.linalg.norm(p[:, :3], axis=1) < max_dis
        p = p[np.where(maska)[0]]
        intensitys_show = intensitys_show[np.where(maska)[0]]
        maska = p[:, 2] > 0
        p = p[np.where(maska)[0]]
        intensitys_show = intensitys_show[np.where(maska)[0]]

        height, width = image_copy.shape[:2]

        # print(f"图像宽度: {width}, 图像高度: {height}")
        # print(intrinsic)

        mask_fov = abs(p[:, 0] / p[:, 2]) < (0.8 * width / intrinsic[0][0])
        p = p[np.where(mask_fov)[0]]
        intensitys_show = intensitys_show[np.where(mask_fov)[0]]
        mask_fov = abs(p[:, 1] / p[:, 2]) < (0.8 * height / intrinsic[1][1])
        p = p[np.where(mask_fov)[0]]
        intensitys_show = intensitys_show[np.where(mask_fov)[0]]

        # intensitys_show = intensitys
        color = intensity2color(intensitys_show)

        # mask_fov = ( abs(p[:,0]/ p[:,2]) < math.tan(15/57.0) )
        # p = p[np.where(mask_fov)[0]]
        # intensitys_show = intensitys[np.where(mask_fov)[0]]
        # mask_fov =  ( abs(p[:,1]/ p[:,2]) < math.tan(10/57.0)  )
        # p = p[np.where(mask_fov)[0]]
        # intensitys_show = intensitys[np.where(mask_fov)[0]]

        p = p.reshape(-1, 1, 3)
        if len(distort.reshape(-1, 1).tolist()) < 5:
            uv, _ = cv2.fisheye.projectPoints(
                p, np.zeros(3), np.zeros(3), intrinsic, distort
            )
        else:
            uv, mask_uv = cv2.projectPoints(
                p, np.zeros(3), np.zeros(3), intrinsic, distort
            )

        uv = uv[:, 0]
        for index, (x, c, i) in enumerate(zip(uv, color, intensitys_show)):
            try:
                if i < intensity_thresh:
                    continue
                # image_copy = cv2.circle(
                #     image_copy,
                #     tuple(map(int, x)),
                #     radius=point_radius,
                #     color=tuple(map(int, c)),
                #     thickness=-1)  # plot

                # 将投影点的坐标转换为整数
                x_int, y_int = map(int, x.ravel())  # 使用 ravel() 将 x 转换为一维数组

                # 检查坐标是否在图像范围内
                if (
                    0 <= x_int < image_copy.shape[1]
                    and 0 <= y_int < image_copy.shape[0]
                ):
                    image_copy = cv2.circle(
                        image_copy,
                        (x_int, y_int),
                        radius=point_radius,
                        color=tuple(map(int, c)),
                        thickness=-1,
                    )  # plot
            except:
                pass

        cv2.imshow("show", image_copy)
        if cv2.waitKey(1) == ord("q"):
            break

    cv2.destroyWindow("show")

    R = extrinsic_copy[:3, :3]  # cam_tf_lidar_copy
    t = extrinsic_copy[:3, 3]
    q = transforms3d.quaternions.mat2quat(R)

    output_path = os.path.join(save_path, f"{cam_id}_tf_rfu.json")
    output_path_inv = os.path.join(save_path, f"rfu_tf_{cam_id}.json")
    output_cam_lidar_path = os.path.join(save_path, f"{cam_id}_cam_tf_lidar.json")
    output_lidar_path = os.path.join(save_path, f"rfu_tf_{lidar_id}.json")
    cam_tf_lidar = extrinsic_copy
    cam_tf_rfu = cam_tf_lidar @ np.linalg.inv(rfu_tf_lidar)
    rfu_tf_lidar_re = rfu_tf_cam_ori @ cam_tf_lidar

    # dump_result(cam_tf_rfu, f"{cam_id}_tf_rfu", output_path)
    dump_result(np.linalg.inv(cam_tf_rfu), f"rfu_tf_{cam_id}", output_path_inv)
    # dump_result(cam_tf_lidar, f"{cam_id}_tf_{lidar_id}", output_cam_lidar_path)
    dump_result(rfu_tf_lidar_re, f"rfu_tf_{lidar_id}", output_lidar_path)


def load_intrinsic_extrinsic_old(ppl_params, lidar_id, camera_id):
    with open(ppl_params, "r") as f:
        camera_intrinsic = json.load(f)["camera_intrinsic"]
        camera_i_ = camera_intrinsic[camera_id]

        K = np.array(camera_i_["K"]).reshape(3, 3).astype(np.float32)
        D = np.array(camera_i_["D"]).reshape(1, -1).astype(np.float32)

    with open(os.path.join(ppl_params), "r") as f:
        rt = json.load(f)
        rota = rt["rfu_tf_camera"][camera_id]["transform"]["rotation"]
        tran = rt["rfu_tf_camera"][camera_id]["transform"]["translation"]

        q = np.quaternion(rota["w"], rota["x"], rota["y"], rota["z"])
        t = np.array([tran["x"], tran["y"], tran["z"]]).astype(np.float32)
        r = quaternion.as_rotation_matrix(q).astype(np.float32)
        rfu_tf_cam = np.eye(4)
        rfu_tf_cam[:3, :3] = r
        rfu_tf_cam[:3, 3] = t

    with open(os.path.join(ppl_params), "r") as f:
        rt = json.load(f)
        rota = rt["rfu_tf_lidar"][lidar_id]["transform"]["rotation"]
        tran = rt["rfu_tf_lidar"][lidar_id]["transform"]["translation"]
        q = np.quaternion(rota["w"], rota["x"], rota["y"], rota["z"])
        t = np.array([tran["x"], tran["y"], tran["z"]]).astype(np.float32)
        r = quaternion.as_rotation_matrix(q).astype(np.float32)
        rfu_tf_lidar = np.eye(4)
        rfu_tf_lidar[:3, :3] = r
        rfu_tf_lidar[:3, 3] = t

    cam_tf_lidar = np.linalg.inv(rfu_tf_cam) @ rfu_tf_lidar
    return K, D, cam_tf_lidar, rfu_tf_lidar, rfu_tf_cam


def load_binary_pcd_structured(filename):
    """解析PCD文件"""
    header = {}
    with open(filename, "rb") as f:
        while True:
            line = f.readline().decode("ascii").strip()
            if line.startswith("DATA"):
                header["data"] = line.split()[1]
                break
            if line == "":
                raise RuntimeError("Unexpected EOF while reading header")
            key, *vals = line.split()
            header[key.lower()] = vals

    fields = header["fields"]
    types = header["type"]
    sizes = list(map(int, header["size"]))
    count = list(map(int, header.get("count", ["1"] * len(fields))))
    width = int(header["width"][0])
    height = int(header["height"][0])
    data_type = header["data"]

    np_dtype = []
    for f, t, s, c in zip(fields, types, sizes, count):
        if t == "F":
            dt = {4: "<f4", 8: "<f8"}[s]
        elif t == "U":
            dt = {1: "|u1", 2: "<u2", 4: "<u4"}[s]
        elif t == "I":
            dt = {1: "|i1", 2: "<i2", 4: "<i4"}[s]
        else:
            raise RuntimeError(f"Unknown PCD type {t}")
        if c > 1:
            np_dtype.append((f, dt, (c,)))
        else:
            np_dtype.append((f, dt))

    point_count = width * height
    with open(filename, "rb") as f:
        while True:
            pos = f.tell()
            line = f.readline()
            if line.startswith(b"DATA"):
                break
        data = f.read()

    if data_type == "binary":
        points = np.frombuffer(data, dtype=np_dtype, count=point_count)
    else:
        raise RuntimeError(f"Unsupported DATA type: {data_type}")

    return points


def refine_camera_lidar(
    cam_id,
    lidar_id,
    lidar_pcd_path,
    image_path,
    save_path,
    intrinsic,
    extrinsic,
    distort,
    rfu_tf_lidar,
    rfu_tf_cam_ori,
):

    if not os.path.exists(save_path):
        os.makedirs(save_path, exist_ok=True)

    # cloud = pcl.load_XYZI(str(lidar_pcd_path))
    # points = np.array( cloud.to_list(), dtype=np.float32)
    # points = np.loadtxt(str(lidar_pcd_path))
    if lidar_pcd_path.endswith(".npy"):
        points = np.load(lidar_pcd_path)
    elif lidar_pcd_path.endswith(".pcd"):
        # cloud = o3d.io.read_point_cloud(lidar_pcd_path)
        # points = np.array(cloud.points)
        # cloud = pcl.PointCloud.PointXYZI()
        # points_with_intensity = np.zeros((cloud.size(), 4), dtype=np.float32)
        points_with_intensity = load_binary_pcd_structured(str(lidar_pcd_path))

        x = points_with_intensity["x"]
        y = points_with_intensity["y"]
        z = points_with_intensity["z"]
        intensity = points_with_intensity["intensity"]

        # 组合xyz和intensity
        points = np.column_stack([x, y, z, intensity])

    else:
        raise ValueError("Unsupported file format: {}".format(lidar_pcd_path))

    if points.shape[-1] != 4:
        points = np.stack([points["x"], points["y"], points["z"], points["i"]], axis=-1)

    # print(points)
    # print(cloud)
    image = cv2.imread(image_path)
    print("rfu_tf_cam_ori: \n", rfu_tf_cam_ori)
    print("rfu_tf_lidar: \n", rfu_tf_lidar)
    print(image_path)

    cv2.namedWindow("show", cv2.WINDOW_NORMAL)
    cv2.createTrackbar("pitch", "show", 100, 200, nothing)
    cv2.createTrackbar("yaw", "show", 100, 200, nothing)
    cv2.createTrackbar("roll", "show", 100, 200, nothing)

    cv2.createTrackbar("x", "show", 50, 100, nothing)
    cv2.createTrackbar("y", "show", 50, 100, nothing)
    cv2.createTrackbar("z", "show", 50, 100, nothing)

    cv2.createTrackbar("max_dis", "show", 200, 600, nothing)
    cv2.createTrackbar("min_dis", "show", 0, 400, nothing)

    cv2.createTrackbar("intensity_thresh", "show", 10, 100, nothing)

    extrinsic_copy = extrinsic
    #  extrinsic_copy cam_tf_lidar
    points_copy = points[:, :3]
    intensitys = np.asarray(points[:, 3])

    # point_radius = 1
    point_radius = int(intrinsic[0][0] / 2433 * 3.0 + 1)
    # h, w = image.shape[:2]
    # if w > 2500:
    #     point_radius = 3
    # else:
    #     point_radius = 2

    image_copy = None
    while True:
        image_copy = image.copy()

        pitch = cv2.getTrackbarPos("pitch", "show")
        yaw = cv2.getTrackbarPos("yaw", "show")
        roll = cv2.getTrackbarPos("roll", "show")
        x = cv2.getTrackbarPos("x", "show")
        y = cv2.getTrackbarPos("y", "show")
        z = cv2.getTrackbarPos("z", "show")
        max_dis = cv2.getTrackbarPos("max_dis", "show")
        min_dis = cv2.getTrackbarPos("min_dis", "show")
        intensity_thresh = cv2.getTrackbarPos("intensity_thresh", "show")

        max_dis = 0.5 * max_dis
        min_dis = 0.5 * min_dis

        pitch = (pitch - 100) * 0.05 * math.pi / 180.0
        yaw = (yaw - 100) * 0.05 * math.pi / 180.0
        roll = (roll - 100) * 0.05 * math.pi / 180.0
        x = (x - 50) * 0.02
        y = (y - 50) * 0.02
        z = (z - 50) * 0.02

        delta_R = transforms3d.euler.euler2mat(pitch, yaw, roll, "sxyz")
        delta_T = np.eye(4)
        delta_T[:3, :3] = delta_R
        delta_T[0][3] = x
        delta_T[1][3] = y
        delta_T[2][3] = z
        extrinsic_copy = delta_T @ extrinsic  # extrinsic = cam_tf_lidar
        p = np.dot(extrinsic_copy, np.c_[points_copy, np.ones(len(points_copy))].T).T
        p = p[:, :3] / p[:, 3:4]

        maska = np.linalg.norm(p[:, :3], axis=1) > min_dis
        p = p[np.where(maska)[0]]
        intensitys_show = intensitys[np.where(maska)[0]]
        maska = np.linalg.norm(p[:, :3], axis=1) < max_dis
        p = p[np.where(maska)[0]]
        intensitys_show = intensitys_show[np.where(maska)[0]]
        maska = p[:, 2] > 0
        p = p[np.where(maska)[0]]
        intensitys_show = intensitys_show[np.where(maska)[0]]

        height, width = image_copy.shape[:2]

        # print(f"图像宽度: {width}, 图像高度: {height}")
        # print(intrinsic)

        mask_fov = abs(p[:, 0] / p[:, 2]) < (0.8 * width / intrinsic[0][0])
        p = p[np.where(mask_fov)[0]]
        intensitys_show = intensitys_show[np.where(mask_fov)[0]]
        mask_fov = abs(p[:, 1] / p[:, 2]) < (0.8 * height / intrinsic[1][1])
        p = p[np.where(mask_fov)[0]]
        intensitys_show = intensitys_show[np.where(mask_fov)[0]]

        # intensitys_show = intensitys
        color = intensity2color(intensitys_show)

        if len(p) == 0:
            cv2.imshow("show", image_copy)
            if cv2.waitKey(1) == ord("q"):
                cv2.imwrite(os.path.join(save_path, "lidar_tf_camera.png"), image_copy)
                break
            continue
        # mask_fov = ( abs(p[:,0]/ p[:,2]) < math.tan(15/57.0) )
        # p = p[np.where(mask_fov)[0]]
        # intensitys_show = intensitys[np.where(mask_fov)[0]]
        # mask_fov =  ( abs(p[:,1]/ p[:,2]) < math.tan(10/57.0)  )
        # p = p[np.where(mask_fov)[0]]
        # intensitys_show = intensitys[np.where(mask_fov)[0]]

        p = p.reshape(-1, 1, 3)
        if len(distort.reshape(-1, 1).tolist()) < 5:
            uv, _ = cv2.fisheye.projectPoints(
                p, np.zeros(3), np.zeros(3), intrinsic, distort
            )
        else:
            uv, mask_uv = cv2.projectPoints(
                p, np.zeros(3), np.zeros(3), intrinsic, distort
            )

        uv = uv[:, 0]
        for index, (x, c, i) in enumerate(zip(uv, color, intensitys_show)):
            try:
                if i < intensity_thresh:
                    continue
                # image_copy = cv2.circle(
                #     image_copy,
                #     tuple(map(int, x)),
                #     radius=point_radius,
                #     color=tuple(map(int, c)),
                #     thickness=-1)  # plot

                # 将投影点的坐标转换为整数
                x_int, y_int = map(int, x.ravel())  # 使用 ravel() 将 x 转换为一维数组

                # 检查坐标是否在图像范围内
                if (
                    0 <= x_int < image_copy.shape[1]
                    and 0 <= y_int < image_copy.shape[0]
                ):
                    image_copy = cv2.circle(
                        image_copy,
                        (x_int, y_int),
                        radius=point_radius,
                        color=tuple(map(int, c)),
                        thickness=-1,
                    )  # plot
            except:
                pass

        cv2.imshow("show", image_copy)
        if cv2.waitKey(1) == ord("q"):
            cv2.imwrite(os.path.join(save_path, "lidar_tf_camera.png"), image_copy)
            break

    cv2.destroyWindow("show")

    R = extrinsic_copy[:3, :3]  # cam_tf_lidar_copy
    t = extrinsic_copy[:3, 3]
    q = transforms3d.quaternions.mat2quat(R)

    output_path = os.path.join(save_path, f"{cam_id}_tf_rfu.json")
    output_path_inv = os.path.join(save_path, f"rfu_tf_{cam_id}.json")
    output_cam_lidar_path = os.path.join(save_path, f"{cam_id}_cam_tf_lidar.json")
    output_lidar_path = os.path.join(save_path, f"rfu_tf_{lidar_id}.json")
    cam_tf_lidar = extrinsic_copy
    cam_tf_rfu = cam_tf_lidar @ np.linalg.inv(rfu_tf_lidar)
    rfu_tf_lidar_re = rfu_tf_cam_ori @ cam_tf_lidar

    # dump_result(cam_tf_rfu, f"{cam_id}_tf_rfu", output_path)
    dump_result(np.linalg.inv(cam_tf_rfu), f"rfu_tf_{cam_id}", output_path_inv)
    # dump_result(cam_tf_lidar, f"{cam_id}_tf_{lidar_id}", output_cam_lidar_path)
    dump_result(rfu_tf_lidar_re, f"rfu_tf_{lidar_id}", output_lidar_path)


def load_intrinsic_extrinsic(
    camera_calib_result_path,
    lidar_calib_result_path,
    camera_intrisic_path,
    lidar_id,
    camera_id,
):

    if lidar_id == "front_2_lidar":
        lidar_id = "lidar"
    # read yaml
    with open(camera_calib_result_path, "r") as f:
        cam_eol_calib_data = yaml.load(f, Loader=yaml.FullLoader)

    with open(lidar_calib_result_path, "r") as f:
        lidar_eol_calib_data = yaml.load(f, Loader=yaml.FullLoader)

    eol_calib_data = {}
    eol_calib_data.update(cam_eol_calib_data)
    eol_calib_data.update(lidar_eol_calib_data)

    with open(camera_intrisic_path, "r") as f:
        camera_intrinsic = yaml.load(f, Loader=yaml.FullLoader)
    # get camera intrinsic

    this_cam_intrinsic = camera_intrinsic[camera_id]
    this_cam_extrinsic = eol_calib_data[camera_id]
    if lidar_id is not None:
        lidar_extrinsic = eol_calib_data[lidar_id]
    else:
        lidar_extrinsic = None

    # camera intrinsic
    focal_len_x = this_cam_intrinsic["focal_len_x"]
    focal_len_y = this_cam_intrinsic["focal_len_y"]
    optical_center_x = this_cam_intrinsic["optical_center_x"]
    optical_center_y = this_cam_intrinsic["optical_center_y"]
    K = np.array(
        [
            [focal_len_x, 0, optical_center_x],
            [0, focal_len_y, optical_center_y],
            [0, 0, 1],
        ],
        dtype=np.float32,
    )

    if this_cam_intrinsic["distortion_type"] == 1:
        distortion_model = "FISHEYE"
    elif this_cam_intrinsic["distortion_type"] == 3:
        distortion_model = "PINHOLE"
    else:
        raise ValueError("Unknown distortion model")

    if distortion_model == "FISHEYE":  # FISHEYE
        D = np.array(
            [
                this_cam_intrinsic["k_1"],
                this_cam_intrinsic["k_2"],
                this_cam_intrinsic["k_3"],
                this_cam_intrinsic["k_4"],
            ],
            dtype=np.float32,
        ).reshape(1, -1)
    elif distortion_model == "PINHOLE":
        D = np.array(
            [
                this_cam_intrinsic["k_1"],
                this_cam_intrinsic["k_2"],
                this_cam_intrinsic["p_1"],
                this_cam_intrinsic["p_2"],
                this_cam_intrinsic["k_3"] if "k_3" in this_cam_intrinsic else 0.0,
                this_cam_intrinsic["k_4"] if "k_4" in this_cam_intrinsic else 0.0,
                this_cam_intrinsic["k_5"] if "k_5" in this_cam_intrinsic else 0.0,
                this_cam_intrinsic["k_6"] if "k_6" in this_cam_intrinsic else 0.0,
            ],
            dtype=np.float32,
        ).reshape(1, -1)

    # camera extrinsic
    q = np.quaternion(
        this_cam_extrinsic["rotation"]["w"],
        this_cam_extrinsic["rotation"]["x"],
        this_cam_extrinsic["rotation"]["y"],
        this_cam_extrinsic["rotation"]["z"],
    )
    t = np.array(
        [
            this_cam_extrinsic["translation"]["x"],
            this_cam_extrinsic["translation"]["y"],
            this_cam_extrinsic["translation"]["z"],
        ]
    ).astype(np.float32)
    r = quaternion.as_rotation_matrix(q).astype(np.float32)

    cam_tf_rfu = np.eye(4)
    cam_tf_rfu[:3, :3] = r
    cam_tf_rfu[:3, 3] = t

    rfu_tf_cam = np.linalg.inv(cam_tf_rfu)

    # lidar extrinsic
    if lidar_extrinsic is None:
        return K, D, distortion_model, None, None, rfu_tf_cam

    q = np.quaternion(
        lidar_extrinsic["rotation"]["w"],
        lidar_extrinsic["rotation"]["x"],
        lidar_extrinsic["rotation"]["y"],
        lidar_extrinsic["rotation"]["z"],
    )
    t = np.array(
        [
            lidar_extrinsic["translation"]["x"],
            lidar_extrinsic["translation"]["y"],
            lidar_extrinsic["translation"]["z"],
        ]
    ).astype(np.float32)
    r = quaternion.as_rotation_matrix(q).astype(np.float32)

    rfu_tf_lidar = np.eye(4)
    rfu_tf_lidar[:3, :3] = r
    rfu_tf_lidar[:3, 3] = t

    lidar_tf_rfu = np.linalg.inv(rfu_tf_lidar)

    cam_tf_lidar = np.linalg.inv(rfu_tf_cam) @ rfu_tf_lidar
    return K, D, distortion_model, cam_tf_lidar, rfu_tf_lidar, rfu_tf_cam


def main():
    parser = argparse.ArgumentParser()
    #     parser.add_argument("--save_path", "-s", default="", help="save path")
    #     parser.add_argument("--lidar_id", default="", help="lidar id")
    #     parser.add_argument("--cam_id", default="", help="camera id")
    #     parser.add_argument("--param_path", "-c", default="", help="param path")
    #     parser.add_argument("--data_path", "-c", default="", help="data path")
    args = parser.parse_args()
    ############# 根据自己的需求修改路径 ######################
    # path = "/media/ws/using_data/megvii_data/lidar_to_cam/z16_test_1202"
    # Z10 data
    # cam_id2cam_path = {
    #     "cam_back_70": "data/Z10/geely_calibroom_image/cam_back_70.jpg",
    #     "cam_back_left_100": "data/Z10/geely_calibroom_image/cam_back_left_100.jpg",
    #     "cam_back_right_100": "data/Z10/geely_calibroom_image/cam_back_right_100.jpg",
    #     "cam_front_30": "data/Z10/geely_calibroom_image/cam_front_30.jpg",
    #     "cam_front_120": "data/Z10/geely_calibroom_image/cam_front_120.jpg",
    #     "cam_front_left_100": "data/Z10/geely_calibroom_image/cam_front_left_100.jpg",
    #     "cam_front_right_100": "data/Z10/geely_calibroom_image/cam_front_right_100.jpg",
    # }

    # lidar_id2lidar_path = {
    #     "front_lidar": "data/Z10/geely_calibroom_lidar/front_lidar.npy",
    #     "middle_lidar": "data/Z10/geely_calibroom_lidar/middle_lidar.npy",
    #     "front_2_lidar": "data/Z10/geely_calibroom_lidar/front_2_lidar.npy",
    # }
    # cam_calib_result = "data/Z10/cam_lidar_eol_calibration_output.yaml"
    # camera_intrisic_path = "data/Z10/soca_cam_intrisics.yaml"

    # # P177 data

    # cam_id2cam_path = {
    #     "cam_back_100": "data/P177/P1023_geely_calibroom_2025.05.25/images/cam_back_100.png",
    #     "cam_back_left_100": "data/P177/P1023_geely_calibroom_2025.05.25/images/cam_back_left_100.png",
    #     "cam_back_right_100": "data/P177/P1023_geely_calibroom_2025.05.25/images/cam_back_right_100.png",
    #     "cam_front_30": "data/P177/P1023_geely_calibroom_2025.05.25/images/cam_front_30.png",
    #     "cam_front_120": "data/P177/P1023_geely_calibroom_2025.05.25/images/cam_front_120.png",
    #     "cam_front_left_100": "data/P177/P1023_geely_calibroom_2025.05.25/images/cam_front_left_100.png",
    #     "cam_front_right_100": "data/P177/P1023_geely_calibroom_2025.05.25/images/cam_front_right_100.png",
    # }
    # lidar_id2lidar_path = {
    #     # "middle_lidar": "data/P177/geely_calibroom_lidars_2025.04.27/middle_lidar.npy",
    #     "front_2_lidar": "data/P177/P1023_geely_calibroom_2025.05.25/lidars/front_2_lidar.pcd",
    # }

    cam_id2img_path = {}
    lidar_id2lidar_path = {}

    args.lidar_id = "front_2_lidar"
    # args.lidar_id = "middle_lidar"
    args.cam_id = "cam_front_120"
    # args.cam_id = "cam_front_30"

    cam_img_dir = "data/P177/P1023_geely_calibroom_2025.05.25/images"
    front_2_lidar_path = (
        "data/P177/P1023_geely_calibroom_2025.05.25/lidars/front_2_lidar.pcd"
    )

    camera_intrisic_path = (
        "data/P177/P1023_geely_calibroom_2025.05.25/soca_cam_intrinsics.yaml"
    )
    cam_calib_result_path = (
        "data/P177/P1023_geely_calibroom_2025.05.25/driving_camera_calibresult.yaml"
    )

    lidar_calib_result_path = (
        "data/P177/P1023_geely_calibroom_2025.05.25/driving_lidar_calibresult.yaml"
    )

    cam_names = [
        "cam_back_100",
        "cam_back_left_100",
        "cam_back_right_100",
        "cam_front_30",
        "cam_front_120",
        "cam_front_left_100",
        "cam_front_right_100",
    ]
    for cam_name in cam_names:
        img_path = os.path.join(cam_img_dir, cam_name + ".png")
        if not os.path.exists(img_path):
            img_path = os.path.join(cam_img_dir, cam_name + ".jpg")

        if not os.path.exists(img_path):
            print(f"Image path {img_path} does not exist.")
            exit
        cam_id2img_path[cam_name] = img_path

    lidar_id2lidar_path = {
        # "middle_lidar": "data/P177/geely_calibroom_lidars_2025.04.27/middle_lidar.npy",
        "front_2_lidar": front_2_lidar_path,
    }
    args.save_path = "./build/output/lidar_camera_consistency_varify"
    ############# 根据自己的需求修改路径 ######################

    lidar_path = lidar_id2lidar_path[args.lidar_id]
    image_path = cam_id2img_path[args.cam_id]

    intrinsic, distort, distort_model, cam_tf_lidar, rfu_tf_lidar, rfu_tf_cam = (
        load_intrinsic_extrinsic(
            cam_calib_result_path,
            lidar_calib_result_path,
            camera_intrisic_path,
            args.lidar_id,
            args.cam_id,
        )
    )

    refine_camera_lidar(
        args.cam_id,
        args.lidar_id,
        lidar_path,
        image_path,
        args.save_path,
        intrinsic,
        cam_tf_lidar,
        distort,
        rfu_tf_lidar,
        rfu_tf_cam,
    )


if __name__ == "__main__":
    main()
