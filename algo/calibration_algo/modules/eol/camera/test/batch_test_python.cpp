
#include "calibration_algo/eol/camera/camera_eol_calibration.h"
#include "calibration_algo/eol/camera/utils/common_utils.hpp"

using namespace calibration_algo::common;
using namespace calibration_algo::eol::camera;

int main(int argc, char** argv) {
  // GLOG
  google::InitGoogleLogging(argv[0]);
  FLAGS_log_dir = "./log";
  if (!std::filesystem::exists(FLAGS_log_dir)) {
    std::filesystem::create_directories(FLAGS_log_dir);
  }

  FLAGS_alsologtostderr = true;
  FLAGS_stop_logging_if_full_disk = true;

  CameraEOLCalibrationPtr camera_eol_calibration_ptr(new CameraEOLCalibration);

  std::map<std::string, CameraIntrinsicParam> cameras_intrinsic;
  std::map<std::string, ExtrinsicPose> cameras_structure;
  std::map<std::string, Point> calibroom_points_map;
  EOLParams eol_config;
  bool is_calib_success = true;
  std::vector<std::pair<std::string, std::string>> image_files;
  std::string output_yaml_path;

  // // P177
  std::string eol_config_path = "../config/P177/eol_config.yaml";
  std::string intrinsic_path = "../data/P177/soca_cam_intrisics.yaml";
  std::string extrinsic_config_path = "../config/P177/cam_extrinsics.yaml";
  std::string calibroom_points_path = "../data/P177/geely_calibroom_points_map.txt";

  // P177 7V images: 2025.04.09
  // std::string img_dir = "../data/P177/geely_calibroom_images_2025.04.09";
  std::string img_dir = "../data/P177/geely_calibroom_images_2025.04.17";

  if (argc > 1) {
    eol_config_path = argv[1];
    LOG(INFO) << "eol_config_path: " << eol_config_path << std::endl;
  } else {
    LOG(INFO) << "No eol_config_path provided! Default as: " << eol_config_path << std::endl;
  }

  if (argc > 2) {
    img_dir = argv[2];
    LOG(INFO) << "img_dir: " << img_dir << std::endl;
  } else {
    LOG(INFO) << "No img_dir provided! Default as: " << img_dir << std::endl;
  }

  if (argc > 3) {
    output_yaml_path = argv[3];
    LOG(INFO) << "output_yaml_path: " << output_yaml_path << std::endl;
  } else {
    LOG(INFO) << "No output_yaml_path provided! Default as: " << output_yaml_path << std::endl;
  }

  if (argc > 4) {
    intrinsic_path = argv[4];
    LOG(INFO) << "intrinsic_path: " << intrinsic_path << std::endl;
  } else {
    LOG(INFO) << "No intrinsic_path provided! Default as: " << intrinsic_path << std::endl;
  }

  // load eol_config
  get_config_from_yaml(eol_config_path, eol_config);

  // get image_files
  get_image_files_from_dir(img_dir, eol_config, image_files);

  // intrinsic
  get_cameras_intrinsic_from_yaml(intrinsic_path, eol_config, cameras_intrinsic);
  // get_cameras_intrinsic_from_default(cameras_intrinsic);

  // structure extrinsic
  get_cameras_extrinsic_from_yaml(cameras_structure, eol_config, extrinsic_config_path);

  // calibroom points
  load_calibroom_points(calibroom_points_path, calibroom_points_map);

  CameraEOLCalibration::CalibrationParam param(SensorProductionCalibration::CalibrationType::CAMERA_EOL_CALIBRATION,
                                               FLAGS_log_dir, cameras_intrinsic, cameras_structure, eol_config,
                                               calibroom_points_map);

  camera_eol_calibration_ptr->Init(param);

  std::map<std::string, CameraEOLCalibration::SensorInput> sensors_input;
  std::string batch_num = "";

  for (const auto& image_file : image_files) {
    cv::Mat image = cv::imread(image_file.second);
    CameraEOLCalibration::SensorInput sensor_input(SensorProductionCalibration::CalibrationType::CAMERA_EOL_CALIBRATION,
                                                   0, image);
    sensors_input.insert(std::make_pair(image_file.first, sensor_input));
  }

  std::map<std::string, CalibrationResult> calibration_results;
  camera_eol_calibration_ptr->RunCalibration(sensors_input, calibration_results);

  LOG(INFO) << "===================== Camera Calibration Finished! =====================" << std::endl;
  LOG(INFO) << "Camera calibration results:";
  for (const auto& result : calibration_results) {
    // ZYX order: yaw, pitch, roll
    Eigen::Quaterniond rfu_tf_cam_q =
        Eigen::Quaterniond(result.second.calib_extrinsic.orientation.w, result.second.calib_extrinsic.orientation.x,
                           result.second.calib_extrinsic.orientation.y, result.second.calib_extrinsic.orientation.z);
    Eigen::Vector3d calib_rpy = rfu_tf_cam_q.toRotationMatrix().eulerAngles(2, 1, 0);
    LOG(INFO) << "  " << result.first << ": ";
    LOG(INFO) << "    calib_status       : " << static_cast<uint32_t>(result.second.calib_status) << std::endl;
    LOG(INFO) << "    error_code         : " << static_cast<uint32_t>(result.second.calib_error_code) << std::endl;
    LOG(INFO) << "    reproj_error       : "
              << std::get<double>(result.second.custom_fields.at("camera_calib_reproj_error")) << std::endl;
    LOG(INFO) << "    POSITION           : " << result.second.calib_extrinsic.position.x << " "
              << result.second.calib_extrinsic.position.y << " " << result.second.calib_extrinsic.position.z
              << std::endl;
    LOG(INFO) << "    ORIENTATION        : " << result.second.calib_extrinsic.orientation.w << " "
              << result.second.calib_extrinsic.orientation.x << " " << result.second.calib_extrinsic.orientation.y
              << " " << result.second.calib_extrinsic.orientation.z << std::endl;
    LOG(INFO) << "    ROTATION ANGLE(xyz): " << calib_rpy.transpose()
              << std::endl;


    if (static_cast<uint32_t>(result.second.calib_status) != 0 ||
        static_cast<uint32_t>(result.second.calib_error_code) != 0) {
      is_calib_success = false;
    }
  }

  // Save calibration results to YAML
  std::map<std::string, std::string> mach2geely_camera_mappings;
  for (const auto& mapping : eol_config.geely2mach_camera_mappings) {
    mach2geely_camera_mappings.insert(std::make_pair(mapping.second, mapping.first));
  }

  if (output_yaml_path.empty()) {
    output_yaml_path = eol_config.tmp_output_dir + "/calibration_output.yaml";
  }
  // Ensure the output directory exists
  std::filesystem::path tmp_output_dir = std::filesystem::path(output_yaml_path).parent_path();
  if (!std::filesystem::exists(tmp_output_dir)) {
    std::filesystem::create_directories(tmp_output_dir);
  }

  cv::FileStorage fs(output_yaml_path, cv::FileStorage::WRITE);

  fs << "VIN"
     << "xxx";  // Replace "xxx" with the actual VIN if available
  fs << "software_version"
     << "xxx";  // calibration software version

  for (const auto& result : calibration_results) {
    fs << mach2geely_camera_mappings[result.first] << "{";

    fs << "calib_errcode" << static_cast<int>(result.second.calib_error_code);
    fs << "calib_mode" << static_cast<int>(SensorProductionCalibration::CalibrationType::CAMERA_EOL_CALIBRATION);
    fs << "calib_status" << static_cast<int>(result.second.calib_status);
    fs << "calib_time"
       << "1900-01-01 00:00:00";  // Replace with actual calibration time if available

    // calib_valid
    // Indicates whether the camera has undergone changes, such as installation position or camera replacement.
    // For EOL Calibration, the default output is 1. If the error code is non-zero, this value should be set to 0.
    fs << "calib_valid" << (static_cast<int>(result.second.calib_error_code) == 0 ? 1 : 0);

    fs << "custom_fields"
       << "{";
    fs << "camera_calib_reproj_error" << std::get<double>(result.second.custom_fields.at("camera_calib_reproj_error"));
    fs << "}";

    Eigen::Quaterniond rfu_tf_cam_q(
        result.second.calib_extrinsic.orientation.w, result.second.calib_extrinsic.orientation.x,
        result.second.calib_extrinsic.orientation.y, result.second.calib_extrinsic.orientation.z);
    Eigen::Vector3d calib_rpy = rfu_tf_cam_q.toRotationMatrix().eulerAngles(2, 1, 0);  // ZYX order

    fs << "euler_degree"
       << "{";
    fs << "RotX" << calib_rpy[2] / M_PI * 180;
    fs << "RotY" << calib_rpy[1] / M_PI * 180;
    fs << "RotZ" << calib_rpy[0] / M_PI * 180;
    fs << "}";

    fs << "information" << result.first + "_tf_rfu";  // Concatenate camera name with "_tf_rfu"

    fs << "rotation"
       << "{";
    fs << "w" << result.second.calib_extrinsic.orientation.w;
    fs << "x" << result.second.calib_extrinsic.orientation.x;
    fs << "y" << result.second.calib_extrinsic.orientation.y;
    fs << "z" << result.second.calib_extrinsic.orientation.z;
    fs << "}";

    fs << "translation"
       << "{";
    fs << "x" << result.second.calib_extrinsic.position.x;
    fs << "y" << result.second.calib_extrinsic.position.y;
    fs << "z" << result.second.calib_extrinsic.position.z;
    fs << "}";

    fs << "}";
  }

  fs.release();

  google::ShutdownGoogleLogging();
  if (is_calib_success) {
    std::cout << std::endl << "Camera Calibration Completed Successfully!" << std::endl;
    return 0;
  } else {
    std::cout << std::endl << "Camera Calibration Failed!" << std::endl;
    return 1;
  }
}
