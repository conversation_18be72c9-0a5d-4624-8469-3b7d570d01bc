
#include "calibration_algo/eol/camera/camera_eol_calibration.h"
#include "calibration_algo/eol/camera/utils/common_utils.hpp"

using namespace calibration_algo::common;
using namespace calibration_algo::eol::camera;

int main(int argc, char** argv) {
  // GLOG
  google::InitGoogleLogging(argv[0]);
  FLAGS_log_dir = "./log";
  if (!std::filesystem::exists(FLAGS_log_dir)) {
    std::filesystem::create_directories(FLAGS_log_dir);
  }

  FLAGS_alsologtostderr = true;
  FLAGS_stop_logging_if_full_disk = true;

  CameraEOLCalibrationPtr camera_eol_calibration_ptr(new CameraEOLCalibration);

  std::map<std::string, CameraIntrinsicParam> cameras_intrinsic;
  std::map<std::string, ExtrinsicPose> cameras_structure;
  std::map<std::string, Point> calibroom_points_map;
  EOLParams eol_config;
  bool tmp_is_simulation_error = false;
  bool is_calib_success = true;
  std::vector<std::pair<std::string, std::string>> image_files;

  std::map<std::string, std::map<std::string, std::vector<double>>> batch_test_result;

  // // P177
  // std::string eol_config_path = "../config/P177/eol_config.yaml";
  std::string intrinsic_path = "../data/P177/soca_cam_intrisics.yaml";
  std::string extrinsic_config_path = "../config/P177/cam_extrinsics.yaml";
  std::string calibroom_points_path = "../data/P177/geely_calibroom_points_map.txt";

  // test_img_dir.txt
  std::string test_img_dir_txt_path =
      "/data/production_calibraton/build/output_batch_test/multi-threaded_test/error_data.txt";

  std::string eol_config_path = "./output_batch_test/multi-threaded_test/tmp_eol_config.yaml";
  std::string output_dir = "./output_batch_test/multi-threaded_test";
  if (!std::filesystem::exists(output_dir)) {
    std::filesystem::create_directories(output_dir);
  }

  std::vector<std::string> all_img_dir_lst;
  std::ifstream test_img_dir_file(test_img_dir_txt_path);
  if (test_img_dir_file.is_open()) {
    std::string line;
    while (std::getline(test_img_dir_file, line)) {
      if (!line.empty()) {
        all_img_dir_lst.push_back(line);
      }
    }
    test_img_dir_file.close();
  } else {
    LOG(ERROR) << "Failed to open test_img_dir.txt file: " << test_img_dir_txt_path;
  }

  // load eol_config
  get_config_from_yaml(eol_config_path, eol_config);

  // intrinsic
  get_cameras_intrinsic_from_yaml(intrinsic_path, eol_config, cameras_intrinsic);

  // structure extrinsic
  get_cameras_extrinsic_from_yaml(cameras_structure, eol_config, extrinsic_config_path);

  // calibroom points
  load_calibroom_points(calibroom_points_path, calibroom_points_map);

  // init batch_test_result
  for (const auto& item : eol_config.geely2mach_camera_mappings) {
    std::string cam_name = item.second;
    std::map<std::string, std::vector<double>> tmp;
    for (int i = 0;
         i <= static_cast<int>(CalibrationErrorCode::EOL_CAMERA_EOL_CAMERA_COMMON_INTERNAL_CALIBRATION_ERROR); ++i) {
      tmp.insert(std::make_pair(std::to_string(i), std::vector<double>()));
    }
    batch_test_result.insert(std::make_pair(cam_name, tmp));
  }

  int test_num = 0;
  for (const auto& img_dir : all_img_dir_lst) {
    // get image_files
    get_image_files_from_dir(img_dir, eol_config, image_files);

    tmp_is_simulation_error = eol_config.is_simulation_error;
    eol_config.is_simulation_error = false;
    CameraEOLCalibration::CalibrationParam param(SensorProductionCalibration::CalibrationType::CAMERA_EOL_CALIBRATION,
                                                 FLAGS_log_dir, cameras_intrinsic, cameras_structure, eol_config,
                                                 calibroom_points_map);

    camera_eol_calibration_ptr->Init(param);

    std::map<std::string, CameraEOLCalibration::SensorInput> sensors_input;
    std::string batch_num = "";

    for (const auto& image_file : image_files) {
      cv::Mat image = cv::imread(image_file.second);
      CameraEOLCalibration::SensorInput sensor_input(
          SensorProductionCalibration::CalibrationType::CAMERA_EOL_CALIBRATION, 0, image);
      sensors_input.insert(std::make_pair(image_file.first, sensor_input));
    }

    std::map<std::string, CalibrationResult> calibration_results;
    camera_eol_calibration_ptr->RunCalibration(sensors_input, calibration_results);

    LOG(INFO) << "===================== Camera Calibration Finished! =====================" << std::endl;
    LOG(INFO) << "Camera calibration results:";
    for (const auto& result : calibration_results) {
      // ZYX order: yaw, pitch, roll
      Eigen::Quaterniond rfu_tf_cam_q =
          Eigen::Quaterniond(result.second.calib_extrinsic.orientation.w, result.second.calib_extrinsic.orientation.x,
                             result.second.calib_extrinsic.orientation.y, result.second.calib_extrinsic.orientation.z);
      Eigen::Vector3d calib_rpy = rfu_tf_cam_q.toRotationMatrix().eulerAngles(2, 1, 0);
      LOG(INFO) << "  " << result.first << ": ";
      LOG(INFO) << "    calib_status       : " << static_cast<uint32_t>(result.second.calib_status) << std::endl;
      LOG(INFO) << "    error_code         : " << static_cast<uint32_t>(result.second.calib_error_code) << std::endl;
      LOG(INFO) << "    reproj_error       : "
                << std::get<double>(result.second.custom_fields.at("camera_calib_reproj_error")) << std::endl;
      LOG(INFO) << "    POSITION           : " << result.second.calib_extrinsic.position.x << " "
                << result.second.calib_extrinsic.position.y << " " << result.second.calib_extrinsic.position.z
                << std::endl;
      LOG(INFO) << "    ORIENTATION        : " << result.second.calib_extrinsic.orientation.w << " "
                << result.second.calib_extrinsic.orientation.x << " " << result.second.calib_extrinsic.orientation.y
                << " " << result.second.calib_extrinsic.orientation.z << std::endl;
      LOG(INFO) << "    ROTATION ANGLE(ZYX): " << calib_rpy.transpose()
                << std::endl;

      if (static_cast<uint32_t>(result.second.calib_status) != 0 ||
          static_cast<uint32_t>(result.second.calib_error_code) != 0) {
        is_calib_success = false;
      }

      int tmp_error_code = static_cast<uint32_t>(result.second.calib_error_code);
      if (tmp_error_code == 0) {
        batch_test_result[result.first][std::to_string(tmp_error_code)].push_back(1.0);
        continue;
      } else if (tmp_error_code == 10) {
        batch_test_result[result.first][std::to_string(tmp_error_code)].push_back(
            std::get<double>(result.second.custom_fields.at("camera_calib_reproj_error")));
      } else {
        batch_test_result[result.first][std::to_string(tmp_error_code)].push_back(1.0);
      }
    }
    test_num += 1;
    // if (test_num >= 10) {
    //   break;
    // }
  }
  LOG(INFO) << std::endl;
  LOG(INFO) << "===================== Batch Test Finished! =====================";
  LOG(INFO) << std::endl;

  // Output batch_test_result
  std::string output_data = "";
  for (const auto& camera_result : batch_test_result) {
    LOG(INFO) << "Camera: " << camera_result.first;
    output_data += "Camera: " + camera_result.first + "\n";
    for (const auto& error_code_result : camera_result.second) {
      if (error_code_result.first == "10") {
        if (!error_code_result.second.empty()) {
          double mean_error = std::accumulate(error_code_result.second.begin(), error_code_result.second.end(), 0.0) /
                              error_code_result.second.size();
          double max_error = *std::max_element(error_code_result.second.begin(), error_code_result.second.end());
          LOG(INFO) << "  Error Code: " << error_code_result.first << " (Mean Reprojection Error: " << mean_error
                    << ", Max Reprojection Error: " << max_error << ")";
          output_data += "  Error Code: " + error_code_result.first +
                         " (Mean Reprojection Error: " + std::to_string(mean_error) +
                         ", Max Reprojection Error: " + std::to_string(max_error) + ")\n";
        }
      } else {
        if (!error_code_result.second.empty()) {
          LOG(INFO) << "  Error Code: " << error_code_result.first << " (Count: " << error_code_result.second.size()
                    << ")";
          output_data += "  Error Code: " + error_code_result.first +
                         " (Count: " + std::to_string(error_code_result.second.size()) + ")\n";
        }
      }
    }
  }

  // Write output_data to a file
  std::string output_file_path = output_dir + "/batch_test_result.txt";
  std::ofstream output_file(output_file_path);
  if (output_file.is_open()) {
    output_file << output_data;
    output_file.close();
    LOG(INFO) << "Batch test results written to: " << output_file_path;
  } else {
    LOG(ERROR) << "Failed to write batch test results to file: " << output_file_path;
  }

  google::ShutdownGoogleLogging();
  if (is_calib_success) {
    return 0;
  } else {
    return 1;
  }
}