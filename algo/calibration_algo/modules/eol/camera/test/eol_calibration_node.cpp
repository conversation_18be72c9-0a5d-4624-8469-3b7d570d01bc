#include <yaml-cpp/yaml.h>

#include <filesystem>

#include "calibration_algo/eol/camera/camera_eol_calibration.h"
#include "calibration_algo/eol/camera/utils/common_utils.hpp"
using namespace calibration_algo::common;
using namespace calibration_algo::eol::camera;

int main(int argc, char** argv) {
  // GLOG
  google::InitGoogleLogging(argv[0]);
  FLAGS_log_dir = "./log";
  if (!std::filesystem::exists(FLAGS_log_dir)) {
    std::filesystem::create_directories(FLAGS_log_dir);
  }

  FLAGS_alsologtostderr = true;
  FLAGS_stop_logging_if_full_disk = true;

  CameraEOLCalibrationPtr camera_eol_calibration_ptr(new CameraEOLCalibration);

  std::map<std::string, CameraIntrinsicParam> cameras_intrinsic;
  std::map<std::string, ExtrinsicPose> cameras_structure;
  std::map<std::string, Point> calibroom_points_map;
  EOLParams eol_config;
  bool is_calib_success = true;

  std::vector<std::pair<std::string, std::string>> image_files;

  // // P177
  std::string eol_config_path = "../config/P177/eol_config.yaml";
  std::string intrinsic_path = "../data/P177/soca_cam_intrinsics_P1005.yaml";
  // std::string intrinsic_path = "../data/P177/soca_cam_intrisics_P1017.yaml";
  std::string extrinsic_config_path = "../config/P177/cam_extrinsics.yaml";
  std::string calibroom_points_path = "../data/P177/geely_calibroom_points_map.txt";

  // P177 7V images: 2025.04.27
  // std::string img_dir = "../data/P177/geely_calibroom_images_2025.04.27";

  // P177 7V images: 2025.05.25
  std::string img_dir = "../data/P177/geely_calibroom_images_2025.05.25";

  // P177 7V images: 2025.06.16
  // std::string img_dir = "../debug/2025.06.16";

  /*
  // Z10
  std::string eol_config_path = "../config/Z10/eol_config.yaml";
  std::string intrinsic_path = "../data/Z10/soca_cam_intrisics.yaml";
  std::string extrinsic_config_path = "../config/Z10/cam_extrinsics.yaml";
  std::string calibroom_points_path = "../data/Z10/geely_calibroom_points_map.txt";

  // Z10 7V images
  std::string img_dir = "../data/Z10/geely_calibroom_image";
  // std::vector<std::pair<std::string, std::string>> image_files = {
  //   {"cam_back_70", "../data/Z10/geely_calibroom_image/cam_back_70.jpg"},
  //   {"cam_back_left_100", "../data/Z10/geely_calibroom_image/cam_back_left_100.jpg"},
  //   {"cam_back_right_100", "../data/Z10/geely_calibroom_image/cam_back_right_100.jpg"},
  //   {"cam_front_30", "../data/Z10/geely_calibroom_image/cam_front_30.jpg"},
  //   {"cam_front_120", "../data/Z10/geely_calibroom_image/cam_front_120.jpg"},
  //   {"cam_front_left_100", "../data/Z10/geely_calibroom_image/cam_front_left_100.jpg"},
  //   {"cam_front_right_100", "../data/Z10/geely_calibroom_image/cam_front_right_100.jpg"},
  // };
*/

  if (argc > 1) {
    eol_config_path = argv[1];
  } else {
    LOG(INFO) << "No eol_config_path provided! Default as: " << eol_config_path << std::endl;
  }

  if (argc == 2) {
    LOG(INFO) << "**** Need to provide both img_dir and intrinsic_path! ****" << std::endl;
  }

  if (argc > 3) {
    img_dir = argv[2];
    intrinsic_path = argv[3];
    LOG(INFO) << "img_dir: " << img_dir << std::endl;
    LOG(INFO) << "intrinsic_path: " << intrinsic_path << std::endl;
  } else {
    LOG(INFO) << "No valid img_dir provided! Default as: " << img_dir << std::endl;
    LOG(INFO) << "No valid intrinsic_path provided! Default as: " << intrinsic_path << std::endl;
  }

  // load eol_config
  get_config_from_yaml(eol_config_path, eol_config);

  // get image_files
  get_image_files_from_dir(img_dir, eol_config, image_files);

  // intrinsic
  get_cameras_intrinsic_from_yaml(intrinsic_path, eol_config, cameras_intrinsic);
  // get_cameras_intrinsic_from_default(cameras_intrinsic);

  // structure extrinsic
  get_cameras_extrinsic_from_yaml(cameras_structure, eol_config, extrinsic_config_path);

  // calibroom points
  load_calibroom_points(calibroom_points_path, calibroom_points_map);

  if (eol_config.is_simulation_error) {
    add_f_D_calibroom_points_offset(cameras_intrinsic, calibroom_points_map, eol_config);
  }

  CameraEOLCalibration::CalibrationParam param(SensorProductionCalibration::CalibrationType::CAMERA_EOL_CALIBRATION,
                                               FLAGS_log_dir, cameras_intrinsic, cameras_structure, eol_config,
                                               calibroom_points_map);

  camera_eol_calibration_ptr->Init(param);

  std::map<std::string, CameraEOLCalibration::SensorInput> sensors_input;
  std::string batch_num = "";

  for (const auto& image_file : image_files) {
    cv::Mat image = cv::imread(image_file.second);
    CameraEOLCalibration::SensorInput sensor_input(SensorProductionCalibration::CalibrationType::CAMERA_EOL_CALIBRATION,
                                                   0, image);
    sensors_input.insert(std::make_pair(image_file.first, sensor_input));
  }

  std::map<std::string, CalibrationResult> calibration_results;
  camera_eol_calibration_ptr->RunCalibration(sensors_input, calibration_results);

  LOG(INFO) << "===================== Camera Calibration Finished! =====================" << std::endl;
  LOG(INFO) << "Camera calibration results:";
  for (const auto& result : calibration_results) {
    // ZYX order: yaw, pitch, roll
    Eigen::Quaterniond rfu_tf_cam_q =
        Eigen::Quaterniond(result.second.calib_extrinsic.orientation.w, result.second.calib_extrinsic.orientation.x,
                           result.second.calib_extrinsic.orientation.y, result.second.calib_extrinsic.orientation.z);
    Eigen::Vector3d calib_rpy = rfu_tf_cam_q.toRotationMatrix().eulerAngles(2, 1, 0);

    LOG(INFO) << "  " << result.first << ": ";
    LOG(INFO) << "    calib_status       : " << static_cast<uint32_t>(result.second.calib_status) << std::endl;
    LOG(INFO) << "    error_code         : " << static_cast<uint32_t>(result.second.calib_error_code) << std::endl;
    LOG(INFO) << "    reproj_error       : "
              << std::get<double>(result.second.custom_fields.at("camera_calib_reproj_error")) << std::endl;
    LOG(INFO) << "    POSITION           : " << result.second.calib_extrinsic.position.x << " "
              << result.second.calib_extrinsic.position.y << " " << result.second.calib_extrinsic.position.z
              << std::endl;
    LOG(INFO) << "    ORIENTATION        : " << result.second.calib_extrinsic.orientation.w << " "
              << result.second.calib_extrinsic.orientation.x << " " << result.second.calib_extrinsic.orientation.y
              << " " << result.second.calib_extrinsic.orientation.z << std::endl;
    LOG(INFO) << "    ROTATION ANGLE(ZYX): " << calib_rpy[0] / M_PI * 180 << " " << calib_rpy[1] / M_PI * 180 << " "
              << calib_rpy[2] / M_PI * 180 << std::endl;

    if (static_cast<uint32_t>(result.second.calib_status) != 0 ||
        static_cast<uint32_t>(result.second.calib_error_code) != 0) {
      is_calib_success = false;
    }
  }

  // Save calibration results to YAML
  std::map<std::string, std::string> mach2geely_camera_mappings;
  for (const auto& mapping : eol_config.geely2mach_camera_mappings) {
    mach2geely_camera_mappings.insert(std::make_pair(mapping.second, mapping.first));
  }

  // Save calibration results to YAML
  std::string output_yaml_path = eol_config.tmp_output_dir + "/driving_camera_calibresult.yaml";
  if (!std::filesystem::exists(eol_config.tmp_output_dir)) {
    std::filesystem::create_directories(eol_config.tmp_output_dir);
  }

  YAML::Node root;
  root["VIN"] = "xxx";               // Replace "xxx" with the actual VIN if available
  root["software_version"] = "xxx";  // calibration software version

  for (const auto& result : calibration_results) {
    YAML::Node cam;
    cam["calib_errcode"] = static_cast<int>(result.second.calib_error_code);
    cam["calib_mode"] = static_cast<int>(SensorProductionCalibration::CalibrationType::CAMERA_EOL_CALIBRATION);
    cam["calib_status"] = static_cast<int>(result.second.calib_status);
    cam["calib_time"] = "1900-01-01 00:00:00";  // Replace with actual
                                                // calibration time if available

    // calib_valid
    cam["calib_valid"] = (static_cast<int>(result.second.calib_error_code) == 0 ? 1 : 0);

    // custom_fields
    YAML::Node custom_fields;
    custom_fields["camera_calib_reproj_error"] =
        std::get<double>(result.second.custom_fields.at("camera_calib_reproj_error"));
    cam["custom_fields"] = custom_fields;

    // Euler angles
    Eigen::Quaterniond cam_tf_rfu_q(
        result.second.calib_extrinsic.orientation.w, result.second.calib_extrinsic.orientation.x,
        result.second.calib_extrinsic.orientation.y, result.second.calib_extrinsic.orientation.z);
    Eigen::Vector3d calib_rpy = cam_tf_rfu_q.toRotationMatrix().eulerAngles(2, 1, 0);  // ZYX

    YAML::Node euler_degree;
    euler_degree["RotZ"] = calib_rpy[0] / M_PI * 180.0;
    euler_degree["RotY"] = calib_rpy[1] / M_PI * 180.0;
    euler_degree["RotX"] = calib_rpy[2] / M_PI * 180.0;
    cam["euler_degree"] = euler_degree;

    cam["information"] = result.first + "_tf_rfu";  // Concatenate camera name with "_tf_rfu"

    // rotation
    YAML::Node rotation;
    rotation["w"] = result.second.calib_extrinsic.orientation.w;
    rotation["x"] = result.second.calib_extrinsic.orientation.x;
    rotation["y"] = result.second.calib_extrinsic.orientation.y;
    rotation["z"] = result.second.calib_extrinsic.orientation.z;
    cam["rotation"] = rotation;

    // translation
    YAML::Node translation;
    translation["x"] = result.second.calib_extrinsic.position.x;
    translation["y"] = result.second.calib_extrinsic.position.y;
    translation["z"] = result.second.calib_extrinsic.position.z;
    cam["translation"] = translation;

    // use mapping as key
    std::string cam_name = mach2geely_camera_mappings.at(result.first);
    root[cam_name] = cam;
  }

  std::ofstream fout(output_yaml_path);
  // fout << "---\n"; // comment out this line if you don't want to use YAML
  // anchors
  fout << root;
  fout.close();

  if (!eol_config.is_simulation_error || eol_config.simulation_error_config.test_num == 0) {
    google::ShutdownGoogleLogging();
    if (is_calib_success) {
      std::cout << std::endl << "Camera Calibration Completed Successfully!" << std::endl;
      return 0;
    } else {
      std::cout << std::endl << "Camera Calibration Failed!" << std::endl;
      return 1;
    }
  }

  // Simulation error
  LOG(INFO) << std::endl << std::endl << std::endl;
  LOG(INFO) << "============================== Simulation error ===============================" << std::endl;

  std::map<std::string, std::map<std::string, CalibrationResult>> simulation_calibration_results;
  simulation_calibration_results.clear();
  simulation_calibration_results.insert(std::make_pair("gt", calibration_results));

  // Initialize progress bar
  int total_tests = eol_config.simulation_error_config.test_num;
  LOG(INFO) << "Running simulation tests...";

  auto start_time = std::chrono::high_resolution_clock::now();

  for (int i = 0; i < total_tests; ++i) {
    std::map<std::string, CameraIntrinsicParam> test_cameras_intrinsic;
    std::map<std::string, Point> test_calibroom_points_map;
    test_cameras_intrinsic = cameras_intrinsic;        // Deep copy
    test_calibroom_points_map = calibroom_points_map;  // Deep copy

    add_f_D_calibroom_points_offset(test_cameras_intrinsic, test_calibroom_points_map, eol_config);

    calibration_algo::eol::camera::CameraEOLCalibration::CalibrationParam param(
        calibration_algo::eol::camera::SensorProductionCalibration::CalibrationType::CAMERA_EOL_CALIBRATION,
        FLAGS_log_dir, test_cameras_intrinsic, cameras_structure, eol_config, test_calibroom_points_map);

    camera_eol_calibration_ptr->Init(param);

    std::map<std::string, CalibrationResult> calibration_results;
    camera_eol_calibration_ptr->RunCalibration(sensors_input, calibration_results);

    simulation_calibration_results.insert(std::make_pair("test_" + std::to_string(i), calibration_results));

    // Update progress bar
    int progress = static_cast<int>((static_cast<float>(i + 1) / total_tests) * 100);
    LOG(INFO) << "Progress: [" << std::string(progress / 2, '=') << std::string(50 - progress / 2, ' ') << "] "
              << progress << "%";
  }

  LOG(INFO) << std::endl;
  LOG(INFO) << std::endl;
  LOG(INFO) << "============================== Simulation tests completed ===============================" << std::endl;

  auto end_time = std::chrono::high_resolution_clock::now();
  LOG(INFO) << std::endl;
  LOG(INFO) << std::endl;
  LOG(INFO) << "Time taken: " << std::chrono::duration_cast<std::chrono::seconds>(end_time - start_time).count()
            << " seconds" << std::endl;

  // Save calibration results to YAML
  std::string output_yaml_path_simulation =
      eol_config.tmp_output_dir + "/" + eol_config.simulation_error_config.output_simulation_error_name;
  cv::FileStorage fs_simulation(output_yaml_path_simulation, cv::FileStorage::WRITE);

  for (const auto& calib_out : simulation_calibration_results) {
    fs_simulation << calib_out.first << "{";

    for (const auto& result : calib_out.second) {
      fs_simulation << result.first << "{";

      fs_simulation << "translation"
                    << "{";
      fs_simulation << "x" << result.second.calib_extrinsic.position.x;
      fs_simulation << "y" << result.second.calib_extrinsic.position.y;
      fs_simulation << "z" << result.second.calib_extrinsic.position.z;
      fs_simulation << "}";

      fs_simulation << "rotation"
                    << "{";
      fs_simulation << "w" << result.second.calib_extrinsic.orientation.w;
      fs_simulation << "x" << result.second.calib_extrinsic.orientation.x;
      fs_simulation << "y" << result.second.calib_extrinsic.orientation.y;
      fs_simulation << "z" << result.second.calib_extrinsic.orientation.z;
      fs_simulation << "}";

      Eigen::Quaterniond cam_tf_rfu_q =
          Eigen::Quaterniond(result.second.calib_extrinsic.orientation.w, result.second.calib_extrinsic.orientation.x,
                             result.second.calib_extrinsic.orientation.y, result.second.calib_extrinsic.orientation.z);
      Eigen::Vector3d calib_rpy = cam_tf_rfu_q.toRotationMatrix().eulerAngles(2, 1, 0);

      fs_simulation << "rotation_angle"
                    << "{";
      fs_simulation << "RotX" << calib_rpy[2] / M_PI * 180;
      fs_simulation << "RotY" << calib_rpy[1] / M_PI * 180;
      fs_simulation << "RotZ" << calib_rpy[0] / M_PI * 180;
      fs_simulation << "}";
      fs_simulation << "}";
    }

    fs_simulation << "}";
  }

  fs_simulation.release();

  google::ShutdownGoogleLogging();

  return 1;
}
