
#include <sys/stat.h>

#include "AprilTags/TagDetector.h"
#include "calibration_algo/eol/camera/camera_eol_calibration.h"
#include "calibration_algo/eol/camera/utils/common_utils.hpp"

using namespace calibration_algo::common;
using namespace calibration_algo::eol::camera;

int main(int argc, char **argv) {
  // GLOG
  google::InitGoogleLogging(argv[0]);
  FLAGS_log_dir = "./log";
  if (!std::filesystem::exists(FLAGS_log_dir)) {
    std::filesystem::create_directories(FLAGS_log_dir);
  }

  FLAGS_alsologtostderr = true;
  FLAGS_stop_logging_if_full_disk = true;

  CameraEOLCalibrationPtr camera_eol_calibration_ptr(new CameraEOLCalibration);

  std::map<std::string, CameraIntrinsicParam> cameras_intrinsic;
  std::map<std::string, ExtrinsicPose> cameras_structure;
  std::map<std::string, Point> calibroom_points_map;
  EOLParams eol_config;

  // // P177
  std::string eol_config_path = "../config/P177/eol_config.yaml";
  std::string intrinsic_path = "../data/P177/soca_cam_intrisics.yaml";
  std::string extrinsic_config_path = "../config/P177/cam_extrinsics.yaml";
  std::string calibroom_points_path = "../data/P177/geely_calibroom_points_map.txt";

  // P177 7V images: 2025.04.09
  std::string cam_name = "cam_back_left_100";
  std::string img_path =
      "/data/production_calibraton/data/P177/geely_calibroom_images_2025.04.17/cam_back_left_100.jpg";
  std::string output_dir = "./output_batch_test/apriltag_detection_test";

  if (argc > 1) {
    cam_name = argv[1];
    LOG(INFO) << "Camera name: " << cam_name;
  } else {
    LOG(INFO) << "Using default camera name: " << cam_name;
  }

  if (argc > 2) {
    img_path = argv[2];
    LOG(INFO) << "Image path: " << img_path;
  } else {
    LOG(INFO) << "Using default image path: " << img_path;
  }

  if (argc > 3) {
    output_dir = argv[3];
    LOG(INFO) << "Output directory: " << output_dir;
  } else {
    LOG(INFO) << "Using default output directory: " << output_dir;
  }

  if (argc > 4) {
    eol_config_path = argv[4];
    LOG(INFO) << "EOL config path: " << eol_config_path;
  } else {
    LOG(INFO) << "Using default EOL config path: " << eol_config_path;
  }

  // load eol_config
  get_config_from_yaml(eol_config_path, eol_config);

  // intrinsic
  get_cameras_intrinsic_from_yaml(intrinsic_path, eol_config, cameras_intrinsic);
  // get_cameras_intrinsic_from_default(cameras_intrinsic);

  // structure extrinsic
  get_cameras_extrinsic_from_yaml(cameras_structure, eol_config, extrinsic_config_path);

  // calibroom points
  load_calibroom_points(calibroom_points_path, calibroom_points_map);

  CameraEOLCalibration::CalibrationParam param(SensorProductionCalibration::CalibrationType::CAMERA_EOL_CALIBRATION,
                                               FLAGS_log_dir, cameras_intrinsic, cameras_structure, eol_config,
                                               calibroom_points_map);

  camera_eol_calibration_ptr->Init(param);
  std::vector<AprilTags::TagDetection> detections;

  cv::Mat img = cv::imread(img_path);
  if (img.empty()) {
    LOG(ERROR) << "Failed to read image: " << img_path;
    return 1;
  }

  detections = camera_eol_calibration_ptr->GetExtrinsicEstimator()->detect_apriltags(cam_name, img);

  if (detections.empty()) {
    LOG(ERROR) << "No AprilTag detections found in image: " << img_path;
  } else {
    // plot
    for (const auto &detection : detections) {
      detection.draw(img, cv::Scalar(0, 0, 255), 3);
    }
    std::string this_output_dir = output_dir + "/" + "plot_img";
    std::string output_path =
        this_output_dir + "/" + std::filesystem::path(img_path).parent_path().filename().string() + ".jpg";
    if (!std::filesystem::exists(this_output_dir)) {
      std::filesystem::create_directories(this_output_dir);
    }
    cv::imwrite(output_path, img);
  }

  // save detection_ids
  std::string this_output_dir = output_dir + "/" + "detections";
  std::string output_path =
      this_output_dir + "/" + std::filesystem::path(img_path).parent_path().filename().string() + ".txt";
  if (!std::filesystem::exists(this_output_dir)) {
    std::filesystem::create_directories(this_output_dir);
  }
  std::ofstream output_file(output_path);
  for (const auto &detection : detections) {
    output_file << detection.id << "\n";
  }
  output_file.close();

  google::ShutdownGoogleLogging();

  return 0;
}
