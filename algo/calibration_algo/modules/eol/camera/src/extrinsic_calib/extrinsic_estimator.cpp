#include "calibration_algo/eol/camera/extrinsic_calib/extrinsic_estimator.h"

#include "calibration_algo/eol/camera/error_code.hpp"

using namespace calibration_algo::common;

namespace calibration_algo::eol::camera {

bool ExtrinsicEstimator::Init(const EOLParams& eol_config, const std::map<std::string, Point>& calibroom_points_map,
                              const SensorProductionCalibration::CalibrationType& calib_type) {
  eol_config_ = eol_config;
  // const AprilTags::TagCodes& m_tagCodes = eol_config.m_tagCodes;

  const std::map<std::string, std::vector<vector<int>>> necessary_ids = eol_config_.check_direction_necessary_ids;
  const std::map<std::string, std::vector<int>> full_ids = eol_config_.check_direction_full_ids;
  const std::vector<std::string> cam_names = eol_config_.cam_names;

  // init april tag detector
  m_tagDetector_.reset(new AprilTags::TagDetector(eol_config.m_tagCodes));

  // // init w_pts map from input map
  for (auto it = calibroom_points_map.begin(); it != calibroom_points_map.end(); ++it) {
    std::string keyname = it->first;
    Eigen::Vector3d wp(it->second.x, it->second.y, it->second.z);
    calibroom_points_map_.insert(std::make_pair(keyname, wp));
  }

  // print calibroom_points_map_ for testing
  // for (const auto& point : calibroom_points_map_)
  // {
  //     std::cout << "Key: " << point.first << ", Value: [" << point.second[0] << ", " << point.second[1] << ", " <<
  //     point.second[2] << "]" << std::endl;
  // }

  /*
  // supported cam directions
  supported_cam_directions_.insert("front");
  supported_cam_directions_.insert("front_left");
  supported_cam_directions_.insert("front_right");
  supported_cam_directions_.insert("back_left");
  supported_cam_directions_.insert("back_right");
  supported_cam_directions_.insert("back");
  */

  // Default output order for AprilTag detection
  int2location_map_ = {{0, "leftdown"}, {1, "rightdown"}, {2, "rightup"}, {3, "leftup"}};

  /*
      rfu                           cam
        y                             -y
    z   |                         z   |
    \  |                          \  |
      \ |                           \ |
      \|                            \|
        ------- x                     ------- x
      ego
        x
    z   |
    \  |
      \ |
      \|
y -------
  */
  Eigen::Matrix3d ego_tf_rfu_mat;
  ego_tf_rfu_mat << 0, 1, 0, -1, 0, 0, 0, 0, 1;  // Rotate 90 degrees around the z-axis
  ego_tf_rfu_ = ego_tf_rfu_mat;

  // init id map
  init_direction_map(necessary_ids, full_ids);

  // init prior param
  init_prior_parameters(cam_names);

  // init variables
  calib_type_ = calib_type;
  reset();

  return true;
}

void ExtrinsicEstimator::reset() {
  sensors_calibration_result_.clear();
  problem_.reset(new ceres::Problem());
  Ci_tf_W_.clear();
  cam_.clear();
  wPts_.clear();
  rayPts_.clear();
  imgPts_.clear();
  imgs_.clear();
  camNames_.clear();
  json_key_names_.clear();
  sensors_calibration_result_.clear();
  structure_param_map_ypr_.clear();
  structure_param_map_t_.clear();
}

void ExtrinsicEstimator::init_direction_map(const std::map<std::string, std::vector<std::vector<int>>>& necessary_ids,
                                            const std::map<std::string, std::vector<int>>& full_ids) {
  direction_to_necessary_ids_map_ = necessary_ids;
  direction_to_full_ids_map_ = full_ids;

  /*
  // front
  std::vector<int> vec;
  vec.push_back(8);vec.push_back(9);vec.push_back(10);vec.push_back(11);vec.push_back(12);vec.push_back(13);vec.push_back(14);vec.push_back(15);
  direction_to_necessary_ids_map_.insert(std::make_pair("front", vec));
  vec.push_back(0);vec.push_back(1);vec.push_back(0);vec.push_back(1);
  std::sort(vec.begin(), vec.end());
  direction_to_full_ids_map_.insert(std::make_pair("front", vec));

  // front_left
  vec.clear();
  vec.push_back(0);vec.push_back(1);vec.push_back(2);vec.push_back(3);vec.push_back(8);vec.push_back(9);vec.push_back(10);vec.push_back(11);
  direction_to_necessary_ids_map_.insert(std::make_pair("front_left", vec));
  vec.push_back(4);vec.push_back(5);
  std::sort(vec.begin(), vec.end());
  direction_to_full_ids_map_.insert(std::make_pair("front_left", vec));

  // front_right
  vec.clear();
  vec.push_back(0);vec.push_back(1);vec.push_back(2);vec.push_back(3);vec.push_back(12);vec.push_back(13);vec.push_back(14);vec.push_back(15);
  direction_to_necessary_ids_map_.insert(std::make_pair("front_right", vec));
  vec.push_back(4);vec.push_back(5);
  std::sort(vec.begin(), vec.end());
  direction_to_full_ids_map_.insert(std::make_pair("front_right", vec));

  // back_left
  vec.clear();
  vec.push_back(4);vec.push_back(5);vec.push_back(6);vec.push_back(7);vec.push_back(20);vec.push_back(21);
  direction_to_necessary_ids_map_.insert(std::make_pair("back_left", vec));
  vec.push_back(2);vec.push_back(3);
  std::sort(vec.begin(), vec.end());
  direction_to_full_ids_map_.insert(std::make_pair("back_left", vec));

  // back_right
  vec.clear();
  vec.push_back(4);vec.push_back(5);vec.push_back(6);vec.push_back(7);vec.push_back(16);vec.push_back(17);vec.push_back(18);vec.push_back(19);
  direction_to_necessary_ids_map_.insert(std::make_pair("back_right", vec));
  vec.push_back(2);vec.push_back(3);
  std::sort(vec.begin(), vec.end());
  direction_to_full_ids_map_.insert(std::make_pair("back_right", vec));

  // back
  vec.clear();
  vec.push_back(16);vec.push_back(17);vec.push_back(18);vec.push_back(19);vec.push_back(20);vec.push_back(21);
  direction_to_necessary_ids_map_.insert(std::make_pair("back", vec));
  direction_to_full_ids_map_.insert(std::make_pair("back", vec));
  */
}

void ExtrinsicEstimator::init_prior_parameters(const std::vector<std::string>& cam_names) {
  bool all_params_exist = std::all_of(cam_names.begin(), cam_names.end(), [&](const std::string& cam_name) {
    return eol_config_.prior_q.find(cam_name) != eol_config_.prior_q.end() &&
           eol_config_.prior_t.find(cam_name) != eol_config_.prior_t.end();
  });

  if (all_params_exist) {
    LOG(INFO) << "load prior_q & t from eol_config.yaml!" << std::endl;
    LOG(INFO) << "vehicle model set to -> " << eol_config_.vehicle_model << std::endl;
    prior_q_ = eol_config_.prior_q;
    prior_t_ = eol_config_.prior_t;
  } else {
    LOG(ERROR) << "No enough prior_q & t in eol_config.yaml, please check! All cam_names:" << std::endl;
    // print all cam_names
    for (const auto& cam_name : cam_names) {
      LOG(ERROR) << cam_name << std::endl;
    }
    LOG(ERROR) << "exit!" << std::endl;
    exit(0);
    // LOG(ERROR) << "init prior_q & t from default" << std::endl;
    // std::map<std::string, std::pair<Eigen::Quaterniond, Eigen::Vector3d>> default_prior_cam_params = {
    //     {"cam_front_120", {{0.710817590659871, 0.703362194621378, -0.00445042445440167, -0.00041195639409545603},
    //     {0.020027444000000002, 1.286506, -1.958484}}},
    //     {"cam_front_left_100", {{0.6392810607474025, 0.6393336233338163, 0.2994606073209174, -0.30478777552178354},
    //     {-1.131762, 0.9766746, -2.1065580000000006}}},
    //     {"cam_front_right_100", {{0.645433150884695, 0.6348681166689356, -0.307257691234454, 0.2931744077397721},
    //     {1.1459240000000002, 0.9367300000000001, -2.11464}}},
    //     {"cam_back_left_100", {{0.3133409720261043, 0.29051453979639896, 0.6462224077067589, -0.6323095264143204},
    //     {-2.573298, 0.6835576, 0.8652266000000001}}},
    //     {"cam_back_right_100", {{-0.3087073867583906, -0.2916619018280692, 0.6405220819115278, -0.6398160258750645},
    //     {2.555448, 0.7415428, 0.8624628}}},
    //     {"cam_back_100", {{0.007687044639153465, 0.00913829621095994, 0.71885111968638, -0.6950614854908262},
    //     {-0.0067396700000000006, 0.9451537999999999, -0.9835083999999998}}}
    // };
    // for (const auto& cam_name : cam_names)
    // {
    //     prior_q_[cam_name] = default_prior_cam_params[cam_name].first; //  q: w x y z
    //     prior_t_[cam_name] = default_prior_cam_params[cam_name].second; // t: x y z
    // }
  }

  /*
  if (eol_config_.prior_q.find("cam_front_120") != eol_config_.prior_q.end() &&
  eol_config_.prior_t.find("cam_front_120") != eol_config_.prior_t.end() &&
      eol_config_.prior_q.find("cam_front_left_100") != eol_config_.prior_q.end() &&
  eol_config_.prior_t.find("cam_front_left_100") != eol_config_.prior_t.end() &&
      eol_config_.prior_q.find("cam_front_right_100") != eol_config_.prior_q.end() &&
  eol_config_.prior_t.find("cam_front_right_100") != eol_config_.prior_t.end() &&
      eol_config_.prior_q.find("cam_back_100") != eol_config_.prior_q.end() && eol_config_.prior_t.find("cam_back_100")
  != eol_config_.prior_t.end() && eol_config_.prior_q.find("cam_back_left_100") != eol_config_.prior_q.end() &&
  eol_config_.prior_t.find("cam_back_left_100") != eol_config_.prior_t.end() &&
      eol_config_.prior_q.find("cam_back_right_100") != eol_config_.prior_q.end() &&
  eol_config_.prior_t.find("cam_back_right_100") != eol_config_.prior_t.end())
  {
      LOG(ERROR) << "load prior_q & t from eol config yaml!" << std::endl;
      LOG(ERROR) << "vehicle model set to -> " << eol_config_.vehicle_model << std::endl;
      prior_q_ = eol_config_.prior_q;
      prior_t_ = eol_config_.prior_t;
  }
  else
  {
      LOG(ERROR) << "init prior_q & t from default" << std::endl;
      Eigen::Quaterniond cam_tf_rfu_q;
      Eigen::Vector3d cam_tf_rfu_t;
      // front
      cam_tf_rfu_q.w() = 0.710817590659871;
      cam_tf_rfu_q.x() = 0.703362194621378;
      cam_tf_rfu_q.y() = -0.00445042445440167;
      cam_tf_rfu_q.z() =  -0.00041195639409545603;
      cam_tf_rfu_t[0] = 0.020027444000000002;
      cam_tf_rfu_t[1] = 1.286506;
      cam_tf_rfu_t[2] = -1.958484;
      prior_q_.insert(std::make_pair("cam_front_120", cam_tf_rfu_q));
      prior_t_.insert(std::make_pair("cam_front_120", cam_tf_rfu_t));
      // front_left
      cam_tf_rfu_q.w() = 0.6392810607474025;
      cam_tf_rfu_q.x() = 0.6393336233338163;
      cam_tf_rfu_q.y() = 0.2994606073209174;
      cam_tf_rfu_q.z() = -0.30478777552178354;
      cam_tf_rfu_t[0] = -1.131762;
      cam_tf_rfu_t[1] = 0.9766746;
      cam_tf_rfu_t[2] = -2.1065580000000006;
      prior_q_.insert(std::make_pair("cam_front_left_100", cam_tf_rfu_q));
      prior_t_.insert(std::make_pair("cam_front_left_100", cam_tf_rfu_t));
      // front_right
      cam_tf_rfu_q.w() = 0.645433150884695;
      cam_tf_rfu_q.x() = 0.6348681166689356;
      cam_tf_rfu_q.y() = -0.307257691234454;
      cam_tf_rfu_q.z() = 0.2931744077397721;
      cam_tf_rfu_t[0] = 1.1459240000000002;
      cam_tf_rfu_t[1] = 0.9367300000000001;
      cam_tf_rfu_t[2] =  -2.11464;
      prior_q_.insert(std::make_pair("cam_front_right_100", cam_tf_rfu_q));
      prior_t_.insert(std::make_pair("cam_front_right_100", cam_tf_rfu_t));
      cam_tf_rfu_q.w() = 0.3133409720261043;
      cam_tf_rfu_q.x() = 0.29051453979639896;
      cam_tf_rfu_q.y() = 0.6462224077067589;
      cam_tf_rfu_q.z() = -0.6323095264143204;
      cam_tf_rfu_t[0] = -2.573298;
      cam_tf_rfu_t[1] = 0.6835576;
      cam_tf_rfu_t[2] = 0.8652266000000001;
      prior_q_.insert(std::make_pair("cam_back_left_100", cam_tf_rfu_q));
      prior_t_.insert(std::make_pair("cam_back_left_100", cam_tf_rfu_t));
      cam_tf_rfu_q.w() = -0.3087073867583906;
      cam_tf_rfu_q.x() = -0.2916619018280692;
      cam_tf_rfu_q.y() = 0.6405220819115278;
      cam_tf_rfu_q.z() = -0.6398160258750645;
      cam_tf_rfu_t[0] = 2.555448;
      cam_tf_rfu_t[1] = 0.7415428;
      cam_tf_rfu_t[2] = 0.8624628;
      prior_q_.insert(std::make_pair("cam_back_right_100", cam_tf_rfu_q));
      prior_t_.insert(std::make_pair("cam_back_right_100", cam_tf_rfu_t));
      cam_tf_rfu_q.w() = 0.007687044639153465;
      cam_tf_rfu_q.x() = 0.00913829621095994;
      cam_tf_rfu_q.y() = 0.71885111968638;
      cam_tf_rfu_q.z() = -0.6950614854908262;
      cam_tf_rfu_t[0] = -0.0067396700000000006;
      cam_tf_rfu_t[1] = 0.9451537999999999;
      cam_tf_rfu_t[2] = -0.9835083999999998;
      prior_q_.insert(std::make_pair("cam_back_100", cam_tf_rfu_q));
      prior_t_.insert(std::make_pair("cam_back_100", cam_tf_rfu_t));
  }
  */
}

bool ExtrinsicEstimator::set_camera_model(const std::string& cam_name, const CameraIntrinsicParam& IntrinsicParam,
                                          const int& image_width, const int& image_height) {
  if (IntrinsicParam.distortion_type != CameraDistortionType::FISHEYE &&
      IntrinsicParam.distortion_type != CameraDistortionType::PINHOLE) {
    return false;
  }

  cv::Mat K;
  K.create(3, 3, CV_64F);
  K.setTo(cv::Scalar(0));
  K.at<double>(0, 0) = IntrinsicParam.fx;
  K.at<double>(0, 2) = IntrinsicParam.cx;
  K.at<double>(1, 1) = IntrinsicParam.fy;
  K.at<double>(1, 2) = IntrinsicParam.cy;
  K.at<double>(2, 2) = 1;
  cv::Mat D;
  D.create(8, 1, CV_64FC1);
  D.setTo(cv::Scalar(0));
  D.at<double>(0, 0) = IntrinsicParam.k1;
  D.at<double>(1, 0) = IntrinsicParam.k2;
  D.at<double>(2, 0) = IntrinsicParam.k3;
  D.at<double>(3, 0) = IntrinsicParam.k4;
  D.at<double>(4, 0) = IntrinsicParam.k5;
  D.at<double>(5, 0) = IntrinsicParam.k6;
  D.at<double>(6, 0) = IntrinsicParam.p1;
  D.at<double>(7, 0) = IntrinsicParam.p2;

  return set_camera_model(cam_name, K, D, image_width, image_height, IntrinsicParam.distortion_type);
}

bool ExtrinsicEstimator::set_camera_model(const std::string& cam_name, const cv::Mat& K, const cv::Mat& D,
                                          const int& Iw, const int& Ih, const CameraDistortionType& cam_type) {
  double fx = K.at<double>(0, 0);
  double fy = K.at<double>(1, 1);
  double cx = K.at<double>(0, 2);
  double cy = K.at<double>(1, 2);
  camodocal::CameraPtr cam_ptr;
  if (cam_type == CameraDistortionType::FISHEYE) {
    double k2 = D.at<double>(0, 0);
    double k3 = D.at<double>(1, 0);
    double k4 = D.at<double>(2, 0);
    double k5 = D.at<double>(3, 0);
    cam_ptr.reset(new camodocal::EquidistantCamera(cam_name, Iw, Ih, k2, k3, k4, k5, fx, fy, cx, cy));
  } else if (cam_type == CameraDistortionType::PINHOLE) {
    double k1 = D.at<double>(0, 0);
    double k2 = D.at<double>(1, 0);
    double k3 = D.at<double>(2, 0);
    double k4 = D.at<double>(3, 0);
    double k5 = D.at<double>(4, 0);
    double k6 = D.at<double>(5, 0);
    double p1 = D.at<double>(6, 0);
    double p2 = D.at<double>(7, 0);
    cam_ptr.reset(new camodocal::PinholeFullCamera(cam_name, Iw, Ih, k1, k2, k3, k4, k5, k6, p1, p2, fx, fy, cx, cy));
  } else {
    return false;
  }
  cam_.insert(std::make_pair(cam_name, cam_ptr));

  return true;
}

// acquire world points
bool ExtrinsicEstimator::acquire_w_pts(const std::string& json_key_name, cv::Point3f& w_pt,
                                       CalibrationResult& sensor_calibration_result) {
  // for (const auto& point : calibroom_points_map_) {
  //     std::cout << "Key: " << point.first << ", Value: [" << point.second[0] << ", " << point.second[1] << ", " <<
  //     point.second[2] << "]" << std::endl;
  // }

  if (calibroom_points_map_.find(json_key_name) == calibroom_points_map_.end()) {
    LOG(ERROR) << "acquire_w_pts for " << json_key_name << " fail!" << std::endl;
    // sensor_calibration_result.calib_error_code =
    // common::CalibrationErrorCode::EOL_CAMERA_COMMON_INTERNAL_CALIBRATION_ERROR; false;
    w_pt.x = 0;
    w_pt.y = 0;
    w_pt.z = 0;
  } else {
    w_pt.x = calibroom_points_map_.at(json_key_name)[0];
    w_pt.y = calibroom_points_map_.at(json_key_name)[1];
    w_pt.z = calibroom_points_map_.at(json_key_name)[2];
  }
  return true;
}

// acquire world points
bool ExtrinsicEstimator::acquire_w_pts(const cv::Mat& img, const std::string& cam_direction, const int& tag_id,
                                       const std::string& location, std::string& json_key_name,
                                       const cv::Point2f& img_pt, cv::Point3f& w_pt,
                                       CalibrationResult& sensor_calibration_result) {
  /*
     Z10 & P177 geely tag-id, AprilTag need to -1                 AprilTag id

         4,3    1,2                                                3,2    0,1



    5,7               8,6                                      4,6               7,5

          _________                                                  _________
         |  front  |                                                |  front  |
  12     |         |     11                                  11     |         |     10
  10     |         |     9                                    9     |         |     8
         |   car   |                                                |   car   |
         |         |                                                |         |
         |         |                                                |         |
         |  back   |                                                |  back   |
  14     |_________|     13                                  13     |_________|     12
     16               15                                        15               14


   18,20             17,19                                    17,19             16,18


  */
  std::string vehicle_type = eol_config_.vehicle_model;
  // LOG(INFO) << "vehicle_type: " << vehicle_type << std::endl;

  // for (const auto& point : calibroom_points_map_) {
  //     std::cout << "Key: " << point.first << ", Value: [" << point.second[0] << ", " << point.second[1] << ", " <<
  //     point.second[2] << "]" << std::endl;
  // }
  // std::cout << "tag_id: " << tag_id << std::endl;
  // std::cout << "location: " << location << std::endl;
  // std::cout << "img_pt: " << img_pt << std::endl;

  if (vehicle_type == "Z10" || vehicle_type == "P177") {
    json_key_name = std::to_string(tag_id) + "_" + location;
    if (!acquire_w_pts(json_key_name, w_pt, sensor_calibration_result)) return false;

  } else {
    LOG(ERROR) << "Vehicle type error! " << vehicle_type << std::endl;
    sensor_calibration_result.calib_error_code = common::CalibrationErrorCode::EOL_CAMERA_INVALID_PARAMETER;
  }

  /*
      if (cam_direction == "front")
      {
          if (0 <= tag_id && tag_id <= 1)
          {
              if (img_pt.x < img.cols / 2)
              {
                  json_key_name = std::to_string(tag_id) + "_left_" + location;
                  if (!acquire_w_pts(json_key_name, w_pt, sensor_calibration_result))
                      return false;
              }
              else
              {
                  json_key_name = std::to_string(tag_id) + "_right_" + location;
                  if (!acquire_w_pts(json_key_name, w_pt, sensor_calibration_result))
                      return false;
              }
          }
          else
          {
              json_key_name = std::to_string(tag_id) + "_" + location;
              if (!acquire_w_pts(json_key_name, w_pt, sensor_calibration_result))
                      return false;
          }
      }
      else if (cam_direction == "front_left")
      {
          if (0 <= tag_id && tag_id <= 5)
          {
              json_key_name = std::to_string(tag_id) + "_left_" + location;
              if (!acquire_w_pts(json_key_name, w_pt, sensor_calibration_result))
                      return false;
          }
          else
          {
              json_key_name = std::to_string(tag_id) + "_" + location;
              if (!acquire_w_pts(json_key_name, w_pt, sensor_calibration_result))
                      return false;
          }
      }
      else if (cam_direction == "front_right")
      {
          if (0 <= tag_id && tag_id <= 5)
          {
              json_key_name = std::to_string(tag_id) + "_right_" + location;
              if (!acquire_w_pts(json_key_name, w_pt, sensor_calibration_result))
                      return false;
          }
          else
          {
              json_key_name = std::to_string(tag_id) + "_" + location;
              if (!acquire_w_pts(json_key_name, w_pt, sensor_calibration_result))
                      return false;
          }
      }
      else if (cam_direction == "back_left")
      {
          if (2 <= tag_id && tag_id <= 7)
          {
              json_key_name = std::to_string(tag_id) + "_left_" + location;
              if (!acquire_w_pts(json_key_name, w_pt, sensor_calibration_result))
                      return false;
          }
          else
          {
              json_key_name = std::to_string(tag_id) + "_" + location;
              if (!acquire_w_pts(json_key_name, w_pt, sensor_calibration_result))
                      return false;
          }
      }
      else if (cam_direction == "back_right")
      {
          if (2 <= tag_id && tag_id <= 7)
          {
              json_key_name = std::to_string(tag_id) + "_right_" + location;
              if (!acquire_w_pts(json_key_name, w_pt, sensor_calibration_result))
                      return false;
          }
          else
          {
              json_key_name = std::to_string(tag_id) + "_" + location;
              if (!acquire_w_pts(json_key_name, w_pt, sensor_calibration_result))
                      return false;
          }
      }
      else if (cam_direction == "back")
      {

          json_key_name = std::to_string(tag_id) + "_" + location;
          if (!acquire_w_pts(json_key_name, w_pt, sensor_calibration_result))
                  return false;
      }
      else
      {
          LOG(ERROR) << "cam direction " << cam_direction << " invalid!" << std::endl;
          sensor_calibration_result.calib_error_code = common::CalibrationErrorCode::EOL_CAMERA_INVALID_PARAMETER;
          return false;
      }
  */

  return true;
}

std::vector<AprilTags::TagDetection> ExtrinsicEstimator::detect_apriltags(const std::string& cam_name,
                                                                          const cv::Mat& img) {
  cv::Mat image_gray;
  std::vector<AprilTags::TagDetection> detections;
  cv::cvtColor(img, image_gray, cv::COLOR_BGR2GRAY);
  image_gray.convertTo(image_gray, -1, 1, -50);

  std::map<int, std::vector<AprilTags::TagDetection>> id_to_points_map;

  // Iterate through the scales and perform detection
  for (const auto& scale_factor : eol_config_.apriltag_detection_img_scale.at(cam_name)) {
    // Resize the image based on the scale factor
    cv::Mat scaled_img;
    cv::resize(image_gray, scaled_img, cv::Size(), scale_factor, scale_factor, cv::INTER_LINEAR);

    // Perform AprilTag detection on the scaled image
    auto scaled_detections = m_tagDetector_->extractTags_opencv(scaled_img);

    // Scale the detection results back to the original image size
    for (auto& det : scaled_detections) {
      det.cxy.first /= scale_factor;
      det.cxy.second /= scale_factor;
      for (int i = 0; i < 4; ++i) {
        det.p[i].first /= scale_factor;
        det.p[i].second /= scale_factor;
      }

      // Add the detection to the map
      id_to_points_map[det.id].push_back(det);
    }
  }

  // Average the detections for each ID
  detections.clear();
  for (const auto& entry : id_to_points_map) {
    int id = entry.first;
    const auto& dets = entry.second;

    AprilTags::TagDetection avg_det;
    for (const auto& det : dets) {
      avg_det.id = id;
      avg_det.cxy.first += det.cxy.first;
      avg_det.cxy.second += det.cxy.second;
      for (int i = 0; i < 4; ++i) {
        avg_det.p[i].first += det.p[i].first;
        avg_det.p[i].second += det.p[i].second;
      }
    }
    avg_det.cxy.first /= dets.size();
    avg_det.cxy.second /= dets.size();
    for (int i = 0; i < 4; ++i) {
      avg_det.p[i].first /= dets.size();
      avg_det.p[i].second /= dets.size();
    }
    // Add the averaged detection to the final list
    detections.push_back(avg_det);
  }

  if (eol_config_.is_use_SubPix.at(cam_name)) {
    refineDetectionsSubPixAdvanced(detections, image_gray);
  }

  // ==================== test for cv::SubPix ============================
  // // copy the detections to the new vector
  // std::vector<AprilTags::TagDetection> new_detections;
  // for (const auto &detection : detections) {
  //   new_detections.push_back(detection);
  // }
  // refineDetectionsSubPixAdvanced(detections, image_gray);
  // // calculate the diff between the detections and the new detections
  // for (size_t i = 0; i < new_detections.size(); ++i) {
  //   auto det1 = detections[i];
  //   auto det2 = new_detections[i];
  //   // (x^2 + y^2)^0.5
  //   double diff1 = std::sqrt(
  //       std::pow(det1.p[0].first - det2.p[0].first, 2) +
  //       std::pow(det1.p[0].second - det2.p[0].second, 2));
  //   double diff2 = std::sqrt
  //       (std::pow(det1.p[1].first - det2.p[1].first, 2) +
  //        std::pow(det1.p[1].second - det2.p[1].second, 2));
  //   double diff3 = std::sqrt
  //       (std::pow(det1.p[2].first - det2.p[2].first, 2) +
  //        std::pow(det1.p[2].second - det2.p[2].second, 2));
  //   double diff4 = std::sqrt
  //       (std::pow(det1.p[3].first - det2.p[3].first, 2) +
  //        std::pow(det1.p[3].second - det2.p[3].second, 2));

  //   if (diff1 > 1 || diff2 > 1 || diff3 > 1 || diff4 > 1) {
  //     // 写入文件, 在后面附加
  //     std::ofstream outfile("detections.txt", std::ios::app);
  //     outfile << cam_name << " " << std::endl;
  //     outfile << det1.id << " " << diff1 << " " << diff2 << " " << diff3 << "
  //     " << diff4 << std::endl; outfile.close();
  //   }
  // }

  return detections;
}

void ExtrinsicEstimator::refineDetectionsSubPixAdvanced(std::vector<AprilTags::TagDetection>& detections,
                                                        const cv::Mat& gray_image, const cv::Size& winSize,
                                                        int maxIterations, double epsilon) {
  if (detections.empty() || gray_image.empty()) {
    return;
  }

  // 确保输入是灰度图像
  cv::Mat working_image;
  if (gray_image.channels() == 3) {
    cv::cvtColor(gray_image, working_image, cv::COLOR_BGR2GRAY);
  } else {
    working_image = gray_image;
  }

  cv::Size zeroZone(-1, -1);
  cv::TermCriteria criteria(cv::TermCriteria::EPS + cv::TermCriteria::COUNT, maxIterations, epsilon);

  for (auto& detection : detections) {
    std::vector<cv::Point2f> corners;
    corners.reserve(4);  // 只优化角点，不优化中心点

    // 添加四个角点
    for (int i = 0; i < 4; ++i) {
      corners.emplace_back(detection.p[i].first, detection.p[i].second);
    }

    // 边界检查
    bool valid = true;
    for (const auto& corner : corners) {
      if (corner.x < winSize.width || corner.y < winSize.height || corner.x >= working_image.cols - winSize.width ||
          corner.y >= working_image.rows - winSize.height) {
        valid = false;
        break;
      }
    }

    if (!valid) {
      continue;  // 跳过边界附近的点
    }

    // 执行亚像素优化
    cv::cornerSubPix(working_image, corners, winSize, zeroZone, criteria);

    // 更新角点
    for (int i = 0; i < 4; ++i) {
      detection.p[i].first = corners[i].x;
      detection.p[i].second = corners[i].y;
    }

    // 重新计算中心点（基于优化后的角点）
    detection.cxy.first = (corners[0].x + corners[1].x + corners[2].x + corners[3].x) / 4.0;
    detection.cxy.second = (corners[0].y + corners[1].y + corners[2].y + corners[3].y) / 4.0;
  }
}

void ExtrinsicEstimator::add_AprilTagDetections_offset(std::vector<AprilTags::TagDetection>& detections,
                                                       const std::vector<double>& offset) {
  // If all offsets are zero, return immediately
  if (std::all_of(offset.begin(), offset.end(), [](double val) { return val == 0.0; })) {
    return;
  }

  std::random_device rd;
  std::mt19937 gen(rd());
  std::pair<float, float> center;

  LOG(INFO) << "Adding offset to AprilTag detections";
  for (auto& detection : detections) {
    LOG(INFO) << "  AprilTag.id: " << detection.id;
    for (int i = 0; i < 4; ++i) {
      double mean_u = detection.p[i].first;
      double mean_v = detection.p[i].second;
      std::uniform_real_distribution<float> dist_u(mean_u - offset[0], mean_u + offset[0]);
      std::uniform_real_distribution<float> dist_v(mean_v - offset[1], mean_v + offset[1]);

      detection.p[i].first = dist_u(gen);
      detection.p[i].second = dist_v(gen);

      LOG(INFO) << "    Original p[" << i << "] : (" << mean_u << ", " << mean_v << ")";
      LOG(INFO) << "    Updated p[" << i << "]  : (" << detection.p[i].first << ", " << detection.p[i].second << ")";
    }

    // Calculate the center point of the four corners
    center.first = 0.0;
    center.second = 0.0;
    for (int i = 0; i < 4; ++i) {
      center.first += detection.p[i].first;
      center.second += detection.p[i].second;
    }
    detection.cxy.first = center.first / 4.0;
    detection.cxy.second = center.second / 4.0;
  }
}

void ExtrinsicEstimator::draw_debug_img(const cv::Mat& img, const std::string& cam_name,
                                        const std::vector<AprilTags::TagDetection>& detections) {
  cv::Mat img_plot = img.clone();
  cv::Mat image_gray;
  cv::cvtColor(img, image_gray, cv::COLOR_BGR2GRAY);
  cv::equalizeHist(image_gray, image_gray);
  for (auto& det : detections) {
    if (det.hammingDistance > 0) continue;
    det.draw(img_plot);
  }
  // cv::imwrite("./" + cam_name + "_gray.png", image_gray);
  std::string this_output_dir = eol_config_.tmp_output_dir;
  std::string output_path = this_output_dir + cam_name + "_debug.png";
  if (!std::filesystem::exists(this_output_dir)) {
    std::filesystem::create_directory(this_output_dir);
  }

  cv::imwrite(output_path, img_plot);
}

// get world points
bool ExtrinsicEstimator::get_w_pts(const std::string& cam_name, const cv::Mat& img,
                                   std::vector<std::vector<cv::Point3f>>& w_pts,
                                   std::vector<std::vector<cv::Point2f>>& img_pts,
                                   std::vector<std::vector<std::string>>& json_key_names,
                                   const std::vector<AprilTags::TagDetection>& detections) {
  auto& sensor_calibration_result = sensors_calibration_result_.at(cam_name);
  // // print sensor_calibration_result for testing
  // for (const auto& result : sensors_calibration_result_)
  // {
  //     std::cout << "Camera: " << result.first << std::endl;
  //     std::cout << "  Calibration Error Code: " << result.second.calib_error_code << std::endl;
  //     std::cout << "  Calibration Error: " << result.second.calib_error << std::endl;
  //     std::cout << "  Calibration Status: " << result.second.calib_status << std::endl;
  //     std::cout << "  Extrinsic Position: [" << result.second.calib_extrinsic.position.x << ", " <<
  //     result.second.calib_extrinsic.position.y << ", " << result.second.calib_extrinsic.position.z << "]" <<
  //     std::endl; std::cout << "  Extrinsic Orientation: [" << result.second.calib_extrinsic.orientation.w << ", " <<
  //     result.second.calib_extrinsic.orientation.x << ", " << result.second.calib_extrinsic.orientation.y << ", " <<
  //     result.second.calib_extrinsic.orientation.z << "]" << std::endl; std::cout << "  Rotation Angle: [" <<
  //     result.second.calib_extrinsic.rotation_angle.roll << ", " << result.second.calib_extrinsic.rotation_angle.pitch
  //     << ", " << result.second.calib_extrinsic.rotation_angle.yaw << "]" << std::endl;
  // }

  const std::string& cam_direction = cam_name;
  // std::string cam_direction = name2direction(cam_name);
  // print name2direction for testing
  // std::cout << "cam_name: " << cam_name << ", cam_direction: " << cam_direction << std::endl;

  std::vector<cv::Point3f> w_pts_per_tag;
  std::vector<cv::Point2f> img_pts_per_tag;
  std::vector<std::string> json_key_names_per_tag;

  // loop over all the tag-ids to get w_pts and img_pts
  for (auto& det : detections) {
    w_pts_per_tag.clear();
    img_pts_per_tag.clear();
    json_key_names_per_tag.clear();
    // counter-clockwise
    for (int i = 0; i < 4; ++i) {
      cv::Point2f img_pt(det.p[i].first, det.p[i].second);
      cv::Point3f w_pt;
      std::string json_key_name;
      if (!acquire_w_pts(img, cam_direction, det.id, int2location_map_.at(i), json_key_name, img_pt, w_pt,
                         sensor_calibration_result)) {
        return false;
      }
      w_pts_per_tag.push_back(w_pt);
      img_pts_per_tag.push_back(img_pt);
      json_key_names_per_tag.push_back(json_key_name);
    }
    w_pts.push_back(w_pts_per_tag);
    img_pts.push_back(img_pts_per_tag);
    json_key_names.push_back(json_key_names_per_tag);
  }
  return true;
}

void ExtrinsicEstimator::filter_tags_with_prior(const std::string& cam_name, const cv::Mat& img,
                                                std::vector<std::vector<cv::Point3f>>& w_pts,
                                                std::vector<std::vector<cv::Point2f>>& img_pts,
                                                std::vector<std::vector<std::string>>& json_key_names,
                                                std::vector<AprilTags::TagDetection>& detections) {
  auto& sensor_calibration_result = sensors_calibration_result_.at(cam_name);
  if (detections.size() != w_pts.size() || w_pts.size() != img_pts.size() || img_pts.size() != json_key_names.size()) {
    LOG(ERROR) << "filter tags with inequal size for " << cam_name << std::endl;
    LOG(ERROR) << cam_name << " size: " << w_pts.size() << " " << img_pts.size() << " " << json_key_names.size() << " "
               << detections.size() << std::endl;
    sensor_calibration_result.calib_error_code =
        common::CalibrationErrorCode::EOL_CAMERA_COMMON_INTERNAL_CALIBRATION_ERROR;
    return;
  }

  if (detections.size() == 0) {
    LOG(ERROR) << "detections.size() == 0! for " << cam_name << std::endl;
    sensor_calibration_result.calib_error_code = common::CalibrationErrorCode::EOL_CAMERA_NO_CALIBRATION_TARGET_FOUND;
    return;
  }

  Eigen::Quaterniond cam_tf_rfu_q_prior;
  Eigen::Vector3d cam_tf_rfu_t_prior;

  if (prior_q_.find(cam_name) == prior_q_.end() || prior_t_.find(cam_name) == prior_t_.end()) {
    LOG(ERROR) << "no prior for " << cam_name << std::endl;
    sensor_calibration_result.calib_error_code =
        common::CalibrationErrorCode::EOL_CAMERA_COMMON_INTERNAL_CALIBRATION_ERROR;
    return;
  }

  cam_tf_rfu_q_prior = prior_q_.at(cam_name);
  cam_tf_rfu_t_prior = prior_t_.at(cam_name);

  std::vector<std::vector<cv::Point3f>> w_pts_out;
  std::vector<std::vector<cv::Point2f>> img_pts_out;
  std::vector<std::vector<std::string>> json_key_names_out;
  std::vector<AprilTags::TagDetection> detections_out;

  if (cam_.find(cam_name) == cam_.end()) {
    LOG(ERROR) << "no cam model for " << cam_name << std::endl;
    sensor_calibration_result.calib_error_code =
        common::CalibrationErrorCode::EOL_CAMERA_COMMON_INTERNAL_CALIBRATION_ERROR;
    return;
  }

  auto cam = cam_.at(cam_name);  // camera model

  cv::Mat img_plot;
  if (eol_config_.is_debug) {
    LOG(ERROR) << "plot reproj_prior_" << cam_name << ".png "
               << "for debug" << std::endl;
    img_plot = img.clone();
  }

  for (int i = 0; i < static_cast<int>(detections.size()); ++i) {
    auto det = detections[i];
    auto w_pts_per_tag = w_pts[i];  // the points on rfu coordinate system
    auto img_pts_per_tag = img_pts[i];
    auto json_key_names_per_tag = json_key_names[i];

    // if (cam_name == "cam_front_120" && (det.id == 0 || det.id == 1))
    //     continue;

    // LOG(ERROR) << "cam_tf_rfu_q_prior: " << cam_tf_rfu_q_prior.w() << " " << cam_tf_rfu_q_prior.x() << " "
    //       << cam_tf_rfu_q_prior.y() << " " << cam_tf_rfu_q_prior.z() << std::endl;
    // LOG(ERROR) << "cam_tf_rfu_t_prior: " << cam_tf_rfu_t_prior[0] << " " << cam_tf_rfu_t_prior[1] << " " <<
    // cam_tf_rfu_t_prior[2] << std::endl;

    std::vector<cv::Point2f> img_pts_proj;
    for (int j = 0; j < 4; ++j) {
      Eigen::Vector3d w_pt_proj =
          cam_tf_rfu_q_prior * Eigen::Vector3d(w_pts_per_tag[j].x, w_pts_per_tag[j].y, w_pts_per_tag[j].z) +
          cam_tf_rfu_t_prior;  // on camera coordinate system

      // LOG(ERROR) << "w_pt_per_tag: " << w_pts_per_tag[j].x << " " << w_pts_per_tag[j].y << " " << w_pts_per_tag[j].z
      // << std::endl; LOG(ERROR) << "w_pt_proj: " << w_pt_proj[0] << " " << w_pt_proj[1] << " " << w_pt_proj[2] <<
      // std::endl;

      Eigen::Vector2d img_pt_proj;
      cam->spaceToPlane(w_pt_proj, img_pt_proj);  // transform from camera coordinate system to image coordinate system
      img_pts_proj.push_back(
          cv::Point2f(img_pt_proj[0], img_pt_proj[1]));  // The priori calibroom point projection results

      // plot prior_proj and detect points for debug
      if (eol_config_.is_debug && !img_plot.empty()) {
        cv::circle(img_plot, cv::Point2f(img_pt_proj[0], img_pt_proj[1]), 8, cv::Scalar(0, 0, 255), 4);  // red for proj
        cv::circle(img_plot, img_pts_per_tag[j], 8, cv::Scalar(255, 0, 0), 4);  // blue for detect
      }
    }

    // calculate the **distance** between the priori projection and the detected points to filter out the tags
    // LOG(INFO) << "cal distance for AprilTag id " << det.id << std::endl;
    if (!overlap_tag(img_pts_proj, img_pts_per_tag, eol_config_.overlap_tag_threshold.at(cam_name))) {
      LOG(ERROR) << "filtering out AprilTag id ** " << det.id << " ** in *** " << cam_name
                 << " *** for proj distance out of range!" << std::endl;
      continue;
    }

    /*
    // ****** Can not use IOU_filter, because sometimes the priori projection don't have overlap with deteced points
    *******
    // LOG(ERROR) << "TODO: we need to use the **IOU** to filter out the tags" << std::endl;
    // TODO: we need to use the **IOU** to filter out the tags
    // calculate **IOU** between the priori projection and the detected points to filter out the tags
    // double iou  = overlap_tag_iou(img_pts_proj, img_pts_per_tag);
    // if (iou < eol_config_.overlap_tag_threshold)
    // {
    //     LOG(ERROR) << "filtering out AprilTag id **" << det.id << "** in ***" << cam_name << "***" <<std::endl;
    //     LOG(ERROR) << "iou < eol_config_.overlap_tag_threshold : " << iou << " < " <<
    eol_config_.overlap_tag_threshold << std::endl;
    //     continue;
    // }
    */

    w_pts_out.push_back(w_pts_per_tag);
    img_pts_out.push_back(img_pts_per_tag);
    json_key_names_out.push_back(json_key_names_per_tag);
    detections_out.push_back(det);
  }

  // test: plot the reprojection results
  if (eol_config_.is_debug) {
    std::string this_output_dir = eol_config_.tmp_output_dir;
    // LOG(ERROR) << this_output_dir << "*****************************************" << std::endl;
    std::string output_img_path = this_output_dir + "reproj_prior_" + cam_name + ".jpg";
    if (!std::filesystem::exists(this_output_dir)) {
      std::filesystem::create_directory(this_output_dir);
    }

    if (img_plot.cols > 1920 && img_plot.rows > 1080) {
      cv::resize(img_plot, img_plot, cv::Size(img_plot.cols / 2, img_plot.rows / 2));
    }
    cv::imwrite(output_img_path, img_plot);
  }

  if (detections_out.size() != w_pts_out.size() || w_pts_out.size() != img_pts_out.size() ||
      img_pts_out.size() != json_key_names_out.size()) {
    LOG(ERROR) << cam_name << " size: " << w_pts_out.size() << " " << img_pts_out.size() << " "
               << json_key_names_out.size() << " " << detections_out.size() << std::endl;
    sensor_calibration_result.calib_error_code =
        common::CalibrationErrorCode::EOL_CAMERA_COMMON_INTERNAL_CALIBRATION_ERROR;
    return;
  }

  // no valid tag detected
  if (detections_out.size() == 0) {
    LOG(ERROR) << "detections.size() == 0! for " << cam_name << std::endl;
    sensor_calibration_result.calib_error_code = common::CalibrationErrorCode::EOL_CAMERA_NO_CALIBRATION_TARGET_FOUND;
    return;
  }

  w_pts = w_pts_out;
  img_pts = img_pts_out;
  json_key_names = json_key_names_out;
  detections = detections_out;
}

bool ExtrinsicEstimator::check_tags_integrity(const std::string& cam_name, const cv::Mat& img,
                                              const std::vector<AprilTags::TagDetection>& detections) {
  auto& sensor_calibration_result = sensors_calibration_result_.at(cam_name);
  // std::string cam_direction = name2direction(cam_name);
  // std::string cam_direction = cam_name

  // pre-check all the detected tag-ids
  std::vector<int> detected_ids;
  for (auto& det : detections) {
    if (det.hammingDistance > 0) continue;
    detected_ids.push_back(det.id);
  }
  std::sort(detected_ids.begin(), detected_ids.end());

  // whether invalid id were detected
  if (!std::includes(direction_to_full_ids_map_.at(cam_name).begin(), direction_to_full_ids_map_.at(cam_name).end(),
                     detected_ids.begin(), detected_ids.end())) {
    // LOG(ERROR) << "invalid ids were detected for " << cam_name << std::endl;
    // sensor_calibration_result.calib_error_code =
    //     common::CalibrationErrorCode::EOL_CAMERA_TOO_MUCH_OR_LITTLE_CALIBRATION_TARGETS_FOUND;
    // // draw_debug_img(img, cam_name, detections);
    // return false;

    LOG(INFO) << "invalid ids were detected for " << cam_name << std::endl;
    std::string out;
    for (auto id : detected_ids) {
      out += std::to_string(id) + " ";
    }
    LOG(INFO) << "detected_ids: " << out << std::endl;
    out = "";
    for (auto id : direction_to_full_ids_map_.at(cam_name)) {
      out += std::to_string(id) + " ";
    }
    LOG(INFO) << "full_ids: " << out << std::endl;

    LOG(INFO) << "================= Filtering out invalid ids =================";
    // 从 detected_ids中去除不在 full_ids 中的id
    for (auto it = detected_ids.begin(); it != detected_ids.end();) {
      if (std::find(direction_to_full_ids_map_.at(cam_name).begin(), direction_to_full_ids_map_.at(cam_name).end(),
                    *it) == direction_to_full_ids_map_.at(cam_name).end()) {
        it = detected_ids.erase(it);
      } else {
        ++it;
      }
    }

    // print detected_ids again
    out = "";
    for (auto id : detected_ids) {
      out += std::to_string(id) + " ";
    }
    LOG(INFO) << "detected_ids after filter: " << out << std::endl;
  }

  // // whether all the necessary ids were detected
  // if (!std::includes(detected_ids.begin(), detected_ids.end(), direction_to_necessary_ids_map_.at(cam_name).begin(),
  // direction_to_necessary_ids_map_.at(cam_name).end()))
  // {
  //     LOG(ERROR) << "all necessary ids detected fail for " << cam_name << std::endl;
  //     // sensor_calibration_result.calib_error_code =
  //     mach_calib::CalibrationErrorCode::EOL_CAMERA_EOL_CAMERA_TOO_MUCH_OR_LITTLE_CALIBRATION_TARGETS_FOUND;
  //     // draw_debug_img(img, cam_name, detections);
  //     // return false;
  // }

  std::set<int> detected_ids_set;
  for (auto& det : detections) {
    if (det.hammingDistance > 0) continue;
    detected_ids_set.insert(det.id);
  }

  // Check if the necessary IDs are detected
  const auto& necessary_ids = direction_to_necessary_ids_map_.at(cam_name);
  for (const auto& group : necessary_ids) {
    if (group.size() == 0) {
      continue;
    }

    bool group_detected = false;
    for (int id : group) {
      if (detected_ids_set.find(id) != detected_ids_set.end()) {
        group_detected = true;
        break;
      }
    }
    if (!group_detected) {
      LOG(ERROR) << "Necessary tag group not fully detected for " << cam_name << std::endl;
      sensor_calibration_result.calib_error_code =
          common::CalibrationErrorCode::EOL_CAMERA_TOO_MUCH_OR_LITTLE_CALIBRATION_TARGETS_FOUND;
      return false;
    }
  }

  /*
      std::set<int> detected_ids_set;
      for (auto det : detections)
      {
          if (det.hammingDistance > 0)
              continue;
          detected_ids_set.insert(det.id);
      }
      // for cam_front & cam_back, at least one tag has been detected for one monotor
      if (cam_direction == "front")
      {
          if ((detected_ids_set.find(8) == detected_ids_set.end() && detected_ids_set.find(9) == detected_ids_set.end())
     || (detected_ids_set.find(10) == detected_ids_set.end() && detected_ids_set.find(11) == detected_ids_set.end()) ||
             (detected_ids_set.find(12) == detected_ids_set.end() && detected_ids_set.find(13) ==
     detected_ids_set.end()) || (detected_ids_set.find(14) == detected_ids_set.end() && detected_ids_set.find(15) ==
     detected_ids_set.end()))
          {
              LOG(ERROR) << "tag detection unbalanced! for " << cam_name << std::endl;
              sensor_calibration_result.calib_error_code = common::CalibrationErrorCode::DETECT_ERROR;
              return false;
          }
      }
      else if (cam_direction == "back")
      {
          if ((detected_ids_set.find(16) == detected_ids_set.end() && detected_ids_set.find(17) ==
     detected_ids_set.end()) || (detected_ids_set.find(18) == detected_ids_set.end() && detected_ids_set.find(19) ==
     detected_ids_set.end()) || (detected_ids_set.find(20) == detected_ids_set.end() && detected_ids_set.find(21) ==
     detected_ids_set.end()))
          {
              LOG(ERROR) << "tag detection unbalanced! for " << cam_name << std::endl;
              sensor_calibration_result.calib_error_code = common::CalibrationErrorCode::DETECT_ERROR;
              return false;
          }
      }

      // for side cam, at least one near tag + one far tag have been detected
      if (cam_direction == "front_left")
      {
          if ((detected_ids_set.find(0) == detected_ids_set.end() &&
              detected_ids_set.find(1) == detected_ids_set.end() &&
              detected_ids_set.find(2) == detected_ids_set.end() &&
              detected_ids_set.find(3) == detected_ids_set.end() &&
              detected_ids_set.find(4) == detected_ids_set.end() &&
              detected_ids_set.find(5) == detected_ids_set.end()) ||
              (detected_ids_set.find(8) == detected_ids_set.end() &&
              detected_ids_set.find(9) == detected_ids_set.end()) ||
              (detected_ids_set.find(10) == detected_ids_set.end() &&
              detected_ids_set.find(11) == detected_ids_set.end()))
          {
              LOG(ERROR) << "tag detection unbalanced! for " << cam_name << std::endl;
              sensor_calibration_result.calib_error_code = common::CalibrationErrorCode::DETECT_ERROR;
              return false;
          }
      }
      else if (cam_direction == "front_right")
      {
          if ((detected_ids_set.find(0) == detected_ids_set.end() &&
              detected_ids_set.find(1) == detected_ids_set.end() &&
              detected_ids_set.find(2) == detected_ids_set.end() &&
              detected_ids_set.find(3) == detected_ids_set.end() &&
              detected_ids_set.find(4) == detected_ids_set.end() &&
              detected_ids_set.find(5) == detected_ids_set.end()) ||
              (detected_ids_set.find(12) == detected_ids_set.end() &&
              detected_ids_set.find(13) == detected_ids_set.end()) ||
              (detected_ids_set.find(14) == detected_ids_set.end() &&
              detected_ids_set.find(15) == detected_ids_set.end()))
          {
              LOG(ERROR) << "tag detection unbalanced! for " << cam_name << std::endl;
              sensor_calibration_result.calib_error_code = common::CalibrationErrorCode::DETECT_ERROR;
              return false;
          }
      }
      else if (cam_direction == "back_left")
      {
          if ((detected_ids_set.find(2) == detected_ids_set.end() &&
              detected_ids_set.find(3) == detected_ids_set.end() &&
              detected_ids_set.find(4) == detected_ids_set.end() &&
              detected_ids_set.find(5) == detected_ids_set.end() &&
              detected_ids_set.find(6) == detected_ids_set.end() &&
              detected_ids_set.find(7) == detected_ids_set.end()) ||
              (detected_ids_set.find(20) == detected_ids_set.end() &&
              detected_ids_set.find(21) == detected_ids_set.end()))
          {
              LOG(ERROR) << "tag detection unbalanced! for " << cam_name << std::endl;
              sensor_calibration_result.calib_error_code = common::CalibrationErrorCode::DETECT_ERROR;
              return false;
          }
      }
      else if (cam_direction == "back_right")
      {
          if ((detected_ids_set.find(2) == detected_ids_set.end() &&
              detected_ids_set.find(3) == detected_ids_set.end() &&
              detected_ids_set.find(4) == detected_ids_set.end() &&
              detected_ids_set.find(5) == detected_ids_set.end() &&
              detected_ids_set.find(6) == detected_ids_set.end() &&
              detected_ids_set.find(7) == detected_ids_set.end()) ||
              (detected_ids_set.find(16) == detected_ids_set.end() &&
              detected_ids_set.find(17) == detected_ids_set.end()) ||
              detected_ids_set.find(18) == detected_ids_set.end() &&
              detected_ids_set.find(19) == detected_ids_set.end())
          {
              LOG(ERROR) << "tag detection unbalanced! for " << cam_name << std::endl;
              sensor_calibration_result.calib_error_code = common::CalibrationErrorCode::DETECT_ERROR;
              return false;
          }
      }
  */

  return true;
}

void ExtrinsicEstimator::draw_reproj_error(const std::string& output_dir) {
  std::vector<cv::Mat> imgs_to_save;
  std::vector<std::string> imgs_to_save_name;
  for (auto it = camNames_.begin(); it != camNames_.end(); ++it) {
    std::string this_cam_name = *it;
    if (sensors_calibration_result_.find(this_cam_name) == sensors_calibration_result_.end()) continue;
    CalibrationResult& this_sensor_calibration_result = sensors_calibration_result_.at(this_cam_name);

    if (imgs_.find(this_cam_name) != imgs_.end()) {
      cv::Mat img_plot = imgs_.at(this_cam_name).clone();
      // cv::Mat img_plot;
      // cv::resize(imgs_.at(this_cam_name), img_plot, cv::Size(), 0.5, 0.5);
      auto& wPts = wPts_.at(this_cam_name);
      auto& imgPts = imgPts_.at(this_cam_name);
      auto& cam = cam_.at(this_cam_name);
      for (int i = 0; i < static_cast<int>(wPts.size()); ++i) {
        for (int j = 0; j < 4; ++j) {
          // red for detected pt
          cv::circle(img_plot, imgPts[i][j], 8, cv::Scalar(0, 0, 255), 4);
          // blue for reproj
          Eigen::Vector3d w_pt(wPts[i][j].x, wPts[i][j].y, wPts[i][j].z);
          Eigen::Vector3d c_pt = get_c_tf_w_q(this_cam_name) * w_pt + get_c_tf_w_t(this_cam_name);
          Eigen::Vector2d uv_proj;
          cam->spaceToPlane(c_pt, uv_proj);
          cv::circle(img_plot, cv::Point2f(uv_proj[0], uv_proj[1]), 8, cv::Scalar(255, 0, 0), 4);
        }
      }

      std::string err_string = "reproj error: " + std::to_string(cal_reproj_error(this_cam_name));
      cv::putText(img_plot, err_string, cv::Point(650, 90), cv::FONT_HERSHEY_SIMPLEX, 1, cv::Scalar(0, 0, 255), 1);
      std::string img_name = output_dir;
      img_name.append("/").append(this_cam_name).append("_reproj.png");
      imgs_to_save_name.push_back(img_name);
      imgs_to_save.push_back(img_plot);
    }
  }

#pragma omp parallel for
  for (int i = 0; i < static_cast<int>(imgs_to_save_name.size()); ++i) {
    cv::imwrite(imgs_to_save_name[i], imgs_to_save[i]);
  }
}

double ExtrinsicEstimator::cal_reproj_error(const std::string& cam_name) {
  if (camNames_.find(cam_name) == camNames_.end()) {
    return -1;
  }

  if (wPts_.find(cam_name) == wPts_.end()) {
    return -1;
  }

  if (imgPts_.find(cam_name) == imgPts_.end()) {
    return -1;
  }

  if (cam_.find(cam_name) == cam_.end()) {
    return -1;
  }

  double reproj_err = 0;
  int cnt = 0;
  std::vector<std::vector<cv::Point3f>>& wPts = wPts_.at(cam_name);
  std::vector<std::vector<cv::Point2f>>& imgPts = imgPts_.at(cam_name);
  camodocal::CameraPtr& cam = cam_.at(cam_name);
  for (int i = 0; i < static_cast<int>(wPts.size()); ++i) {
    for (int j = 0; j < 4; ++j) {
      Eigen::Vector3d w_pt(wPts[i][j].x, wPts[i][j].y, wPts[i][j].z);
      Eigen::Vector3d c_pt = get_c_tf_w_q(cam_name) * w_pt + get_c_tf_w_t(cam_name);
      Eigen::Vector2d uv_proj;
      cam->spaceToPlane(c_pt, uv_proj);
      reproj_err += (sqrt(pow(uv_proj[0] - imgPts[i][j].x, 2) + pow(uv_proj[1] - imgPts[i][j].y, 2)));
      cnt++;
    }
  }
  reproj_err /= (cnt + 1e-12);
  return reproj_err;
}

std::vector<cv::Point2f> ExtrinsicEstimator::undistort_points(const std::string& cam_name,
                                                              const std::vector<cv::Point2f>& imgPts) {
  std::vector<cv::Point2f> outPts;
  if (cam_.find(cam_name) == cam_.end()) return outPts;
  auto& cam = cam_.at(cam_name);

  for (auto imgPt : imgPts) {
    outPts.push_back(undistort_point(imgPt, cam));
  }
  return outPts;
}

cv::Point2f ExtrinsicEstimator::undistort_point(const cv::Point2f& imgPt, const camodocal::CameraPtr& cam) {
  cv::Point2f outPt;
  Eigen::Vector2d p(imgPt.x, imgPt.y);
  Eigen::Vector3d P;
  cam->liftProjective(p, P);
  double fx = cam->get_camera_intrinsic().at<double>(0, 0);
  double fy = cam->get_camera_intrinsic().at<double>(1, 1);
  double cx = cam->get_camera_intrinsic().at<double>(0, 2);
  double cy = cam->get_camera_intrinsic().at<double>(1, 2);
  Eigen::Vector3d proj_p;
  proj_p[0] = fx * P[0] + cx * P[2];
  proj_p[1] = fy * P[1] + cy * P[2];
  proj_p[2] = P[2];
  outPt.x = proj_p[0] / proj_p[2];
  outPt.y = proj_p[1] / proj_p[2];
  return outPt;
}

double ExtrinsicEstimator::calEpipolarError(const cv::Mat& F, const cv::Point2f& pt1, const cv::Point2f& pt2) {
  // 极线 l' = F * x1，计算点 pt2 到 l' 的距离
  cv::Matx33d F_new(F);
  cv::Vec3d pt1_new(pt1.x, pt1.y, 1);
  cv::Vec3d line = F_new * pt1_new;
  double a = line(0), b = line(1), c = line(2);
  double dist1 = abs(a * pt2.x + b * pt2.y + c) / sqrt(a * a + b * b);

  // 极线 l = F^T * x2，计算点 pt1 到 l 的距离
  cv::Matx33d F_transpose(cv::Mat(F.t()));
  cv::Vec3d pt2_new(pt2.x, pt2.y, 1);
  cv::Vec3d line_transpose = F_transpose * pt2_new;
  double a_transpose = line_transpose(0), b_transpose = line_transpose(1), c_transpose = line_transpose(2);
  double dist2 = abs(a_transpose * pt1.x + b_transpose * pt1.y + c_transpose) /
                 sqrt(a_transpose * a_transpose + b_transpose * b_transpose);

  // 返回平均距离
  return (dist1 + dist2) / 2;
}

void ExtrinsicEstimator::print_epipolar_error() {
  for (auto it0 = camNames_.begin(); it0 != camNames_.end(); ++it0) {
    std::string cam0_name = *it0;
    for (auto it1 = it0; it1 != camNames_.end(); ++it1) {
      std::string cam1_name = *it1;
      if (cam0_name == cam1_name) continue;
      double epipolar_error = cal_epipolar_error(cam0_name, cam1_name);
      if (epipolar_error != -1) {
        LOG(INFO) << cam0_name << " - " << cam1_name << " epipolar error: " << cal_epipolar_error(cam0_name, cam1_name)
                  << std::endl;
      }
    }
  }
}

double ExtrinsicEstimator::cal_epipolar_error(const std::string& cam0_name, const std::string& cam1_name) {
  if (sensors_calibration_result_.find(cam0_name) == sensors_calibration_result_.end() ||
      sensors_calibration_result_.find(cam1_name) == sensors_calibration_result_.end()) {
    return -1;
  }

  if (json_key_names_.find(cam0_name) == json_key_names_.end() ||
      json_key_names_.find(cam1_name) == json_key_names_.end()) {
    return -1;
  }

  std::vector<std::string> json_key_names_cam0_flatten;
  for (int i = 0; i < static_cast<int>(json_key_names_.at(cam0_name).size()); ++i) {
    json_key_names_cam0_flatten.insert(json_key_names_cam0_flatten.end(), json_key_names_.at(cam0_name)[i].begin(),
                                       json_key_names_.at(cam0_name)[i].end());
  }
  std::vector<std::string> json_key_names_cam1_flatten;
  for (int i = 0; i < static_cast<int>(json_key_names_.at(cam1_name).size()); ++i) {
    json_key_names_cam1_flatten.insert(json_key_names_cam1_flatten.end(), json_key_names_.at(cam1_name)[i].begin(),
                                       json_key_names_.at(cam1_name)[i].end());
  }

  std::vector<std::string> same_key_names;
  std::unordered_set<std::string> name_set(json_key_names_cam0_flatten.begin(), json_key_names_cam0_flatten.end());
  for (auto& name : json_key_names_cam1_flatten) {
    if (name_set.count(name)) same_key_names.push_back(name);
  }
  if (same_key_names.size() < 8) return -1;

  std::vector<cv::Point2f> undistorted_img_pts_i, undistorted_img_pts_j;
  auto& cam0 = cam_.at(cam0_name);
  auto& cam1 = cam_.at(cam1_name);
  for (auto& name : same_key_names) {
    auto it0 = std::find(json_key_names_cam0_flatten.begin(), json_key_names_cam0_flatten.end(), name);
    int id0 = std::distance(json_key_names_cam0_flatten.begin(), it0);
    undistorted_img_pts_i.push_back(undistort_point(imgPts_.at(cam0_name)[id0 / 4][id0 % 4], cam0));
    auto it1 = std::find(json_key_names_cam1_flatten.begin(), json_key_names_cam1_flatten.end(), name);
    int id1 = std::distance(json_key_names_cam1_flatten.begin(), it1);
    undistorted_img_pts_j.push_back(undistort_point(imgPts_.at(cam1_name)[id1 / 4][id1 % 4], cam1));
  }

  // 计算F
  Eigen::Quaterniond cj_tf_ci_q = get_c_tf_w_q(cam1_name) * get_c_tf_w_q(cam0_name).inverse();
  Eigen::Vector3d cj_tf_ci_t =
      get_c_tf_w_q(cam1_name) * (get_c_tf_w_q(cam0_name).inverse() * (-get_c_tf_w_t(cam0_name))) +
      get_c_tf_w_t(cam1_name);
  cv::Mat EssentialMatrix = SE3d_SkewSymmetric(cj_tf_ci_q, cj_tf_ci_t);
  // // F = K^(-T)*t_x*R*K^-1
  cv::Mat FundamentalMatrix =
      cam1->get_camera_intrinsic().inv().t() * EssentialMatrix * cam0->get_camera_intrinsic().inv();
  FundamentalMatrix /= FundamentalMatrix.at<double>(2, 2);

  // // cal err
  double total_err = 0;
  int cnt = 0;
  for (int i = 0; i < static_cast<int>(undistorted_img_pts_i.size()); ++i) {
    total_err += calEpipolarError(FundamentalMatrix, undistorted_img_pts_i[i], undistorted_img_pts_j[i]);
    cnt += 1;
  }

  return total_err / (cnt + 1e-12);
}

void ExtrinsicEstimator::printf_result() {
  LOG(INFO) << std::endl;
  LOG(INFO) << "=================== extrinsic calib result ===================" << std::endl;
  for (auto it = sensors_calibration_result_.begin(); it != sensors_calibration_result_.end(); ++it) {
    // rfu_tf_cam
    Eigen::Quaterniond calib_q = get_w_tf_c_q(it->first);
    auto ypr = R2ypr(calib_q);
    auto t = get_w_tf_c_t(it->first);

    LOG(INFO) << "rfu_tf_" << it->first << std::endl;
    LOG(INFO) << "  ypr         : " << ypr.x() << " " << ypr.y() << " " << ypr.z() << std::endl;
    LOG(INFO) << "  tx ty tz    : " << t.x() << " " << t.y() << " " << t.z() << std::endl;
    LOG(INFO) << "  error code  : " << static_cast<uint32_t>(it->second.calib_error_code) << std::endl;
    LOG(INFO) << "  reproj error: " << std::get<double>(it->second.custom_fields.at("camera_calib_reproj_error"))
              << std::endl;
  }
}

bool ExtrinsicEstimator::solve_pnp(const std::string& cam_name, const std::vector<std::vector<cv::Point3f>>& w_pts,
                                   const std::vector<std::vector<cv::Point2f>>& img_pts,
                                   std::vector<std::vector<cv::Point3f>>& ray_pts, AxisAngle& ci_tf_w) {
  auto& sensor_calibration_result = sensors_calibration_result_.at(cam_name);
  if (w_pts.size() != img_pts.size() || img_pts.size() == 0) {
    sensor_calibration_result.calib_error_code =
        common::CalibrationErrorCode::EOL_CAMERA_COMMON_INTERNAL_CALIBRATION_ERROR;
    return false;
  }

  std::vector<std::vector<cv::Point2f>> un_imgpts;
  if (cam_.find(cam_name) == cam_.end()) {
    LOG(ERROR) << "find " << cam_name << " in cam_ fail!" << std::endl;
    sensor_calibration_result.calib_error_code = common::CalibrationErrorCode::EOL_CAMERA_INVALID_PARAMETER;
    return false;
  }
  auto this_cam = cam_.at(cam_name);
  double fx = this_cam->get_camera_intrinsic().at<double>(0, 0);
  double fy = this_cam->get_camera_intrinsic().at<double>(1, 1);
  double cx = this_cam->get_camera_intrinsic().at<double>(0, 2);
  double cy = this_cam->get_camera_intrinsic().at<double>(1, 2);

  for (int i = 0; i < static_cast<int>(img_pts.size()); ++i) {
    std::vector<cv::Point3f> ray_pts_per_tag;
    std::vector<cv::Point2f> un_imgpts_per_tag;
    for (int j = 0; j < 4; ++j) {
      auto p = img_pts[i][j];
      Eigen::Vector2d uv(p.x, p.y);
      Eigen::Vector3d ray;
      this_cam->liftProjective(uv, ray);

      if (isnan(ray[0]) || isnan(ray[1]) || isnan(ray[2])) {
        LOG(ERROR) << "ray isnan for " + cam_name + "!" << std::endl;
        sensor_calibration_result.calib_error_code =
            common::CalibrationErrorCode::EOL_CAMERA_COMMON_INTERNAL_CALIBRATION_ERROR;
        return false;
      }
      ray_pts_per_tag.push_back(cv::Point3f(ray[0], ray[1], ray[2]));
      Eigen::Vector3d proj_p;
      proj_p[0] = fx * ray[0] + cx * ray[2];
      proj_p[1] = fy * ray[1] + cy * ray[2];
      proj_p[2] = ray[2];
      un_imgpts_per_tag.push_back(cv::Point2f(proj_p[0] / proj_p[2], proj_p[1] / proj_p[2]));
    }
    ray_pts.push_back(ray_pts_per_tag);
    un_imgpts.push_back(un_imgpts_per_tag);
  }

  // flatten w_pts and un_imgpts for pnp
  std::vector<cv::Point3f> w_pts_flatten;
  std::vector<cv::Point2f> un_imgpts_flatten;
  for (const auto& inner_w_pts : w_pts) {
    w_pts_flatten.insert(w_pts_flatten.end(), inner_w_pts.begin(), inner_w_pts.end());
  }
  for (const auto& inner_un_imgpts : un_imgpts) {
    un_imgpts_flatten.insert(un_imgpts_flatten.end(), inner_un_imgpts.begin(), inner_un_imgpts.end());
  }

  // solve PNP
  cv::Mat r_vec, t_vec;
  cv::solvePnP(w_pts_flatten, un_imgpts_flatten, this_cam->get_camera_intrinsic(), cv::Mat(), r_vec, t_vec, false,
               cv::SOLVEPNP_EPNP);

  // save pnp result
  ci_tf_w.r_vec[0] = r_vec.at<double>(0, 0);
  ci_tf_w.r_vec[1] = r_vec.at<double>(1, 0);
  ci_tf_w.r_vec[2] = r_vec.at<double>(2, 0);
  ci_tf_w.t_vec[0] = t_vec.at<double>(0, 0);
  ci_tf_w.t_vec[1] = t_vec.at<double>(1, 0);
  ci_tf_w.t_vec[2] = t_vec.at<double>(2, 0);

  return true;
}

void ExtrinsicEstimator::feed_img(const cv::Mat& img, const std::string& cam_name,
                                  const CameraIntrinsicParam& IntrinsicParam, const ExtrinsicPose& StructureParam) {
  Eigen::Quaterniond cam_tf_rfu_structure_q =
      Eigen::Quaterniond(StructureParam.orientation.w, StructureParam.orientation.x, StructureParam.orientation.y,
                         StructureParam.orientation.z);
  Eigen::Vector3d cam_tf_rfu_structure_t(StructureParam.position.x, StructureParam.position.y,
                                         StructureParam.position.z);

  cv::Mat r_vec_structure, t_vec_structure;  // axis-angle, t_vec: 3x1
  r_vec_structure.create(3, 1, CV_64F);
  t_vec_structure.create(3, 1, CV_64F);
  r_vec_structure = quat2axisangle(cam_tf_rfu_structure_q.x(), cam_tf_rfu_structure_q.y(), cam_tf_rfu_structure_q.z(),
                                   cam_tf_rfu_structure_q.w());
  t_vec_structure.at<double>(0, 0) = cam_tf_rfu_structure_t.x();
  t_vec_structure.at<double>(1, 0) = cam_tf_rfu_structure_t.y();
  t_vec_structure.at<double>(2, 0) = cam_tf_rfu_structure_t.z();

  // // save structure param for pnp & comparison
  Eigen::Vector3d euler_angles = cam_tf_rfu_structure_q.toRotationMatrix().eulerAngles(2, 1, 0);  // ZYX order
  // LOG(INFO) << cam_name << " StructureParam.orientation -> (RotX, RotY, RotZ): " << euler_angles[2] / M_PI * 180 << "
  // "
  //           << euler_angles[1] / M_PI * 180 << " " << euler_angles[0] / M_PI * 180 << std::endl;

  structure_param_map_ypr_.insert(
      std::make_pair(cam_name, Eigen::Vector3d(euler_angles[2], euler_angles[1], euler_angles[0])));  // ZYX order
  structure_param_map_t_.insert(std::make_pair(
      cam_name, Eigen::Vector3d(StructureParam.position.x, StructureParam.position.y, StructureParam.position.z)));

  // init sensors_calibration_result
  CalibrationResult sensor_calibration_result = CalibrationResult();
  sensor_calibration_result.custom_fields["camera_calib_reproj_error"] = NAN;
  // sensor_calibration_result.calib_error_code = common::CalibrationErrorCode::EOL_CAMERA_NO_CALIBRATION;
  // sensor_calibration_result.calib_error = 0;
  // sensor_calibration_result.calib_status = 0;

  if (prior_q_.find(cam_name) != prior_q_.end() && prior_t_.find(cam_name) != prior_t_.end()) {
    Eigen::Quaterniond cam_tf_rfu_prior_q = prior_q_.at(cam_name);
    Eigen::Vector3d cam_tf_rfu_prior_t = prior_t_.at(cam_name);
    sensor_calibration_result.calib_extrinsic.position.x = cam_tf_rfu_prior_t[0];
    sensor_calibration_result.calib_extrinsic.position.y = cam_tf_rfu_prior_t[1];
    sensor_calibration_result.calib_extrinsic.position.z = cam_tf_rfu_prior_t[2];
    sensor_calibration_result.calib_extrinsic.orientation.w = cam_tf_rfu_prior_q.w();
    sensor_calibration_result.calib_extrinsic.orientation.x = cam_tf_rfu_prior_q.x();
    sensor_calibration_result.calib_extrinsic.orientation.y = cam_tf_rfu_prior_q.y();
    sensor_calibration_result.calib_extrinsic.orientation.z = cam_tf_rfu_prior_q.z();

    // Eigen::Vector3d ypr_prior = R2ypr(cam_tf_rfu_prior_q);
    // sensor_calibration_result.calib_extrinsic.rotation_angle.roll = ypr_prior[2];
    // sensor_calibration_result.calib_extrinsic.rotation_angle.pitch = ypr_prior[1];
    // sensor_calibration_result.calib_extrinsic.rotation_angle.yaw = ypr_prior[0];
  } else {
    LOG(ERROR) << "prior q or t not found for " << cam_name << std::endl;
    sensor_calibration_result.calib_extrinsic.position.x = -1;
    sensor_calibration_result.calib_extrinsic.position.y = -1;
    sensor_calibration_result.calib_extrinsic.position.z = -1;
    sensor_calibration_result.calib_extrinsic.orientation.w = -1;
    sensor_calibration_result.calib_extrinsic.orientation.x = -1;
    sensor_calibration_result.calib_extrinsic.orientation.y = -1;
    sensor_calibration_result.calib_extrinsic.orientation.z = -1;
    // sensor_calibration_result.calib_extrinsic.rotation_angle.roll = -1;
    // sensor_calibration_result.calib_extrinsic.rotation_angle.pitch = -1;
    // sensor_calibration_result.calib_extrinsic.rotation_angle.yaw = -1;
  }

  // feed input
  feed_img(img, cam_name, IntrinsicParam, sensor_calibration_result);

  sensors_calibration_result_.insert(std::make_pair(cam_name, sensor_calibration_result));
}

void ExtrinsicEstimator::feed_img(const cv::Mat& img, const std::string& cam_name,
                                  const CameraIntrinsicParam& IntrinsicParam,
                                  CalibrationResult& sensor_calibration_result) {
  if (img.empty()) {
    LOG(FATAL) << "img empty for " << cam_name << std::endl;
    sensor_calibration_result.calib_error_code = common::CalibrationErrorCode::EOL_CAMERA_INVALID_IMAGE;
    return;
  }

  /*
  // if (!supported_cam_directions_.count(name2direction(cam_name)))
  // {
  //     LOG(FATAL) << cam_name << " direction not supported!" << std::endl;
  //     sensor_calibration_result.calib_error_code = common::CalibrationErrorCode::EOL_CAMERA_INVALID_PARAMETER;
  //     return;
  // }
  */

  if (!set_camera_model(cam_name, IntrinsicParam, img.cols, img.rows)) {
    LOG(FATAL) << cam_name << " set camera model fail" << std::endl;

    sensor_calibration_result.calib_error_code = common::CalibrationErrorCode::EOL_CAMERA_INVALID_PARAMETER;
    return;
  }
}

bool ExtrinsicEstimator::Solve() {
  // BA
  problem_.reset(new ceres::Problem());

  for (auto it = camNames_.begin(); it != camNames_.end(); ++it) {
    std::string this_cam_name = *it;

    if (sensors_calibration_result_.find(this_cam_name) == sensors_calibration_result_.end()) continue;
    CalibrationResult& this_sensor_calibration_result = sensors_calibration_result_.at(this_cam_name);

    if (this_sensor_calibration_result.calib_error_code != common::CalibrationErrorCode::EOL_CAMERA_NO_CALIBRATION) {
      continue;
    }

    // reprojection error
    auto& wPts = wPts_.at(this_cam_name);
    auto& rayPts = rayPts_.at(this_cam_name);
    for (int i = 0; i < static_cast<int>(wPts.size()); ++i) {
      for (int j = 0; j < 4; ++j) {
        ceres::CostFunction* cost = ImageCostFunctor::create(wPts[i][j], rayPts[i][j]);
        problem_->AddResidualBlock(cost, nullptr, Ci_tf_W_.at(this_cam_name).r_vec, Ci_tf_W_.at(this_cam_name).t_vec);
      }
    }

    if (structure_param_map_t_.find(this_cam_name) == structure_param_map_t_.end()) {
      LOG(ERROR) << "no structure param for " << this_cam_name << " for cost build!" << std::endl;
      continue;
    }

    // // structure prior
    Eigen::Vector3d rfu_tf_cam_structure_t = structure_param_map_t_.at(this_cam_name);
    ceres::CostFunction* cost = TranslationPriorFunctor::create(
        rfu_tf_cam_structure_t, eol_config_.ceres_cfg.at("structure_translation_prior_weight_x"),
        eol_config_.ceres_cfg.at("structure_translation_prior_weight_y"),
        eol_config_.ceres_cfg.at("structure_translation_prior_weight_z"));
    problem_->AddResidualBlock(cost, nullptr, Ci_tf_W_.at(this_cam_name).r_vec, Ci_tf_W_.at(this_cam_name).t_vec);
  }

  // Configure solver options
  ceres::Solver::Options options;
  options.minimizer_type = ceres::TRUST_REGION;
  options.linear_solver_type = ceres::SPARSE_NORMAL_CHOLESKY;
  options.min_relative_decrease = 1e-6;
  options.trust_region_strategy_type = ceres::LEVENBERG_MARQUARDT;  // Optimization Methods
  options.minimizer_progress_to_stdout = true;
  // options.num_threads = num_threads;
  options.max_num_iterations = 100;

  ceres::Solver::Summary summary;
  time_t tstart, tend;
  tstart = time(0);
  ceres::Solve(options, problem_.get(), &summary);
  tend = time(0);

  // if (std::difftime(tend, tstart) > 20)
  // {
  //     for (auto it = sensors_calibration_result_.begin(); it != sensors_calibration_result_.end(); ++it)
  //     {
  //         it->second.calib_error_code = common::CalibrationErrorCode::EOL_CAMERA_TIME_OUT;
  //     }
  // }

  // assign error code
  for (auto it = sensors_calibration_result_.begin(); it != sensors_calibration_result_.end(); ++it) {
    // already have error code
    if (it->second.calib_error_code != common::CalibrationErrorCode::EOL_CAMERA_NO_CALIBRATION) continue;

    // compare diff on rfu coordinate
    // auto t_calibed = ego_tf_rfu_ * get_w_tf_c_t(it->first);
    auto t_calibed = get_w_tf_c_t(it->first);
    auto t_structure = structure_param_map_t_.at(it->first);
    // LOG(INFO) << it->first << " diff xyz(cm): " << (t_calibed[0] - t_structure[0]) * 100 << " "
    //           << (t_calibed[1] - t_structure[1]) * 100 << " " << (t_calibed[2] - t_structure[2]) * 100 << std::endl;

    // get extrinsic (cam_tf_rfu \ rfu_tf_cam)
    Eigen::Quaterniond calib_q = get_c_tf_w_q(it->first);
    // Eigen::Quaterniond calib_q = get_w_tf_c_q(it->first);
    sensors_calibration_result_.at(it->first).calib_extrinsic.orientation.w = calib_q.w();
    sensors_calibration_result_.at(it->first).calib_extrinsic.orientation.x = calib_q.x();
    sensors_calibration_result_.at(it->first).calib_extrinsic.orientation.y = calib_q.y();
    sensors_calibration_result_.at(it->first).calib_extrinsic.orientation.z = calib_q.z();

    Eigen::Vector3d calib_t = get_c_tf_w_t(it->first);
    // Eigen::Vector3d calib_t = get_w_tf_c_t(it->first);
    sensors_calibration_result_.at(it->first).calib_extrinsic.position.x = calib_t.x();
    sensors_calibration_result_.at(it->first).calib_extrinsic.position.y = calib_t.y();
    sensors_calibration_result_.at(it->first).calib_extrinsic.position.z = calib_t.z();

    // // roll pitch yaw, same order with calibresult
    // Eigen::Vector3d calib_rpy = calib_q.toRotationMatrix().eulerAngles(0, 1, 2);
    // sensors_calibration_result_.at(it->first).calib_extrinsic.rotation_angle.roll = calib_rpy.x() / M_PI * 180;
    // sensors_calibration_result_.at(it->first).calib_extrinsic.rotation_angle.pitch = calib_rpy.y() / M_PI * 180;
    // sensors_calibration_result_.at(it->first).calib_extrinsic.rotation_angle.yaw = calib_rpy.z() / M_PI * 180;

    // compare result with structure result
    Eigen::Vector3d calib_ypr = R2ypr(get_w_tf_c_q(it->first));
    Eigen::Vector3d prior_ypr = R2ypr(prior_q_.at(it->first).inverse());

    Eigen::Vector3d angle_diff = R2ypr(get_w_tf_c_q(it->first) * prior_q_.at(it->first));
    angle_diff = angle_diff.cwiseAbs();

    double angle_diff_threshold = eol_config_.ceres_cfg.at("report_angle_diff_threshold");
    double trans_diff_threshold = eol_config_.ceres_cfg.at("report_trans_diff_threshold");
    double reproj_error_threshold = eol_config_.reproj_error_threshold[it->first];

    if (angle_diff[0] > angle_diff_threshold) {  // pitch
      LOG(ERROR) << it->first << " angle_diff[0] > angle_diff_threshold : (" << angle_diff[0] << " > "
                 << angle_diff_threshold << ")" << std::endl;
      // LOG(ERROR) << "*************** Need to open the following line to continue ***************";
      it->second.calib_error_code = common::CalibrationErrorCode::EOL_CAMERA_PITCH_ANGLE_OUT_OF_RANGE;
      continue;
    }
    if (angle_diff[1] > angle_diff_threshold) {  // yaw
      LOG(ERROR) << it->first << " angle_diff[1] > angle_diff_threshold : (" << angle_diff[1] << " > "
                 << angle_diff_threshold << ")" << std::endl;
      // LOG(ERROR) << "*************** Need to open the following line to continue ***************";
      it->second.calib_error_code = common::CalibrationErrorCode::EOL_CAMERA_YAW_ANGLE_OUT_OF_RANGE;
      continue;
    }
    if (angle_diff[2] > angle_diff_threshold) {  // roll
      LOG(ERROR) << it->first << " angle_diff[2] > angle_diff_threshold : (" << angle_diff[2] << " > "
                 << angle_diff_threshold << ")" << std::endl;
      // LOG(ERROR) << "*************** Need to open the following line to continue ***************";
      it->second.calib_error_code = common::CalibrationErrorCode::EOL_CAMERA_ROLL_ANGLE_OUT_OF_RANGE;
      continue;
    }
    LOG(INFO) << it->first << " diff ypr(°): " << angle_diff[0] << " " << angle_diff[1] << " " << angle_diff[2]
              << std::endl;

    Eigen::Vector3d trans_diff = t_calibed - t_structure;
    trans_diff = trans_diff.cwiseAbs();
    if (trans_diff[0] > trans_diff_threshold || trans_diff[1] > trans_diff_threshold ||
        trans_diff[2] > trans_diff_threshold) {  // m
      LOG(ERROR) << it->first << " trans diff > trans_diff_threshold : (" << trans_diff[0] << " " << trans_diff[1]
                 << " " << trans_diff[2] << ") > " << trans_diff_threshold << "m" << std::endl;
      // LOG(ERROR) << "*************** Need to open the following line to continue ***************";
      it->second.calib_error_code = common::CalibrationErrorCode::EOL_CAMERA_CAMERA_POSITION_NOT_CORRECT;
      continue;
    }
    LOG(INFO) << it->first << " diff trans xyz(cm): " << trans_diff[0] * 100 << " " << trans_diff[1] * 100 << " "
              << trans_diff[2] * 100 << std::endl;

    // reproj error check

    double reproj_error = cal_reproj_error(it->first);
    it->second.custom_fields["camera_calib_reproj_error"] = reproj_error;

    if (reproj_error > reproj_error_threshold) {  // pixel
      LOG(ERROR) << it->first << " reproj error > reproj_error_threshold : (" << reproj_error << " > "
                 << reproj_error_threshold << "pixel)" << std::endl;
      // LOG(ERROR) << "*************** Need to open the following line to continue ***************";
      it->second.calib_error_code = common::CalibrationErrorCode::EOL_CAMERA_REPROJECTION_OUT_OF_RANGE;
      continue;
    }

    // no error code for this sensor
    if (it->second.calib_error_code == common::CalibrationErrorCode::EOL_CAMERA_NO_CALIBRATION) {
      it->second.calib_error_code = common::CalibrationErrorCode::EOL_CAMERA_NO_ERROR;
      it->second.calib_status = common::CalibrationStatus::SUCCESS;
    }
  }

  return true;
}

void ExtrinsicEstimator::save_BA(const std::string& cam_name, const std::vector<std::vector<cv::Point3f>>& ray_pts,
                                 const std::vector<std::vector<cv::Point2f>>& img_pts,
                                 const std::vector<std::vector<cv::Point3f>>& w_pts, const cv::Mat& img,
                                 const AxisAngle& cam_tf_rfu,
                                 const std::vector<std::vector<std::string>>& json_key_names) {
  camNames_.insert(cam_name);
  rayPts_.insert(std::make_pair(cam_name, ray_pts));
  imgPts_.insert(std::make_pair(cam_name, img_pts));
  wPts_.insert(std::make_pair(cam_name, w_pts));
  imgs_.insert(std::make_pair(cam_name, img));
  Ci_tf_W_.insert(std::make_pair(cam_name, cam_tf_rfu));
  json_key_names_.insert(std::make_pair(cam_name, json_key_names));
}

Eigen::Quaterniond ExtrinsicEstimator::get_c_tf_w_q(const std::string& cam_name) {
  if (Ci_tf_W_.find(cam_name) == Ci_tf_W_.end()) return Eigen::Quaterniond::Identity();

  AxisAngle a_a = Ci_tf_W_.at(cam_name);
  double angle = sqrt(pow(a_a.r_vec[0], 2) + pow(a_a.r_vec[1], 2) + pow(a_a.r_vec[2], 2));
  Eigen::Vector3d axis(a_a.r_vec[0] / angle, a_a.r_vec[1] / angle, a_a.r_vec[2] / angle);
  Eigen::Quaterniond q(Eigen::AngleAxisd(angle, axis));
  return q;
}

Eigen::Vector3d ExtrinsicEstimator::get_c_tf_w_t(const std::string& cam_name) {
  if (Ci_tf_W_.find(cam_name) == Ci_tf_W_.end()) return Eigen::Vector3d::Zero();

  AxisAngle a_a = Ci_tf_W_.at(cam_name);
  Eigen::Vector3d t(a_a.t_vec[0], a_a.t_vec[1], a_a.t_vec[2]);
  return t;
}

Eigen::Quaterniond ExtrinsicEstimator::get_w_tf_c_q(const std::string& cam_name) {
  return get_c_tf_w_q(cam_name).inverse();
}

Eigen::Vector3d ExtrinsicEstimator::get_w_tf_c_t(const std::string& cam_name) {
  return get_c_tf_w_q(cam_name).inverse() * -get_c_tf_w_t(cam_name);
}

/*
std::string ExtrinsicEstimator::name2direction(const std::string& cam_name)
{

    // cam_front_left_120 -> front_left
    // cam_back_100 -> back

    std::istringstream stream(cam_name);
    std::string tok;
    std::vector<std::string> toks;
    while (std::getline(stream, tok, '_'))
    {
        toks.push_back(tok);
    }
    if (toks.size() > 0 && toks.at(0) != "cam")
    {
        LOG(ERROR) << cam_name << " doesn't start with cam!" << std::endl;
    }
    std::string direction;
    for (int i = 1; i < toks.size() - 1; ++i)
    {
        direction += toks.at(i);
        if (i != toks.size() - 2)
            direction += "_";
    }
    return direction;
}

*/

void ExtrinsicEstimator::save_result(std::map<std::string, CalibrationResult>& calibration_results) {
  std::map<std::string, CalibrationResult> failed_res;
  for (auto it = sensors_calibration_result_.begin(); it != sensors_calibration_result_.end(); it++) {
    calibration_results.insert(std::make_pair(it->first, it->second));
    if (it->second.calib_error_code != CalibrationErrorCode::EOL_CAMERA_NO_ERROR) {
      calibration_results.at(it->first).calib_status = CalibrationStatus::FAILED;
      failed_res.insert(std::make_pair(it->first, it->second));
    } else {
      calibration_results.at(it->first).calib_status = CalibrationStatus::SUCCESS;
    }
  }

  LOG(INFO) << std::endl;
  if (failed_res.size() > 0) {
    LOG(ERROR) << "************************** calibration failed list **************************" << std::endl;
    LOG(ERROR) << std::endl;
    for (auto it = failed_res.begin(); it != failed_res.end(); it++) {
      Eigen::Quaterniond rfu_tf_cam_q =
          Eigen::Quaterniond(it->second.calib_extrinsic.orientation.w, it->second.calib_extrinsic.orientation.x,
                             it->second.calib_extrinsic.orientation.y, it->second.calib_extrinsic.orientation.z);
      Eigen::Vector3d calib_rpy = rfu_tf_cam_q.toRotationMatrix().eulerAngles(2, 1, 0);
      LOG(ERROR) << "calibration error for " << it->first << ": ";
      LOG(ERROR) << "    calib_status       : " << static_cast<uint32_t>(it->second.calib_status) << std::endl;
      LOG(ERROR) << "    error_code         : " << static_cast<uint32_t>(it->second.calib_error_code) << std::endl;
      LOG(ERROR) << "    reproj_error       : "
                 << std::get<double>(it->second.custom_fields.at("camera_calib_reproj_error")) << std::endl;
      LOG(ERROR) << "    POSITION           : " << it->second.calib_extrinsic.position.x << " "
                 << it->second.calib_extrinsic.position.y << " " << it->second.calib_extrinsic.position.z << std::endl;
      LOG(ERROR) << "    ORIENTATION        : " << it->second.calib_extrinsic.orientation.w << " "
                 << it->second.calib_extrinsic.orientation.x << " " << it->second.calib_extrinsic.orientation.y << " "
                 << it->second.calib_extrinsic.orientation.z << std::endl;
      LOG(ERROR) << "    ROTATION ANGLE(XYZ): " << calib_rpy[2] / M_PI * 180 << " " << calib_rpy[1] / M_PI * 180 << " "
                 << calib_rpy[0] / M_PI * 180 << std::endl;
    }
    LOG(ERROR) << std::endl;
    LOG(ERROR) << "============================== some sensors calibration failed! =============================="
               << std::endl;

  } else {
    LOG(INFO) << "************************ all sensors calibration success! *************************" << std::endl;
  }
  LOG(INFO) << std::endl;

  /* For E171
  calibration_output.sensors_calibration_result = sensors_calibration_result_;
  calibration_output.calibration_error_code = common::CalibrationErrorCode::EOL_CAMERA_NO_ERROR;
  for (auto it = sensors_calibration_result_.begin(); it != sensors_calibration_result_.end(); it++)
  {
      if (it->second.calib_error_code != common::CalibrationErrorCode::EOL_CAMERA_NO_ERROR)
      {
          calibration_output.calibration_error_code =
  common::CalibrationErrorCode::EOL_CAMERA_COMMON_INTERNAL_CALIBRATION_ERROR; break;
      }
  }
  */
}

CalibrationErrorCode ExtrinsicEstimator::get_calib_error_code(const std::string& cam_name) {
  CalibrationErrorCode error_code;
  if (sensors_calibration_result_.find(cam_name) == sensors_calibration_result_.end()) {
    error_code = CalibrationErrorCode::EOL_CAMERA_COMMON_INTERNAL_CALIBRATION_ERROR;
  } else {
    error_code = sensors_calibration_result_.at(cam_name).calib_error_code;
  }

  return error_code;
}

}  // namespace calibration_algo::eol::camera