#include "calibration_algo/eol/camera/sensor_production_calibration.h"

using namespace calibration_algo::common;

namespace calibration_algo::eol::camera {

SensorProductionCalibration::CalibrationParam::CalibrationParam(
    CalibrationType calibration_type, const std::string& log_output_path,
    const std::map<std::string, CameraIntrinsicParam>& cameras_intrinsic,
    const std::map<std::string, ExtrinsicPose>& cameras_structure)
    : calibration_type_(calibration_type),
      log_output_path_(log_output_path),
      cameras_intrinsic_(cameras_intrinsic),
      cameras_structure_(cameras_structure) {}

SensorProductionCalibration::SensorInput::SensorInput(CalibrationType calibration_type, const uint64_t timestamp)
    : calibration_type_(calibration_type), timestamp_(timestamp) {}

SensorProductionCalibration::SensorProductionCalibration() {}

SensorProductionCalibration::~SensorProductionCalibration() {}

SensorProductionCalibration::CalibrationOutput::CalibrationOutput(CalibrationType calibration_type) {
  calibration_type_ = calibration_type;
}

}  // namespace calibration_algo::eol::camera
