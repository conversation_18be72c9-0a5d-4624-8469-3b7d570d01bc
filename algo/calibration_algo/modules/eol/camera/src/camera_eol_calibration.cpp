#include "calibration_algo/eol/camera/camera_eol_calibration.h"

#include <filesystem>

#include "calibration_algo/eol/camera/error_code.hpp"

using namespace calibration_algo::common;

namespace calibration_algo::eol::camera {

CameraEOLCalibration::CalibrationParam::CalibrationParam(
    CalibrationType calibraiton_type, const std::string &log_output_path,
    const std::map<std::string, CameraIntrinsicParam> &cameras_intrinsic,
    const std::map<std::string, ExtrinsicPose> &cameras_structure, const EOLParams &eol_config,
    const std::map<std::string, Point> &calibroom_points_map)
    : SensorProductionCalibration::CalibrationParam(calibraiton_type, log_output_path, cameras_intrinsic,
                                                    cameras_structure) {
  calibroom_points_map_ = calibroom_points_map;
  eol_config_ = eol_config;
}

CameraEOLCalibration::SensorInput::SensorInput(CalibrationType calibraiton_type, const uint64_t timestamp,
                                               const cv::Mat &image)
    : SensorProductionCalibration::SensorInput(calibraiton_type, timestamp) {
  image_ = image.clone();
}

CameraEOLCalibration::CameraEOLCalibration() { extrinsic_estimator_.reset(new ExtrinsicEstimator()); }
CameraEOLCalibration::~CameraEOLCalibration() {}

int CameraEOLCalibration::Init(const CalibrationParam &calibration_param) {
  calibration_param_ = calibration_param;
  // FLAGS_log_dir = calibration_param_.GetLogOutputPath();
  // FLAGS_alsologtostderr = true;
  // FLAGS_stop_logging_if_full_disk = true;
  // AprilTags::TagCodes m_tagCodes = AprilTags::tagCodes16h5;

  extrinsic_estimator_->Init(calibration_param_.GetEOLParam(), calibration_param_.GetCalibroomPointsMap(),
                             calibration_param_.GetCalibrationType());
  return -1;
}

int CameraEOLCalibration::CameraEOLExtrinsicCalibration(const std::map<std::string, SensorInput> &sensors_input,
                                                        std::map<std::string, CalibrationResult> &calibration_results) {
  std::chrono::steady_clock::time_point begin, end, rT0, rT1, rT2, rT3, rT4, rT5, rT6, rT7;
  rT0 = std::chrono::steady_clock::now();
  begin = rT0;
  extrinsic_estimator_->reset();
  std::vector<cv::Mat> imgs_to_detect;
  std::vector<std::string> imgs_to_detect_cam_name;

  // ================== step 1. feed img to extrinsic estimator ==================

  // Check if the number of sensors_input matches the prior in eol_config
  if (sensors_input.size() != calibration_param_.GetEOLParam().prior_q.size()) {
    LOG(FATAL) << "The number of sensors_input does not match the prior in eol_config. : " << sensors_input.size()
               << " vs " << calibration_param_.GetEOLParam().prior_q.size() << std::endl;

    // return static_cast<int>(common::CalibrationErrorCode::SENSOR_COUNT_MISMATCH);
    exit(-1);
  }

  for (auto &kv : sensors_input) {
    std::string cam_name = kv.first;
    auto sensor_input = kv.second;
    cv::Mat img = sensor_input.GetImage();

    if (calibration_param_.GetCamerasIntrinsic().find(cam_name) == calibration_param_.GetCamerasIntrinsic().end()) {
      continue;
    }
    if (calibration_param_.GetCameraStructureParameters().find(cam_name) ==
        calibration_param_.GetCameraStructureParameters().end()) {
      continue;
    }
    auto intrinsic_param = calibration_param_.GetCamerasIntrinsic().at(cam_name);
    auto structure_param = calibration_param_.GetCameraStructureParameters().at(cam_name);
    extrinsic_estimator_->feed_img(img, cam_name, intrinsic_param, structure_param);
    // no error during feed img
    if (extrinsic_estimator_->get_calib_error_code(cam_name) !=
        common::CalibrationErrorCode::EOL_CAMERA_NO_CALIBRATION) {
      continue;
    }

    // save img for later parallel apriltag detection
    imgs_to_detect_cam_name.push_back(cam_name);
    imgs_to_detect.push_back(img);

    // Draw original image for debug
    if (calibration_param_.GetEOLParam().is_debug) {
      LOG(INFO) << "save original image for debug" << std::endl;
      std::string this_output_dir = calibration_param_.GetEOLParam().tmp_output_dir;
      std::string output_path = this_output_dir + cam_name + ".png";
      if (!std::filesystem::exists(this_output_dir)) {
        std::filesystem::create_directory(this_output_dir);
      }
      cv::imwrite(output_path, img);
    }
  }

  // ============================ step 2. detect apriltags ============================
  rT1 = std::chrono::steady_clock::now();
  std::map<std::string, std::vector<AprilTags::TagDetection>> detections_map;

#pragma omp parallel for  // parallel apriltag detections
  for (int i = 0; i < static_cast<int>(imgs_to_detect.size()); ++i) {
    auto detections = extrinsic_estimator_->detect_apriltags(imgs_to_detect_cam_name[i], imgs_to_detect[i]);

    if (calibration_param_.GetEOLParam().is_simulation_error) {
      extrinsic_estimator_->add_AprilTagDetections_offset(
          detections, calibration_param_.GetEOLParam().simulation_error_config.img_detect_points_offset);
    }

    // random mask simulation
    if (calibration_param_.GetEOLParam().is_simulation_error) {
      std::vector<int> random_mask_AprilTag_lst =
          calibration_param_.GetEOLParam().simulation_error_config.random_mask_AprilTag.at(imgs_to_detect_cam_name[i]);
      if (random_mask_AprilTag_lst.size() > 0) {
        // random choose one tag to mask
        int random_index = random_mask_AprilTag_lst[rand() % random_mask_AprilTag_lst.size()];
        LOG(ERROR) << "****** SIMULATION ERRPR ****** random mask AprilTag: " << imgs_to_detect_cam_name[i] << " "
                   << random_index << std::endl;
        detections.erase(std::remove_if(detections.begin(), detections.end(),
                                        [random_index](const AprilTags::TagDetection &detection) {
                                          return detection.id == random_index;
                                        }),
                         detections.end());
      }
    }

    /*
    // // test(cam_front_30_tf_lidar): Filter detections with AprilTag ID 3 for Z10
    if (imgs_to_detect_cam_name[i] == "cam_front_30")
    {
      AprilTags::TagDetection fake_detection;
      fake_detection.id = 3;
      fake_detection.p[0] = {358.0, 772.0};
      fake_detection.p[1] = {508.0, 775.0};
      fake_detection.p[2] = {512.0, 622.0};
      fake_detection.p[3] = {365.0, 620.0}; // Example corner points
      // Calculate the center point of the four corners
      double center_x = 0.0, center_y = 0.0;
      for (const auto &corner : fake_detection.p)
      {
        center_x += corner.first;
        center_y += corner.second;
      }
      center_x /= 4.0;
      center_y /= 4.0;
      fake_detection.cxy = {center_x, center_y};
      detections.push_back(fake_detection);

      detections.erase(
          std::remove_if(detections.begin(), detections.end(),
            [](const AprilTags::TagDetection &detection) {
              return detection.id == 3 || detection.id == 2;
            }),
          detections.end());

      // modify detections
      // for (auto &detection : detections) {
      //   if (detection.id == 0) {
      //     detection.p[0].second += 5.0;
      //     detection.p[1].second += 5.0;
      //     detection.p[2].second -= 5.0;
      //     detection.p[3].second -= 5.0;

      //   }
      // }
    }
*/

#pragma omp critical  // keep safe when write to shared data
    { detections_map.insert(std::make_pair(imgs_to_detect_cam_name[i], detections)); }

    if (calibration_param_.GetEOLParam().is_debug) {
      LOG(ERROR) << "plot AprilTags on the image for debug";
      cv::Mat img = imgs_to_detect[i];
      for (const auto &detection : detections) {
        detection.draw(img, cv::Scalar(0, 0, 255), 3);
      }
      std::string this_output_dir = calibration_param_.GetEOLParam().tmp_output_dir;
      std::string output_path = this_output_dir + imgs_to_detect_cam_name[i] + "_detected.jpg";
      if (!std::filesystem::exists(this_output_dir)) {
        std::filesystem::create_directory(this_output_dir);
      }

      if (img.rows > 1080 && img.cols > 1920) {
        cv::resize(img, img, cv::Size(img.cols / 2, img.rows / 2));
      }
      cv::imwrite(output_path, img);
    }
  }
  rT2 = std::chrono::steady_clock::now();

  /*
    if (imgs_to_detect.size() > 3)
    {
        #pragma omp parallel for // parallel apriltag detections
        for (int i = 0; i < imgs_to_detect.size(); ++i)
        {
          auto detections = extrinsic_estimator_->detect_apriltags(imgs_to_detect_cam_name[i], imgs_to_detect[i]);
          #pragma omp critical { // keep safe when write to shared data
            detections_map.insert(std::make_pair(imgs_to_detect_cam_name[i], detections));
          }

          // Draw detected AprilTags on the image for visualization
          cv::Mat img = imgs_to_detect[i].clone();
          for (const auto& detection : detections)
          {
            detection.draw(img, cv::Scalar(0, 0, 255), 5);
          }
          std::string output_path = "./output/" + imgs_to_detect_cam_name[i] + "_detected.png";
          if (!std::filesystem::exists("./output")) {
            std::filesystem::create_directory("./output");
          }
          cv::imwrite(output_path, img);

        }
        rT2_1 = std::chrono::steady_clock::now();
        rT2_2 = rT2_1;


    // // parallel apriltag detections
    //   #pragma omp parallel for
    //     for (int i = 0; i < 3; ++i)
    //     {
    //       auto detections = extrinsic_estimator_->detect_apriltags(imgs_to_detect_cam_name[i], imgs_to_detect[i]);

    //       // Draw detected AprilTags on the image for visualization
    //       cv::Mat img = imgs_to_detect[i].clone();
    //       for (const auto& detection : detections)
    //       {
    //           detection.draw(img, cv::Scalar(0, 0, 255), 5);
    //       }
    //       std::string output_path = "./output/" + imgs_to_detect_cam_name[i] + "_detected.png";
    //       if (!std::filesystem::exists("./output")) {
    //           std::filesystem::create_directory("./output");
    //       }

    //       cv::imwrite(output_path, img);

    //       #pragma omp critical
    //       {
    //           detections_map.insert(std::make_pair(imgs_to_detect_cam_name[i], detections));
    //       }
    //     }
    //   rT2_1 = std::chrono::steady_clock::now();

    //   #pragma omp parallel for
    //     for (int i = 3; i < imgs_to_detect.size(); ++i)
    //     {
    //       auto detections = extrinsic_estimator_->detect_apriltags(imgs_to_detect_cam_name[i], imgs_to_detect[i]);
    //       #pragma omp critical
    //       {
    //           detections_map.insert(std::make_pair(imgs_to_detect_cam_name[i], detections));
    //       }
    //     }
    //   rT2_2 = std::chrono::steady_clock::now();


    }
    else
    {

      // #pragma omp parallel for
      //   for (int i = 0; i < imgs_to_detect.size(); ++i)
      //   {
      //     auto detections = extrinsic_estimator_->detect_apriltags(imgs_to_detect_cam_name[i], imgs_to_detect[i]);
      //     #pragma omp critical
      //     {
      //         detections_map.insert(std::make_pair(imgs_to_detect_cam_name[i], detections));
      //     }
      //   }
      //   rT2_1 = std::chrono::steady_clock::now();
      //   rT2_2 = rT2_1;
    }
    */

  // ============================ step 3. solve pnp ============================
  for (int i = 0; i < static_cast<int>(imgs_to_detect_cam_name.size()); ++i) {
    auto cam_name = imgs_to_detect_cam_name[i];
    auto img = imgs_to_detect[i];

    std::vector<std::vector<cv::Point3f>> w_pts;
    std::vector<std::vector<cv::Point2f>> img_pts;
    std::vector<std::vector<std::string>> json_key_names;
    if (!extrinsic_estimator_->get_w_pts(cam_name, img, w_pts, img_pts, json_key_names, detections_map.at(cam_name))) {
      continue;
    }

    // LOG(INFO) << "cam_name: " << cam_name << " w_pts.size(): " << w_pts.size() << " img_pts.size(): " <<
    // img_pts.size(); for (size_t j = 0; j < w_pts.size(); ++j)
    // {
    //   LOG(INFO) << j;
    //   for (size_t k = 0; k < w_pts[j].size(); ++k)
    //   {
    //     LOG(INFO) << "w_pts: " << w_pts[j][k] << " img_pts: " << img_pts[j][k];
    //   }
    // }

    extrinsic_estimator_->filter_tags_with_prior(cam_name, img, w_pts, img_pts, json_key_names,
                                                 detections_map.at(cam_name));

    // LOG(INFO) << "Finish filter tags with prior for " << cam_name;

    // no error
    if (extrinsic_estimator_->get_calib_error_code(cam_name) !=
        common::CalibrationErrorCode::EOL_CAMERA_NO_CALIBRATION) {
      continue;
    }

    // LOG(INFO) << "Finish get_calib_error_code for " << cam_name;

    if (!extrinsic_estimator_->check_tags_integrity(cam_name, img, detections_map.at(cam_name))) continue;

    // LOG(INFO) << "Start to solve pnp for " << cam_name;

    // then solve for pnp
    std::vector<std::vector<cv::Point3f>> ray_pts;       // on camera coordinate
    ExtrinsicEstimator::AxisAngle cam_tf_rfu_axisAngle;  // camera coordinate to rfu coordinate
    extrinsic_estimator_->solve_pnp(cam_name, w_pts, img_pts, ray_pts, cam_tf_rfu_axisAngle);

    // no error during solve_pnp
    if (extrinsic_estimator_->get_calib_error_code(cam_name) !=
        common::CalibrationErrorCode::EOL_CAMERA_NO_CALIBRATION) {
      continue;
    }

    // then save this sensor's result for later BA
    extrinsic_estimator_->save_BA(cam_name, ray_pts, img_pts, w_pts, img, cam_tf_rfu_axisAngle, json_key_names);

    cv::Mat img_plot;
    // plot pnp reproj error
    if (calibration_param_.GetEOLParam().is_debug) {
      img_plot = img.clone();
    }

    if (w_pts.size() != img_pts.size()) {
      LOG(ERROR) << "Mismatch between world points and image points size for camera: " << cam_name;
      continue;
    }

    double total_reproj_error = 0.0;
    int total_points = 0;

    // cam tf rfu
    Eigen::Quaterniond cam_tf_rfu_q;
    Eigen::Vector3d cam_tf_rfu_t;
    cam_tf_rfu_q = extrinsic_estimator_->get_c_tf_w_q(cam_name);
    cam_tf_rfu_t = extrinsic_estimator_->get_c_tf_w_t(cam_name);

    // get cam
    auto this_cam = extrinsic_estimator_->get_cam(cam_name);

    for (int i = 0; i < static_cast<int>(w_pts.size()); ++i) {
      for (int j = 0; j < static_cast<int>(w_pts[i].size()); ++j) {
        Eigen::Vector3d cam_pt =
            cam_tf_rfu_q * Eigen::Vector3d(w_pts[i][j].x, w_pts[i][j].y, w_pts[i][j].z) + cam_tf_rfu_t;
        Eigen::Vector2d img_pt_proj;
        this_cam->spaceToPlane(cam_pt, img_pt_proj);

        cv::Point2f detected_pt = img_pts[i][j];
        cv::Point2f projected_pt(img_pt_proj[0], img_pt_proj[1]);

        double error = cv::norm(detected_pt - projected_pt);
        total_reproj_error += error;
        total_points++;

        if (calibration_param_.GetEOLParam().is_debug && !img_plot.empty()) {
          cv::circle(img_plot, detected_pt, 8, cv::Scalar(255, 0, 0), 4);   // blue for detected pt
          cv::circle(img_plot, projected_pt, 8, cv::Scalar(0, 0, 255), 4);  // red for projected pt

          // plot j
          // cv::putText(img_plot, std::to_string(j), detected_pt, cv::FONT_HERSHEY_SIMPLEX, 1, cv::Scalar(255, 0, 0),
          // 2); cv::putText(img_plot, std::to_string(j), projected_pt, cv::FONT_HERSHEY_SIMPLEX, 1, cv::Scalar(0, 0,
          // 255), 2);

          // plot reprojection error
          cv::putText(img_plot, std::to_string(error), (detected_pt + projected_pt) / 2, cv::FONT_HERSHEY_SIMPLEX, 1,
                      cv::Scalar(0, 255, 0), 2);
        }
      }
    }

    double mean_reproj_error = total_reproj_error / total_points;
    LOG(INFO) << "Mean PNP reprojection error for camera " << cam_name << ": " << mean_reproj_error << " pixels";

    if (calibration_param_.GetEOLParam().is_debug && !img_plot.empty()) {
      LOG(ERROR) << "plot reproj_pnp for debug" << std::endl;
      std::string this_output_dir = calibration_param_.GetEOLParam().tmp_output_dir;
      std::string output_path = this_output_dir;
      output_path.append("reproj_pnp_").append(cam_name).append(".jpg");
      if (!std::filesystem::exists(this_output_dir)) {
        std::filesystem::create_directory(this_output_dir);
      }
      if (img_plot.rows > 1080 && img_plot.cols > 1920) {
        cv::resize(img_plot, img_plot, cv::Size(img_plot.cols / 2, img_plot.rows / 2));
      }
      cv::imwrite(output_path, img_plot);
    }

    /*
    for (int i = 0; i < img_pts.size(); ++i)
    {
        for (int j = 0; j < 4; ++j)
        {
            cv::circle(img_plot, img_pts[i][j], 8, cv::Scalar(255, 0, 0), 4); // blue for detected pt
            cv::putText(img_plot, std::to_string(j), img_pts[i][j], cv::FONT_HERSHEY_SIMPLEX, 1, cv::Scalar(255, 0, 0),
    2);
        }
    }

    // LOG(INFO) << "ci_tf_w.r_vec: " << ci_tf_w.r_vec[0] << ", " << ci_tf_w.r_vec[1] << ", " << ci_tf_w.r_vec[2];
    // LOG(INFO) << "ci_tf_w.t_vec: " << ci_tf_w.t_vec[0] << ", " << ci_tf_w.t_vec[1] << ", " << ci_tf_w.t_vec[2];


    Eigen::Quaterniond cam_tf_rfu_q;
    Eigen::Vector3d cam_tf_rfu_t;

    cam_tf_rfu_q = extrinsic_estimator_->get_c_tf_w_q(cam_name);
    cam_tf_rfu_t = extrinsic_estimator_->get_c_tf_w_t(cam_name);

    // get cam
    auto this_cam = extrinsic_estimator_->get_cam(cam_name);

    for (int i = 0; i < w_pts.size(); ++i) // points on rfu coordinate
    {
        for (int j = 0; j < 4; ++j)
        {
            // transform w_pts to img_pts
            Eigen::Vector3d cam_pt = cam_tf_rfu_q * Eigen::Vector3d(w_pts[i][j].x, w_pts[i][j].y, w_pts[i][j].z) +
    cam_tf_rfu_t; // on camera coordinate system Eigen::Vector2d img_pt_proj;

            this_cam->spaceToPlane(cam_pt, img_pt_proj); // transform from camera coordinate system to image coordinate
    system cv::circle(img_plot, cv::Point2f(img_pt_proj[0], img_pt_proj[1]), 8, cv::Scalar(0, 0, 255), 4); // red for
    proj cv::putText(img_plot, std::to_string(j), cv::Point2f(img_pt_proj[0], img_pt_proj[1]), cv::FONT_HERSHEY_SIMPLEX,
    1, cv::Scalar(0, 0, 255), 2);
        }
    }
    std::string output_path = std::string("./output/") + "reproj_pnp_" + cam_name + ".png";
    if (!std::filesystem::exists("./output")){
        std::filesystem::create_directory("./output");
    }
    cv::imwrite(output_path, img_plot);

    */
  }

  rT3 = std::chrono::steady_clock::now();

  // ============================ step 4. ceres slover ============================
  // ceres solver
  extrinsic_estimator_->Solve();

  // ============================ step 5. print result ============================
  // reproj final res
  for (int i = 0; i < static_cast<int>(imgs_to_detect_cam_name.size()); ++i) {
    auto cam_name = imgs_to_detect_cam_name[i];
    auto img = imgs_to_detect[i];

    std::vector<std::vector<cv::Point3f>> w_pts;
    std::vector<std::vector<cv::Point2f>> img_pts;
    std::vector<std::vector<std::string>> json_key_names;
    if (!extrinsic_estimator_->get_w_pts(cam_name, img, w_pts, img_pts, json_key_names, detections_map.at(cam_name))) {
      continue;
    }

    // plot ceres reproj error
    cv::Mat img_plot;
    if (calibration_param_.GetEOLParam().is_debug) {
      LOG(ERROR) << "plot reproj_ceres for debug" << std::endl;
      img_plot = img.clone();
    }

    if (w_pts.size() != img_pts.size()) {
      LOG(ERROR) << "Mismatch between world points and image points size for camera: " << cam_name;
      continue;
    }

    double total_reproj_error = 0.0;
    int total_points = 0;

    // cam tf rfu
    Eigen::Quaterniond cam_tf_rfu_q;
    Eigen::Vector3d cam_tf_rfu_t;
    cam_tf_rfu_q = extrinsic_estimator_->get_c_tf_w_q(cam_name);
    cam_tf_rfu_t = extrinsic_estimator_->get_c_tf_w_t(cam_name);

    // get cam
    auto this_cam = extrinsic_estimator_->get_cam(cam_name);

    for (int i = 0; i < static_cast<int>(w_pts.size()); ++i) {
      for (int j = 0; j < static_cast<int>(w_pts[i].size()); ++j) {
        Eigen::Vector3d cam_pt =
            cam_tf_rfu_q * Eigen::Vector3d(w_pts[i][j].x, w_pts[i][j].y, w_pts[i][j].z) + cam_tf_rfu_t;
        Eigen::Vector2d img_pt_proj;
        this_cam->spaceToPlane(cam_pt, img_pt_proj);

        cv::Point2f detected_pt = img_pts[i][j];
        cv::Point2f projected_pt(img_pt_proj[0], img_pt_proj[1]);

        double error = cv::norm(detected_pt - projected_pt);
        total_reproj_error += error;
        total_points++;

        if (calibration_param_.GetEOLParam().is_debug && !img_plot.empty()) {
          cv::circle(img_plot, detected_pt, 8, cv::Scalar(255, 0, 0), 4);   // blue for detected pt
          cv::circle(img_plot, projected_pt, 8, cv::Scalar(0, 0, 255), 4);  // red for projected pt

          // plot j
          // cv::putText(img_plot, std::to_string(j), detected_pt, cv::FONT_HERSHEY_SIMPLEX, 1, cv::Scalar(255, 0, 0),
          // 2); cv::putText(img_plot, std::to_string(j), projected_pt, cv::FONT_HERSHEY_SIMPLEX, 1, cv::Scalar(0, 0,
          // 255), 2);

          // plot reprojection error
          cv::putText(img_plot, std::to_string(error), (detected_pt + projected_pt) / 2, cv::FONT_HERSHEY_SIMPLEX, 1,
                      cv::Scalar(0, 255, 0), 2);
        }
      }
    }

    double mean_reproj_error = total_reproj_error / total_points;
    LOG(INFO) << "Mean Ceres reprojection error for camera " << cam_name << ": " << mean_reproj_error << " pixels";

    if (calibration_param_.GetEOLParam().is_debug && !img_plot.empty()) {
      std::string this_output_dir = calibration_param_.GetEOLParam().tmp_output_dir;
      std::string output_path = std::string(this_output_dir) + "reproj_ceres_" + cam_name + ".jpg";
      if (!std::filesystem::exists(this_output_dir)) {
        std::filesystem::create_directory(this_output_dir);
      }
      if (img_plot.rows > 1080 && img_plot.cols > 1920) {
        cv::resize(img_plot, img_plot, cv::Size(img_plot.cols / 2, img_plot.rows / 2));
      }
      cv::imwrite(output_path, img_plot);
    }
  }

  rT4 = std::chrono::steady_clock::now();
  extrinsic_estimator_->printf_result();
  rT5 = std::chrono::steady_clock::now();
  // extrinsic_estimator_->draw_reproj_error(calibration_param_.GetLogOutputPath());
  rT6 = std::chrono::steady_clock::now();
  extrinsic_estimator_->print_epipolar_error();
  rT7 = std::chrono::steady_clock::now();

  /*
    LOG(ERROR) << "[feed img time]: " << std::chrono::duration_cast<std::chrono::microseconds>(rT1 - rT0).count()/1000.0
    << "ms" << std::endl
              << "[parallel tag detection time first three]: " <<
    std::chrono::duration_cast<std::chrono::microseconds>(rT2_1 - rT1).count()/1000.0 << "ms" << std::endl
              << "[parallel tag detection time latter three]: " <<
    std::chrono::duration_cast<std::chrono::microseconds>(rT2_2 - rT2_1).count()/1000.0 << "ms" << std::endl
              << "[pnp time]: " << std::chrono::duration_cast<std::chrono::microseconds>(rT3 - rT2_2).count()/1000.0 <<
    "ms" << std::endl
              << "[ceres time]: " << std::chrono::duration_cast<std::chrono::microseconds>(rT4 - rT3).count()/1000.0 <<
    "ms" << std::endl
              << "[draw reproj time]: " << std::chrono::duration_cast<std::chrono::microseconds>(rT6 -
    rT5).count()/1000.0 << "ms" << std::endl
              << "[cal epipolar time]: " << std::chrono::duration_cast<std::chrono::microseconds>(rT7 -
    rT6).count()/1000.0 << "ms" << std::endl;
  */

  LOG(INFO) << "[feed img time]: " << std::chrono::duration_cast<std::chrono::microseconds>(rT1 - rT0).count() / 1000.0
            << "ms" << std::endl
            << "[parallel tag detection time]: "
            << std::chrono::duration_cast<std::chrono::microseconds>(rT2 - rT1).count() / 1000.0 << "ms" << std::endl
            << "[pnp time]: " << std::chrono::duration_cast<std::chrono::microseconds>(rT3 - rT2).count() / 1000.0
            << "ms" << std::endl
            << "[ceres time]: " << std::chrono::duration_cast<std::chrono::microseconds>(rT4 - rT3).count() / 1000.0
            << "ms" << std::endl
            << "[draw reproj time]: "
            << std::chrono::duration_cast<std::chrono::microseconds>(rT6 - rT5).count() / 1000.0 << "ms" << std::endl
            << "[cal epipolar time]: "
            << std::chrono::duration_cast<std::chrono::microseconds>(rT7 - rT6).count() / 1000.0 << "ms" << std::endl;

  extrinsic_estimator_->save_result(calibration_results);

  end = std::chrono::steady_clock::now();
  LOG(INFO) << "[total time]: "
            << std::chrono::duration_cast<std::chrono::microseconds>(end - begin).count() / 1000000.0 << "s"
            << std::endl;
  return -1;
}

void CameraEOLCalibration::DrawReprojImages(const std::string &output_dir) {
  extrinsic_estimator_->draw_reproj_error(output_dir);
}

int CameraEOLCalibration::RunCalibration(const std::map<std::string, SensorInput> &sensors_input,
                                         std::map<std::string, CalibrationResult> &calibration_results) {
  CameraEOLExtrinsicCalibration(sensors_input, calibration_results);
  return -1;
}
}  // namespace calibration_algo::eol::camera